"""
Poppler 和 Tesseract 配置文件
"""
import os

# Poppler 工具路徑配置
POPPLER_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), "poppler-24.08.0", "Library", "bin")

# Tesseract OCR 路徑配置
# 方法1: 如果安裝到系統
TESSERACT_SYSTEM_PATHS = [
    r"C:\Program Files\Tesseract-OCR\tesseract.exe",
    r"C:\tesseract\tesseract.exe",
    r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe"
]

# 方法2: 如果下載到專案目錄
TESSERACT_LOCAL_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), "tesseract-ocr", "tesseract.exe")

# 檢查 Poppler 是否存在
def check_poppler_installation():
    """檢查 Poppler 是否正確安裝"""
    import os
    
    expected_files = ["pdftoppm.exe", "pdfinfo.exe"]
    
    if not os.path.exists(POPPLER_PATH):
        print(f"❌ Poppler 路徑不存在: {POPPLER_PATH}")
        return False
    
    missing_files = []
    for file in expected_files:
        file_path = os.path.join(POPPLER_PATH, file)
        if not os.path.exists(file_path):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少 Poppler 工具: {missing_files}")
        print(f"請檢查路徑: {POPPLER_PATH}")
        return False
    
    print(f"✅ Poppler 安裝正確，路徑: {POPPLER_PATH}")
    return True

# Tesseract 檢查和配置
def find_tesseract():
    """尋找 Tesseract 安裝位置"""
    # 檢查本地專案目錄
    if os.path.exists(TESSERACT_LOCAL_PATH):
        return TESSERACT_LOCAL_PATH

    # 檢查系統安裝位置
    for path in TESSERACT_SYSTEM_PATHS:
        if os.path.exists(path):
            return path

    return None

def check_tesseract_installation():
    """檢查 Tesseract 是否正確安裝"""
    tesseract_path = find_tesseract()

    if tesseract_path:
        print(f"✅ Tesseract 找到，路徑: {tesseract_path}")
        return True
    else:
        print("❌ 未找到 Tesseract OCR")
        print("請安裝 Tesseract 或將其放置在專案目錄中")
        return False

def setup_tesseract_path():
    """設置 Tesseract 路徑"""
    tesseract_path = find_tesseract()

    if tesseract_path:
        # 設置 pytesseract 使用的 Tesseract 路徑
        try:
            import pytesseract
            pytesseract.pytesseract.tesseract_cmd = tesseract_path
            print(f"✅ 已設置 Tesseract 路徑: {tesseract_path}")
            return True
        except ImportError:
            try:
                import unstructured_pytesseract
                unstructured_pytesseract.pytesseract.tesseract_cmd = tesseract_path
                print(f"✅ 已設置 unstructured_pytesseract 路徑: {tesseract_path}")
                return True
            except ImportError:
                print("⚠️ 無法導入 pytesseract 模組")
                return False

    return False

# 設置環境變數（僅在當前 Python 進程中）
def setup_poppler_path():
    """設置 Poppler 路徑到當前進程的環境變數"""
    if check_poppler_installation():
        current_path = os.environ.get('PATH', '')
        if POPPLER_PATH not in current_path:
            os.environ['PATH'] = POPPLER_PATH + os.pathsep + current_path
            print(f"✅ 已設置 Poppler 路徑到當前進程")
        return True
    return False

def setup_all_tools():
    """設置所有工具路徑"""
    poppler_ok = setup_poppler_path()
    tesseract_ok = setup_tesseract_path()

    if poppler_ok and tesseract_ok:
        print("🎉 所有工具都已正確設置！")
        return True
    elif poppler_ok:
        print("⚠️ Poppler 已設置，但 Tesseract 未找到")
        return False
    else:
        print("❌ 工具設置失敗")
        return False
