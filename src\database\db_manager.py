# Database manager for handling PostgreSQL database operations
import psycopg2
from psycopg2.extras import RealDictCursor
from sqlalchemy import create_engine, text
from typing import List, Dict, Any, Optional
from src.config.settings import DB_HOST, DB_PORT, DB_USER, DB_PASSWORD, DB_NAME

class DBManager:
    """Manages PostgreSQL database operations for the OCR project"""

    def __init__(self):
        self.connection_string = f"postgresql+psycopg2://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
        self.engine = create_engine(self.connection_string)

    def create_tables(self):
        """Create required tables for parent and child documents with vector support"""
        with self.engine.connect() as conn:
            # Enable pgvector extension first
            conn.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))

            # Create parent documents table
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS parent_documents (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    file_path TEXT NOT NULL,
                    title TEXT,
                    page_content TEXT NOT NULL,
                    document_metadata JSONB,
                    image_count INTEGER DEFAULT 0,
                    image_paths JSONB DEFAULT '[]'::jsonb,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                )
            """))

            # Create child documents table with vector column
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS child_documents (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    parent_id UUID REFERENCES parent_documents(id) ON DELETE CASCADE,
                    content TEXT NOT NULL,
                    document_metadata JSONB,
                    embedding VECTOR(768),
                    content_type TEXT DEFAULT 'text',
                    image_path TEXT,
                    image_description TEXT,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                )
            """))

            # Create index on vector column for similarity search
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS child_documents_embedding_idx
                ON child_documents USING ivfflat (embedding vector_cosine_ops)
            """))

            conn.commit()
            print("✓ 資料庫表格創建完成")

            # 檢查並修改現有表格（如果需要）
            self._update_existing_tables()

    def _update_existing_tables(self):
        """更新現有表格結構（如果需要）"""
        with self.engine.connect() as conn:
            # 檢查表格是否存在並進行必要的修改
            try:
                # 檢查 parent_documents 表是否存在
                result = conn.execute(text("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_name = 'parent_documents'
                    )
                """))
                if result.fetchone()[0]:
                    try:
                        conn.execute(text("""
                            ALTER TABLE parent_documents
                            ALTER COLUMN id SET DEFAULT gen_random_uuid()
                        """))
                        print("✓ parent_documents 表的 id 欄位已設置預設值")
                    except Exception as e:
                        print(f"修改 parent_documents 表失敗: {e}")

                # 檢查 child_documents 表是否存在
                result = conn.execute(text("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_name = 'child_documents'
                    )
                """))
                if result.fetchone()[0]:
                    try:
                        conn.execute(text("""
                            ALTER TABLE child_documents
                            ALTER COLUMN id SET DEFAULT gen_random_uuid()
                        """))
                        print("✓ child_documents 表的 id 欄位已設置預設值")
                    except Exception as e:
                        print(f"修改 child_documents 表失敗: {e}")

                    try:
                        conn.execute(text("""
                            ALTER TABLE child_documents
                            ALTER COLUMN embedding TYPE VECTOR(768)
                        """))
                        print("✓ child_documents 表的 embedding 欄位已更新為 768 維度")
                    except Exception as e:
                        print(f"修改 embedding 維度失敗: {e}")

                conn.commit()

            except Exception as e:
                print(f"檢查表格失敗: {e}")

    def insert_parent_document(self, document) -> str:
        """Insert a parent document into the database and return UUID"""
        import json
        with self.engine.connect() as conn:
            result = conn.execute(text("""
                INSERT INTO parent_documents (file_path, title, page_content, document_metadata)
                VALUES (:file_path, :title, :page_content, :document_metadata)
                RETURNING id
            """), {
                'file_path': document.metadata.get('source', ''),
                'title': document.metadata.get('title', ''),
                'page_content': document.page_content,
                'document_metadata': json.dumps(document.metadata)
            })
            conn.commit()
            return str(result.fetchone()[0])

    def insert_child_document(self, parent_id: str, document, embedding: List[float],
                             content_type: str = 'text', image_path: str = None,
                             image_description: str = None) -> str:
        """Insert a child document with embedding into the database and return UUID"""
        import json
        with self.engine.connect() as conn:
            result = conn.execute(text("""
                INSERT INTO child_documents (parent_id, content, content_type, image_path,
                                           image_description, embedding, document_metadata)
                VALUES (:parent_id, :content, :content_type, :image_path,
                        :image_description, :embedding, :document_metadata)
                RETURNING id
            """), {
                'parent_id': parent_id,
                'content': document.page_content if hasattr(document, 'page_content') else '',
                'content_type': content_type,
                'image_path': image_path,
                'image_description': image_description,
                'embedding': embedding,
                'document_metadata': json.dumps(document.metadata if hasattr(document, 'metadata') else {})
            })
            conn.commit()
            return str(result.fetchone()[0])

    def update_parent_document_images(self, parent_id: str, image_paths: List[str]):
        """Update parent document with image information"""
        import json
        with self.engine.connect() as conn:
            conn.execute(text("""
                UPDATE parent_documents
                SET image_count = :image_count, image_paths = :image_paths
                WHERE id = :parent_id
            """), {
                'parent_id': parent_id,
                'image_count': len(image_paths),
                'image_paths': json.dumps(image_paths)
            })
            conn.commit()

    def get_document(self, doc_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve a parent document by ID"""
        with self.engine.connect() as conn:
            result = conn.execute(text("""
                SELECT * FROM parent_documents WHERE id = :doc_id
            """), {'doc_id': doc_id})
            row = result.fetchone()
            if row:
                return dict(row._mapping)
            return None

    def similarity_search(self, query_embedding: List[float], limit: int = 5) -> List[Dict[str, Any]]:
        """Perform similarity search on child documents"""
        with self.engine.connect() as conn:
            result = conn.execute(text("""
                SELECT cd.*, pd.file_path,
                       1 - (cd.embedding <=> :query_embedding) as similarity
                FROM child_documents cd
                JOIN parent_documents pd ON cd.parent_id = pd.id
                ORDER BY cd.embedding <=> :query_embedding
                LIMIT :limit
            """), {
                'query_embedding': query_embedding,
                'limit': limit
            })
            return [dict(row._mapping) for row in result.fetchall()]

    def delete_documents_by_source(self, source_filename: str):
        """根據來源文件名刪除所有相關的父文檔和子文檔"""
        try:
            with self.engine.connect() as conn:
                # 首先獲取要刪除的父文檔ID列表
                parent_result = conn.execute(
                    text("SELECT id FROM parent_documents WHERE document_metadata->>'source' = :source"),
                    {"source": source_filename}
                )
                parent_ids = [row[0] for row in parent_result.fetchall()]

                if not parent_ids:
                    print(f"⚠️ 沒有找到來源為 {source_filename} 的文檔")
                    return {"deleted_parents": 0, "deleted_children": 0, "deleted_images": 0}

                # 逐個刪除每個父文檔的子文檔
                total_child_deleted = 0
                total_image_deleted = 0

                for parent_id in parent_ids:
                    # 統計要刪除的子文檔記錄數（文字類型）
                    child_result = conn.execute(
                        text("SELECT COUNT(*) FROM child_documents WHERE parent_id = :parent_id AND (content_type IS NULL OR content_type != 'image')"),
                        {"parent_id": parent_id}
                    )
                    child_count = child_result.fetchone()[0]

                    # 統計要刪除的圖片記錄數
                    image_result = conn.execute(
                        text("SELECT COUNT(*) FROM child_documents WHERE parent_id = :parent_id AND content_type = 'image'"),
                        {"parent_id": parent_id}
                    )
                    image_count = image_result.fetchone()[0]

                    # 刪除該父文檔的所有子文檔
                    child_delete_result = conn.execute(
                        text("DELETE FROM child_documents WHERE parent_id = :parent_id"),
                        {"parent_id": parent_id}
                    )

                    total_child_deleted += child_count
                    total_image_deleted += image_count

                # 再刪除父文檔
                parent_delete_result = conn.execute(
                    text("DELETE FROM parent_documents WHERE document_metadata->>'source' = :source"),
                    {"source": source_filename}
                )
                actual_parent_deleted = parent_delete_result.rowcount

                conn.commit()

                print(f"✓ 已刪除來源為 {source_filename} 的文檔:")
                print(f"  - 父文檔: {actual_parent_deleted} 個")
                print(f"  - 子文檔: {total_child_deleted} 個")
                print(f"  - 其中圖片記錄: {total_image_deleted} 個")

                return {
                    "deleted_parents": actual_parent_deleted,
                    "deleted_children": total_child_deleted,
                    "deleted_images": total_image_deleted
                }

        except Exception as e:
            print(f"✗ 刪除文檔失敗: {e}")
            raise e

    def get_documents_by_source(self, source_filename: str):
        """根據來源文件名獲取文檔統計信息"""
        try:
            with self.engine.connect() as conn:
                # 獲取父文檔數量
                parent_result = conn.execute(
                    text("SELECT COUNT(*) FROM parent_documents WHERE document_metadata->>'source' = :source"),
                    {"source": source_filename}
                )
                parent_count = parent_result.fetchone()[0]

                # 獲取子文檔數量
                child_result = conn.execute(
                    text("SELECT COUNT(*) FROM child_documents WHERE document_metadata->>'source' = :source AND content_type != 'image'"),
                    {"source": source_filename}
                )
                child_count = child_result.fetchone()[0]

                # 獲取圖片數量
                image_result = conn.execute(
                    text("SELECT COUNT(*) FROM child_documents WHERE document_metadata->>'source' = :source AND content_type = 'image'"),
                    {"source": source_filename}
                )
                image_count = image_result.fetchone()[0]

                return {
                    "parent_documents": parent_count,
                    "child_documents": child_count,
                    "images": image_count,
                    "exists": parent_count > 0
                }

        except Exception as e:
            print(f"✗ 查詢文檔統計失敗: {e}")
            return {"parent_documents": 0, "child_documents": 0, "images": 0, "exists": False}
