"""
圖片描述生成模組
使用 Gemini AI 模型為圖片生成文字描述
"""
import os
import base64
from typing import List, Dict, Optional
from PIL import Image
import google.generativeai as genai
from dotenv import load_dotenv
import warnings
warnings.filterwarnings("ignore")

# 載入 .env 文件
load_dotenv()

class GeminiImageCaptioner:
    """使用 Gemini 的圖片描述生成器"""

    def __init__(self, api_key: str = None):
        """
        初始化 Gemini 圖片描述生成器

        Args:
            api_key: Gemini API 金鑰
        """
        self.api_key = api_key or os.getenv('GEMINI_API_KEY')

        if not self.api_key:
            raise ValueError("請設置 GEMINI_API_KEY 環境變數或提供 API 金鑰")

        # 配置 Gemini
        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel('gemini-1.5-flash')

        print("✓ Gemini 圖片描述生成器初始化成功")
    
    def _encode_image(self, image_path: str) -> str:
        """將圖片編碼為 base64"""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            raise Exception(f"圖片編碼失敗: {e}")
    
    def generate_caption(self, image_path: str, language: str = "繁體中文") -> str:
        """
        使用 Gemini 為單張圖片生成描述

        Args:
            image_path: 圖片路徑
            language: 描述語言 (預設繁體中文)

        Returns:
            str: 圖片描述
        """
        try:
            # 載入圖片
            image = Image.open(image_path)

            # 建立提示詞
            prompt = f"""
請用{language}詳細描述這張圖片的內容。請包含以下要素：
1. 主要物件或人物
2. 顏色和視覺特徵
3. 場景或背景
4. 任何文字內容
5. 整體氛圍或風格

請提供簡潔但詳細的描述，約50-100字。
"""

            # 使用 Gemini 生成描述
            response = self.model.generate_content([prompt, image])

            if response.text:
                return response.text.strip()
            else:
                return "無法生成圖片描述"

        except Exception as e:
            print(f"✗ Gemini 圖片描述生成失敗 ({image_path}): {e}")
            return f"圖片描述生成失敗: {str(e)}"
    
    def generate_captions_batch(self, image_list: List[Dict], detailed: bool = False) -> List[Dict]:
        """
        批量生成圖片描述

        Args:
            image_list: 圖片信息列表
            detailed: 是否生成詳細描述 (此參數保留相容性，但不影響 Gemini 處理)

        Returns:
            List[Dict]: 包含描述的圖片信息列表
        """
        print(f"正在使用 Gemini 為 {len(image_list)} 張圖片生成描述...")

        for i, img_info in enumerate(image_list):
            try:
                print(f"  處理圖片 {i+1}/{len(image_list)}: {img_info['filename']}")

                # 使用 Gemini 生成描述
                caption = self.generate_caption(img_info['path'])
                img_info['caption'] = caption
                img_info['descriptions'] = {'gemini': caption}

                print(f"    ✓ 描述: {caption[:100]}...")

            except Exception as e:
                print(f"    ✗ 處理失敗: {e}")
                img_info['caption'] = "描述生成失敗"
                img_info['descriptions'] = {'gemini': "描述生成失敗"}

        print("✓ Gemini 圖片描述生成完成")
        return image_list

    def cleanup_model(self):
        """清理模型以釋放記憶體"""
        # Gemini 是 API 調用，無需清理本地模型
        print("✓ Gemini 資源已清理")

# 為了相容性，提供 ImageCaptioner 別名
ImageCaptioner = GeminiImageCaptioner

# 簡化版本的圖片描述生成器（不需要深度學習模型）
class SimpleImageCaptioner:
    """簡化版圖片描述生成器，基於圖片屬性"""
    
    def __init__(self):
        pass
    
    def generate_caption(self, image_path: str) -> str:
        """
        基於圖片屬性生成簡單描述
        
        Args:
            image_path: 圖片路徑
            
        Returns:
            str: 簡單描述
        """
        try:
            image = Image.open(image_path)
            width, height = image.size
            mode = image.mode
            
            # 基本描述
            description_parts = []
            
            # 基於內容的智能描述
            content_description = self._analyze_image_content(image, width, height)
            if content_description:
                description_parts.append(content_description)

            # 尺寸描述
            if width > 1000 or height > 1000:
                description_parts.append("高解析度圖片")
            elif width < 200 or height < 200:
                description_parts.append("小尺寸圖片")
            else:
                description_parts.append("中等尺寸圖片")

            # 格式描述
            description_parts.append(f"{mode}格式")
            description_parts.append(f"尺寸{width}x{height}像素")

            # 檔案大小
            file_size = os.path.getsize(image_path)
            if file_size > 1024 * 1024:
                description_parts.append(f"檔案大小{file_size // (1024*1024)}MB")
            else:
                description_parts.append(f"檔案大小{file_size // 1024}KB")

            return "，".join(description_parts)

        except Exception as e:
            return f"無法分析圖片: {str(e)}"

    def _analyze_image_content(self, image, width, height):
        """基於圖片特徵分析內容"""
        try:
            import numpy as np

            # 轉換為 numpy 陣列進行分析
            img_array = np.array(image)

            # 分析顏色
            if len(img_array.shape) == 3:
                # 彩色圖片
                avg_color = np.mean(img_array, axis=(0, 1))

                # 判斷主要顏色
                if avg_color[0] > avg_color[1] and avg_color[0] > avg_color[2]:
                    color_desc = "偏紅色調"
                elif avg_color[1] > avg_color[0] and avg_color[1] > avg_color[2]:
                    color_desc = "偏綠色調"
                elif avg_color[2] > avg_color[0] and avg_color[2] > avg_color[1]:
                    color_desc = "偏藍色調"
                else:
                    color_desc = "色彩豐富"

                # 分析亮度
                brightness = np.mean(avg_color)
                if brightness > 200:
                    brightness_desc = "明亮"
                elif brightness < 80:
                    brightness_desc = "較暗"
                else:
                    brightness_desc = "適中亮度"

                return f"{brightness_desc}的{color_desc}圖片"
            else:
                return "黑白圖片"

        except Exception:
            return "彩色圖片"
            
        except Exception as e:
            return f"無法分析圖片: {str(e)}"
    
    def generate_captions_batch(self, image_list: List[Dict], detailed: bool = False) -> List[Dict]:
        """批量生成簡單描述"""
        for img_info in image_list:
            img_info['caption'] = self.generate_caption(img_info['path'])
            img_info['descriptions'] = {'basic': img_info['caption']}
        return image_list
