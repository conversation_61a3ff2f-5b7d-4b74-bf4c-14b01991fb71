"""
settings.py

此模組用於定義專案的通用配置參數，例如 API Keys、預設的文本分割參數等。
"""

import os

# 預設的文本塊大小 (字元數)
DEFAULT_CHUNK_SIZE: int = 1000

# 預設的文本塊重疊大小 (字元數)
DEFAULT_CHUNK_OVERLAP: int = 200

# Google Gemini API Key (不再用於嵌入，但可能用於其他功能，暫時保留)
GEMINI_API_KEY: str = os.getenv("GEMINI_API_KEY", "")  # 🔒 移除硬編碼 API Key

# Unstructured 圖像輸出目錄
# 提取 PDF 中圖像時的暫存目錄
UNSTRUCTURED_IMAGE_OUTPUT_DIR: str = "data/images"

# PostgreSQL 資料庫連接配置
DB_HOST: str = os.getenv("DB_HOST", "localhost")
DB_PORT: int = int(os.getenv("DB_PORT", "5432"))
DB_NAME: str = os.getenv("DB_NAME", "postgres") # 根據使用者提供的圖片更新資料庫名稱
DB_USER: str = os.getenv("DB_USER", "postgres")
DB_PASSWORD: str = os.getenv("DB_PASSWORD", "rag")

# 本地向量化模型配置
# 使用相對於專案根目錄的相對路徑
LOCAL_EMBEDDING_MODEL_PATH: str = "src/embeddings/bge-base-zh-v1.5"
LOCAL_EMBEDDING_MODEL_DIMENSION: int = 768 # bge-base-zh-v1.5 的向量維度

# 向量化模型名稱 (現在指向本地模型路徑)
EMBEDDING_MODEL_NAME: str = LOCAL_EMBEDDING_MODEL_PATH

# 其他可配置參數...