#!/bin/bash

# OCR 專案部署腳本
# 支援本地 Docker 和 GCP VM 部署

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函數定義
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查 Docker 是否安裝
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安裝，請先安裝 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安裝，請先安裝 Docker Compose"
        exit 1
    fi
    
    log_success "Docker 環境檢查通過"
}

# 檢查環境變數文件
check_env() {
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            log_warning ".env 文件不存在，從 .env.example 複製"
            cp .env.example .env
            log_warning "請編輯 .env 文件設置正確的環境變數"
        else
            log_error ".env 和 .env.example 文件都不存在"
            exit 1
        fi
    fi
    log_success "環境變數文件檢查通過"
}

# 構建 Docker 映像
build_image() {
    log_info "開始構建 Docker 映像..."
    
    # 選擇 Dockerfile
    if [ "$1" = "optimized" ]; then
        DOCKERFILE="Dockerfile.optimized"
        log_info "使用優化版 Dockerfile"
    else
        DOCKERFILE="Dockerfile"
        log_info "使用標準 Dockerfile"
    fi
    
    docker build -f $DOCKERFILE -t ocr-api:latest .
    log_success "Docker 映像構建完成"
}

# 啟動服務
start_services() {
    log_info "啟動 Docker Compose 服務..."
    docker-compose up -d
    
    # 等待服務啟動
    log_info "等待服務啟動..."
    sleep 30
    
    # 檢查服務狀態
    if curl -f http://localhost:8080/health > /dev/null 2>&1; then
        log_success "OCR API 服務啟動成功"
        log_info "API 地址: http://localhost:8080"
        log_info "健康檢查: http://localhost:8080/health"
    else
        log_error "OCR API 服務啟動失敗"
        docker-compose logs ocr-api
        exit 1
    fi
}

# 停止服務
stop_services() {
    log_info "停止 Docker Compose 服務..."
    docker-compose down
    log_success "服務已停止"
}

# 清理資源
cleanup() {
    log_info "清理 Docker 資源..."
    docker-compose down -v --remove-orphans
    docker system prune -f
    log_success "清理完成"
}

# 查看日誌
show_logs() {
    if [ -z "$1" ]; then
        docker-compose logs -f
    else
        docker-compose logs -f $1
    fi
}

# 主函數
main() {
    case "$1" in
        "build")
            check_docker
            build_image $2
            ;;
        "start")
            check_docker
            check_env
            start_services
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            stop_services
            sleep 5
            check_env
            start_services
            ;;
        "logs")
            show_logs $2
            ;;
        "cleanup")
            cleanup
            ;;
        "deploy")
            check_docker
            check_env
            build_image $2
            start_services
            ;;
        *)
            echo "使用方法: $0 {build|start|stop|restart|logs|cleanup|deploy} [optimized]"
            echo ""
            echo "命令說明:"
            echo "  build [optimized]  - 構建 Docker 映像"
            echo "  start             - 啟動服務"
            echo "  stop              - 停止服務"
            echo "  restart           - 重啟服務"
            echo "  logs [service]    - 查看日誌"
            echo "  cleanup           - 清理資源"
            echo "  deploy [optimized] - 完整部署 (構建 + 啟動)"
            echo ""
            echo "範例:"
            echo "  $0 deploy          # 標準部署"
            echo "  $0 deploy optimized # 優化版部署"
            echo "  $0 logs ocr-api    # 查看 API 日誌"
            exit 1
            ;;
    esac
}

# 執行主函數
main "$@"
