<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能文件處理系統</title>
    <style>
        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #007bff;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        input[type="file"] {
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .loading {
            text-align: center;
            display: none;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .stats {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
        }
        .stats h4 {
            margin-top: 0;
            color: #495057;
        }
        .stats ul {
            margin: 0;
            padding-left: 20px;
        }
        .file-list {
            margin-top: 30px;
        }
        .file-item {
            background-color: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .file-info {
            flex-grow: 1;
        }
        .file-actions {
            margin-left: 15px;
        }
        .delete-btn {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        .delete-btn:hover {
            background-color: #c82333;
        }
        .delete-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .db-stats {
            font-size: 11px;
            color: #6c757d;
            margin-top: 5px;
        }
        .db-stats span {
            margin-right: 10px;
            padding: 2px 6px;
            background-color: #e9ecef;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 智能文件處理系統</h1>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 拖拽文件到此處或點擊選擇文件</p>
            <input type="file" id="fileInput" accept=".pdf,.txt,.doc,.docx">
            <br><br>
            <button onclick="uploadFile()">上傳並處理</button>
        </div>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在處理文件，請稍候...</p>
        </div>
        
        <div class="result" id="result"></div>
        
        <div class="file-list">
            <h3>📋 已處理文件列表</h3>
            <button onclick="loadFileList()">刷新列表</button>
            <div id="fileList"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        
        // 拖拽上傳功能
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
            }
        });
        
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });
        
        async function uploadFile() {
            const file = fileInput.files[0];
            if (!file) {
                showResult('請選擇一個文件', 'error');
                return;
            }
            
            // 檢查文件類型
            const allowedTypes = ['.pdf', '.txt', '.doc', '.docx'];
            const fileExt = '.' + file.name.split('.').pop().toLowerCase();
            if (!allowedTypes.includes(fileExt)) {
                showResult(`不支援的文件類型: ${fileExt}。支援的類型: ${allowedTypes.join(', ')}`, 'error');
                return;
            }
            
            showLoading(true);
            hideResult();
            
            const formData = new FormData();
            formData.append('file', file);
            
            try {
                const response = await fetch(`${API_BASE}/upload`, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    if (result.success) {
                        showResult('文件處理成功！', 'success');
                        showStats(result.processing_stats);
                    } else {
                        showResult(result.message, 'info');
                    }
                } else {
                    showResult(`處理失敗: ${result.detail}`, 'error');
                }
                
            } catch (error) {
                showResult(`網路錯誤: ${error.message}`, 'error');
            } finally {
                showLoading(false);
            }
        }
        
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }
        
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = message;
            resultDiv.style.display = 'block';
        }
        
        function hideResult() {
            document.getElementById('result').style.display = 'none';
        }
        
        function showStats(stats) {
            if (!stats) return;
            
            const resultDiv = document.getElementById('result');
            let statsHtml = '<div class="stats"><h4>📊 處理統計</h4><ul>';
            
            for (const [key, value] of Object.entries(stats)) {
                const displayKey = {
                    'file_type': '文件類型',
                    'parent_documents': 'Parent Documents',
                    'child_documents': 'Child Documents',
                    'images_processed': '處理圖片數',
                    'vector_dimension': '向量維度',
                    'split_method': '分割方式',
                    'titles_detected': '檢測標題數'
                }[key] || key;
                
                statsHtml += `<li><strong>${displayKey}:</strong> ${value}</li>`;
            }
            
            statsHtml += '</ul></div>';
            resultDiv.innerHTML += statsHtml;
        }
        
        async function loadFileList() {
            try {
                const response = await fetch(`${API_BASE}/files`);
                const result = await response.json();

                const fileListDiv = document.getElementById('fileList');

                if (result.files && result.files.length > 0) {
                    let html = `<p>總共 ${result.total} 個已處理文件:</p>`;

                    result.files.forEach(file => {
                        const processedDate = new Date(file.processed_at).toLocaleString('zh-TW');
                        const fileSize = (file.size / 1024).toFixed(1);
                        const dbStats = file.database_stats;

                        // 資料庫統計信息
                        let dbStatsHtml = '';
                        if (dbStats && dbStats.exists) {
                            dbStatsHtml = `
                                <div class="db-stats">
                                    <span>父文檔: ${dbStats.parent_documents}</span>
                                    <span>子文檔: ${dbStats.child_documents}</span>
                                    <span>圖片: ${dbStats.images}</span>
                                </div>
                            `;
                        } else {
                            dbStatsHtml = '<div class="db-stats"><span style="color: #dc3545;">⚠️ 資料庫中無記錄</span></div>';
                        }

                        html += `
                            <div class="file-item">
                                <div class="file-info">
                                    <strong>${file.filename}</strong><br>
                                    <small>原始名稱: ${file.original_name}</small><br>
                                    <small>大小: ${fileSize} KB | 處理時間: ${processedDate}</small>
                                    ${dbStatsHtml}
                                </div>
                                <div class="file-actions">
                                    <button class="delete-btn" onclick="deleteFile('${file.filename}', '${file.original_name}')">
                                        🗑️ 刪除
                                    </button>
                                </div>
                            </div>
                        `;
                    });

                    fileListDiv.innerHTML = html;
                } else {
                    fileListDiv.innerHTML = '<p>尚無已處理的文件</p>';
                }

            } catch (error) {
                document.getElementById('fileList').innerHTML = `<p>載入失敗: ${error.message}</p>`;
            }
        }

        async function deleteFile(filename, originalName) {
            if (!confirm(`確定要刪除文件 "${originalName}" 嗎？\n\n這將會刪除：\n- OUTPUT 目錄中的文件\n- 資料庫中的所有相關記錄\n- 相關的圖片文件\n\n此操作無法復原！`)) {
                return;
            }

            try {
                showResult('正在刪除文件...', 'info');

                const response = await fetch(`${API_BASE}/files/${encodeURIComponent(filename)}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    showResult('文件刪除成功！', 'success');

                    // 顯示刪除統計
                    let statsHtml = '<div class="stats"><h4>🗑️ 刪除統計</h4><ul>';
                    statsHtml += `<li><strong>刪除文件:</strong> ${result.deleted_file}</li>`;
                    if (result.database_deletions) {
                        const db = result.database_deletions;
                        statsHtml += `<li><strong>父文檔:</strong> ${db.deleted_parents} 個</li>`;
                        statsHtml += `<li><strong>子文檔:</strong> ${db.deleted_children} 個</li>`;
                        statsHtml += `<li><strong>圖片記錄:</strong> ${db.deleted_images} 個</li>`;
                    }
                    if (result.deleted_images && result.deleted_images.length > 0) {
                        statsHtml += `<li><strong>圖片文件:</strong> ${result.deleted_images.length} 個</li>`;
                    }
                    statsHtml += '</ul></div>';

                    document.getElementById('result').innerHTML += statsHtml;

                    // 重新載入文件列表
                    loadFileList();
                } else {
                    showResult(`刪除失敗: ${result.detail || result.message}`, 'error');
                }

            } catch (error) {
                showResult(`刪除失敗: ${error.message}`, 'error');
            }
        }
        
        // 頁面載入時自動載入文件列表
        window.onload = () => {
            loadFileList();
        };
    </script>
</body>
</html>
