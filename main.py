import os
from src.config.poppler_config import setup_all_tools
from src.text_splitters.pdf_table_splitter import PDFTableSplitter
from src.utils.document_model import Document
from src.database.db_manager import DBManager
from src.embeddings.embedding_generator import EmbeddingGenerator
from src.image_processing.image_processor import ImageProcessor
from langchain_text_splitters import RecursiveCharacterTextSplitter # 用於對大文檔進行逐元素拆分
from sqlalchemy import create_engine, text
from src.config.settings import DB_HOST, DB_PORT, DB_USER, DB_PASSWORD, DB_NAME

# 設置所有工具路徑
print("=== 檢查和設置工具路徑 ===")
setup_all_tools()
print("=" * 40)

def main():
    """
    主函數，用於演示 PDFTableSplitter 的使用，並將結果存入資料庫。
    """
    pdf_file_path = "./data/檔案/catpdf.pdf" # 使用者提供的 PDF 路徑

    if not os.path.exists(pdf_file_path):
        print(f"錯誤: 文件不存在於 {pdf_file_path}")
        print("請將 PDF 文件放入 ./data/檔案/ 目錄中")
        return

    # 初始化資料庫管理器和嵌入生成器
    db_manager = DBManager()
    embedding_generator = EmbeddingGenerator()

    # 初始化圖片處理器 (啟用中文描述)
    image_processor = ImageProcessor(
        output_dir="./data/照片",
        use_ai_captioning=True,  # 使用 AI 生成中文描述
        db_manager=db_manager,
        embedding_generator=embedding_generator
    )

    # 確保資料庫存在並啟用 pgvector 擴展
    temp_engine = create_engine(f"postgresql+psycopg2://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/postgres")
    with temp_engine.connect() as conn:
        conn.execute(text("COMMIT")) # 結束任何掛起的事務
        try:
            conn.execute(text(f"CREATE DATABASE {DB_NAME}"))
            print(f"資料庫 '{DB_NAME}' 已創建。")
        except Exception as e:
            if "already exists" in str(e):
                print(f"資料庫 '{DB_NAME}' 已存在。")
            else:
                print(f"創建資料庫失敗: {e}")
                return
        conn.close()
    
    # 連接到目標資料庫並啟用 pgvector 擴展
    target_engine = create_engine(f"postgresql+psycopg2://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}")
    with target_engine.connect() as conn:
        conn.execute(text("CREATE EXTENSION IF NOT EXISTS vector;"))
        conn.commit() # 提交擴展創建
        print(f"pgvector 擴展已在 '{DB_NAME}' 中啟用。")
        conn.close()

    # 創建資料庫表
    db_manager.create_tables()

    print(f"正在分割 PDF 文件: {pdf_file_path}")
    
    # 演示基於標題的分割 (作為 Parent Documents)
    print("\n--- 演示基於標題的 PDF 分割 (作為 Parent Documents) ---")
    splitter_by_title = PDFTableSplitter(split_by_title=True)
    parent_documents = splitter_by_title.split_document(pdf_file_path)
    print(f"\n分割完成 (按標題)，共 {len(parent_documents)} 個 Parent Document 物件。")

    # 初始化逐元素拆分器 (用於生成 Child Documents)
    # 這裡使用 LangChain 的 RecursiveCharacterTextSplitter 進行通用文本拆分
    child_splitter = RecursiveCharacterTextSplitter(
        chunk_size=500, # 可以根據需求調整小文檔的 chunk_size
        chunk_overlap=100 # 可以根據需求調整小文檔的 chunk_overlap
    )

    # 收集所有存儲的父文檔ID，用於後續圖片處理
    stored_parent_ids = []

    for i, parent_doc in enumerate(parent_documents):
        pages_info = parent_doc.metadata.get("pages", "N/A")
        print(f"\n--- Parent Document {i+1} (頁數: {pages_info}) ---")
        print(f"內容 (前200字): {parent_doc.page_content[:200]}...")
        print(f"元數據: {parent_doc.metadata}") # 這裡仍然使用 doc.metadata，因為 Document 物件本身沒有改變

        # 將 Parent Document 存入資料庫
        parent_id = db_manager.insert_parent_document(parent_doc)
        stored_parent_ids.append(parent_id)  # 收集父文檔ID

        # 對 Parent Document 的內容進行逐元素拆分 (生成 Child Documents)
        print(f"  正在對 Parent Document (ID: {parent_id}) 進行逐元素拆分...")
        child_texts = child_splitter.split_text(parent_doc.page_content)
        
        child_documents = []
        for j, text_content in enumerate(child_texts):
            # 繼承 Parent Document 的元數據，並添加 Child Document 特有的元數據
            child_metadata = {
                **parent_doc.metadata,
                "parent_id": str(parent_id), # 關聯 Parent Document 的 ID
                "type": "pdf_child_text",
                "child_chunk_index": j
            }
            child_documents.append(Document(page_content=text_content, metadata=child_metadata))

        print(f"  拆分完成，共 {len(child_documents)} 個 Child Document 物件。")

        # 向量化並儲存 Child Documents
        for j, child_doc in enumerate(child_documents):
            # 生成向量嵌入
            embedding = embedding_generator.generate_single_embedding(child_doc.page_content)

            # 儲存 Child Document 及其向量
            db_manager.insert_child_document(parent_id, child_doc, embedding)
            
            # 為了避免輸出過多，這裡只列印前幾個 Child Document 的資訊
            if j < 3:
                print(f"    --- Child Document {j+1} (頁數: {child_doc.metadata.get('pages', 'N/A')}) ---")
                print(f"    內容 (前100字): {child_doc.page_content[:100]}...")
                print(f"    元數據: {child_doc.metadata}")
                print(f"    嵌入維度: {len(embedding)}")
            elif j == 3:
                print("    ...")

        # 圖片處理已移到循環外部，避免重複處理

    # 🖼️ 處理整個 PDF 的圖片（只執行一次）
    print(f"\n=== 🖼️ 處理 PDF 圖片 ===")
    print(f"正在從 {pdf_file_path} 提取和處理圖片...")

    try:
        # 提取所有圖片並生成描述
        from src.image_processing.image_extractor import ImageExtractor
        extractor = ImageExtractor("./data/照片")
        all_images = extractor.extract_and_deduplicate(pdf_file_path)

        if all_images:
            print(f"✓ 從 PDF 中提取了 {len(all_images)} 張圖片")

            # 為每張圖片生成描述並存入資料庫
            total_saved = 0
            for img_info in all_images:
                try:
                    # 生成圖片描述
                    caption = image_processor.captioner.generate_caption(img_info['path'])
                    img_info['caption'] = caption
                    img_info['descriptions'] = {'basic': caption}

                    # 創建圖片文檔對象
                    img_document = Document()
                    img_document.page_content = caption
                    img_document.content = caption
                    img_document.metadata = {
                        'source': pdf_file_path,
                        'type': 'image',
                        'page_number': img_info.get('page_number', 0),
                        'image_filename': img_info.get('filename', ''),
                        'image_width': img_info.get('width', 0),
                        'image_height': img_info.get('height', 0),
                        'image_size_bytes': img_info.get('size_bytes', 0),
                        'extracted_at': img_info.get('extracted_at', ''),
                        'descriptions': img_info.get('descriptions', {})
                    }

                    # 生成向量嵌入
                    embedding = embedding_generator.generate_single_embedding(caption)

                    # 找到對應的父文檔（根據頁面號）
                    page_num = img_info.get('page_number', 1)
                    target_parent_id = None

                    # 簡單策略：將圖片關聯到第一個父文檔
                    # 更複雜的策略可以根據頁面範圍來匹配
                    if stored_parent_ids:
                        target_parent_id = stored_parent_ids[0]  # 使用第一個父文檔的ID

                    if target_parent_id:
                        # 存入資料庫
                        child_id = db_manager.insert_child_document(
                            parent_id=target_parent_id,
                            document=img_document,
                            embedding=embedding,
                            content_type='image',
                            image_path=img_info.get('path', ''),
                            image_description=caption
                        )
                        total_saved += 1
                        print(f"  ✓ 圖片 {img_info['filename']} (第{page_num}頁) -> 描述: {caption[:50]}...")

                except Exception as e:
                    print(f"  ✗ 處理圖片 {img_info.get('filename', 'unknown')} 失敗: {e}")
                    continue

            print(f"✓ 圖片處理完成: 成功處理 {total_saved}/{len(all_images)} 張圖片")

            # 更新第一個父文檔的圖片信息
            if stored_parent_ids and all_images:
                image_paths = [img['path'] for img in all_images]
                db_manager.update_parent_document_images(stored_parent_ids[0], image_paths)
                print(f"✓ 已更新父文檔的圖片關聯信息")
        else:
            print("ℹ️ 此 PDF 中沒有找到圖片")

    except Exception as e:
        print(f"⚠️ 圖片處理失敗: {e}")

    print("\n--- 原始的 PDF 分割 (逐元素，不存入資料庫) ---")
    splitter_default = PDFTableSplitter() # 預設不按標題分割
    documents_default = splitter_default.split_document(pdf_file_path)
    print(f"\n分割完成 (預設)，共 {len(documents_default)} 個 Document 物件。")
    for i, doc in enumerate(documents_default):
        pages_info = doc.metadata.get("pages", "N/A")
        print(f"\n--- Document {i+1} (頁數: {pages_info}) ---")
        print(f"內容 (前200字): {doc.page_content[:200]}...")
        print(f"元數據: {doc.metadata}")

    # 清理資源
    print("\n=== 清理資源 ===")
    try:
        image_processor.cleanup()
        print("✓ 所有資源已清理完成")
    except Exception as e:
        print(f"⚠️ 資源清理時出現問題: {e}")

if __name__ == "__main__":
    main()