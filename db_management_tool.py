"""
資料庫管理工具
功能：
1. 建立表格
2. 刪除表格（只有向量的部分）
3. 確認表格
4. 離開
"""
import os
import sys
from pathlib import Path
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.exc import SQLAlchemyError

# 添加 src 目錄到路徑
sys.path.append(str(Path(__file__).parent / "src"))

from src.database.db_manager import DBManager
from src.config.settings import DB_HOST, DB_PORT, DB_NAME, DB_USER, DB_PASSWORD

class DatabaseManagementTool:
    def __init__(self):
        """初始化資料庫管理工具"""
        self.db_manager = None
        self.engine = None
        self.setup_database_connection()
    
    def setup_database_connection(self):
        """設置資料庫連接"""
        try:
            # 構建資料庫連接 URL
            database_url = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
            self.engine = create_engine(database_url)
            self.db_manager = DBManager()
            print("✅ 資料庫連接成功")
        except Exception as e:
            print(f"❌ 資料庫連接失敗: {e}")
            sys.exit(1)
    
    def show_menu(self):
        """顯示主選單"""
        print("\n" + "="*50)
        print("🗃️  資料庫管理工具")
        print("="*50)
        print("1. 建立表格")
        print("2. 刪除表格（只有向量的部分）")
        print("3. 確認表格")
        print("4. 離開")
        print("="*50)
    
    def create_tables(self):
        """建立表格"""
        print("\n🔨 建立資料庫表格...")
        
        try:
            # 使用 DBManager 的建立表格方法
            self.db_manager.create_tables()
            print("✅ 表格建立完成！")
            
            # 顯示建立的表格
            self.show_table_info()
            
        except Exception as e:
            print(f"❌ 建立表格失敗: {e}")
    
    def delete_vector_data(self):
        """刪除向量資料（保留表格結構）"""
        print("\n🗑️  刪除向量資料...")
        
        # 確認操作
        confirm = input("⚠️  確定要刪除所有向量資料嗎？這將清空 child_documents 和 parent_documents 表格！(yes/NO): ").strip()
        
        if confirm.lower() != 'yes':
            print("❌ 操作已取消")
            return
        
        try:
            with self.engine.connect() as conn:
                # 刪除 child_documents 表格的所有資料（包含向量）
                result = conn.execute(text("DELETE FROM child_documents"))
                child_deleted = result.rowcount
                
                # 刪除 parent_documents 表格的所有資料
                result = conn.execute(text("DELETE FROM parent_documents"))
                parent_deleted = result.rowcount
                
                conn.commit()
                
                print(f"✅ 向量資料刪除完成！")
                print(f"   - 刪除父文檔: {parent_deleted} 個")
                print(f"   - 刪除子文檔: {child_deleted} 個")
                
        except Exception as e:
            print(f"❌ 刪除向量資料失敗: {e}")
    
    def check_tables(self):
        """確認表格狀態"""
        print("\n📊 檢查資料庫表格狀態...")
        
        try:
            inspector = inspect(self.engine)
            tables = inspector.get_table_names()
            
            print(f"\n📋 資料庫中的表格 ({len(tables)} 個):")
            
            for table in tables:
                print(f"\n🔹 表格: {table}")
                
                # 獲取表格欄位信息
                columns = inspector.get_columns(table)
                print(f"   欄位數: {len(columns)}")
                
                # 獲取資料數量
                with self.engine.connect() as conn:
                    result = conn.execute(text(f"SELECT COUNT(*) FROM {table}"))
                    count = result.fetchone()[0]
                    print(f"   資料數: {count} 筆")
                
                # 顯示主要欄位
                main_columns = [col['name'] for col in columns[:5]]  # 只顯示前5個欄位
                print(f"   主要欄位: {', '.join(main_columns)}")
                
                if len(columns) > 5:
                    print(f"   ... 還有 {len(columns) - 5} 個欄位")
            
            # 特別檢查向量相關表格
            self.check_vector_tables()
            
        except Exception as e:
            print(f"❌ 檢查表格失敗: {e}")
    
    def check_vector_tables(self):
        """檢查向量相關表格的詳細信息"""
        print(f"\n🔍 向量表格詳細信息:")
        
        try:
            with self.engine.connect() as conn:
                # 檢查 parent_documents
                if self.table_exists('parent_documents'):
                    result = conn.execute(text("""
                        SELECT 
                            COUNT(*) as total,
                            COUNT(DISTINCT document_metadata->>'source') as unique_sources
                        FROM parent_documents
                    """))
                    row = result.fetchone()
                    print(f"   📄 parent_documents: {row[0]} 筆資料, {row[1]} 個不同來源")
                
                # 檢查 child_documents
                if self.table_exists('child_documents'):
                    result = conn.execute(text("""
                        SELECT 
                            COUNT(*) as total,
                            COUNT(CASE WHEN content_type = 'image' THEN 1 END) as images,
                            COUNT(CASE WHEN content_type != 'image' OR content_type IS NULL THEN 1 END) as texts
                        FROM child_documents
                    """))
                    row = result.fetchone()
                    print(f"   📝 child_documents: {row[0]} 筆資料 (文字: {row[2]}, 圖片: {row[1]})")
                    
                    # 檢查向量維度（使用 pgvector 的函數）
                    try:
                        result = conn.execute(text("""
                            SELECT vector_dims(embedding) as dimension
                            FROM child_documents
                            WHERE embedding IS NOT NULL
                            LIMIT 1
                        """))
                        dim_row = result.fetchone()
                        if dim_row and dim_row[0]:
                            print(f"   🔢 向量維度: {dim_row[0]}")
                        else:
                            print(f"   ⚠️  沒有向量資料")
                    except Exception as e:
                        print(f"   ⚠️  無法檢查向量維度: {e}")
                
        except Exception as e:
            print(f"❌ 檢查向量表格失敗: {e}")
    
    def table_exists(self, table_name):
        """檢查表格是否存在"""
        try:
            inspector = inspect(self.engine)
            return table_name in inspector.get_table_names()
        except:
            return False
    
    def show_table_info(self):
        """顯示表格基本信息"""
        try:
            inspector = inspect(self.engine)
            tables = inspector.get_table_names()
            
            print(f"\n📋 已建立的表格:")
            for table in tables:
                if table in ['parent_documents', 'child_documents']:
                    with self.engine.connect() as conn:
                        result = conn.execute(text(f"SELECT COUNT(*) FROM {table}"))
                        count = result.fetchone()[0]
                        print(f"   ✅ {table}: {count} 筆資料")
                        
        except Exception as e:
            print(f"⚠️ 無法顯示表格信息: {e}")
    
    def run(self):
        """運行主程式"""
        print("🚀 資料庫管理工具啟動")
        
        while True:
            self.show_menu()
            
            try:
                choice = input("\n請選擇操作 (1-4): ").strip()
                
                if choice == '1':
                    self.create_tables()
                elif choice == '2':
                    self.delete_vector_data()
                elif choice == '3':
                    self.check_tables()
                elif choice == '4':
                    print("\n👋 再見！")
                    break
                else:
                    print("❌ 無效選擇，請輸入 1-4")
                
                # 等待用戶按鍵繼續
                if choice in ['1', '2', '3']:
                    input("\n按 Enter 鍵繼續...")
                    
            except KeyboardInterrupt:
                print("\n\n👋 程式已中斷，再見！")
                break
            except Exception as e:
                print(f"❌ 操作失敗: {e}")
                input("\n按 Enter 鍵繼續...")

def main():
    """主函數"""
    tool = DatabaseManagementTool()
    tool.run()

if __name__ == "__main__":
    main()
