# 多階段構建 - 優化版本
# 第一階段：構建階段
FROM python:3.10-slim as builder

# 設置環境變數
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV DEBIAN_FRONTEND=noninteractive

# 安裝構建依賴
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    wget \
    git \
    pkg-config \
    libpq-dev \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 安裝 Poetry
RUN pip install --no-cache-dir poetry==1.8.3

# 設置工作目錄
WORKDIR /app

# 複製 Poetry 配置文件
COPY pyproject.toml poetry.lock* ./

# 配置 Poetry 並安裝依賴
RUN poetry config virtualenvs.create false \
    && poetry install --only=main --no-dev --no-interaction --no-ansi

# 第二階段：運行階段
FROM python:3.10-slim

# 設置環境變數
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV DEBIAN_FRONTEND=noninteractive
ENV ENVIRONMENT=production
ENV PORT=8080
ENV PYTHONPATH=/app

# 安裝運行時依賴
RUN apt-get update && apt-get install -y \
    curl \
    # Tesseract OCR
    tesseract-ocr \
    tesseract-ocr-chi-tra \
    tesseract-ocr-chi-sim \
    libtesseract-dev \
    # Poppler (PDF 處理)
    poppler-utils \
    libpoppler-dev \
    # 圖片處理
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    # PostgreSQL 客戶端
    libpq5 \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 設置 Tesseract 環境變數
ENV TESSDATA_PREFIX=/usr/share/tesseract-ocr/5/tessdata/

# 創建非 root 用戶
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 設置工作目錄
WORKDIR /app

# 從構建階段複製 Python 套件
COPY --from=builder /usr/local/lib/python3.10/site-packages /usr/local/lib/python3.10/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# 創建必要的目錄結構
RUN mkdir -p /app/data/input \
    && mkdir -p /app/data/output \
    && mkdir -p /app/data/照片 \
    && mkdir -p /app/data/檔案 \
    && mkdir -p /app/src/embeddings \
    && mkdir -p /app/logs

# 複製應用程式碼
COPY --chown=appuser:appuser . .

# 設置權限
RUN chown -R appuser:appuser /app \
    && chmod -R 755 /app/data \
    && chmod -R 755 /app/src

# 切換到非 root 用戶
USER appuser

# 暴露端口
EXPOSE 8080

# 健康檢查
HEALTHCHECK --interval=30s --timeout=30s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 啟動命令
CMD ["python", "-m", "uvicorn", "api_server:app", "--host", "0.0.0.0", "--port", "8080", "--workers", "1"]
