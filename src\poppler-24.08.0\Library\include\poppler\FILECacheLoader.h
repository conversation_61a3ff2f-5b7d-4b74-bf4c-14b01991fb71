//========================================================================
//
// FILECacheLoader.h
//
// This file is licensed under the GPLv2 or later
//
// Copyright 2010 Hib Eris <<EMAIL>>
// Copyright 2010, 2022 Albert Astals Cid <<EMAIL>>
// Copyright 2021 <PERSON> <<EMAIL>>
//
//========================================================================

#ifndef FILECACHELOADER_H
#define FILECACHELOADER_H

#include "CachedFile.h"

#include <cstdio>

class POPPLER_PRIVATE_EXPORT FILECacheLoader : public CachedFileLoader
{
    FILE *file = stdin;

public:
    FILECacheLoader() = default;
    ~FILECacheLoader() override;

    explicit FILECacheLoader(FILE *fileA) : file(fileA) { }

    size_t init(CachedFile *cachedFile) override;
    int load(const std::vector<ByteRange> &ranges, CachedFileWriter *writer) override;
};

#endif
