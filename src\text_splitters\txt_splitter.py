"""
TXT 文件分割器
支援按標題分割和逐元素分割
"""
import re
from typing import List
from src.utils.document_model import Document
from src.utils.file_reader import FileReader

class TXTSplitter:
    """TXT 文件分割器，支援按標題分割和逐元素分割"""
    
    def __init__(self, split_by_title: bool = False):
        """
        初始化 TXT 分割器
        
        Args:
            split_by_title: 是否按標題分割（作為 Parent Documents）
        """
        self.split_by_title = split_by_title
    
    def split_document(self, file_path: str) -> List[Document]:
        """
        從指定 TXT 文件路徑讀取內容並進行文本分割
        
        Args:
            file_path (str): 待分割 TXT 文件的路徑
            
        Returns:
            List[Document]: 包含分割後文本塊及其元數據的 Document 物件列表
        """
        # 讀取 TXT 文件內容
        content = FileReader.read_text_file(file_path)
        
        if self.split_by_title:
            return self._split_by_title(content, file_path)
        else:
            return self._split_by_paragraphs(content, file_path)
    
    def _split_by_title(self, content: str, file_path: str) -> List[Document]:
        """按標題分割文本"""
        documents = []
        
        # 定義標題模式（可以根據需要調整）
        title_patterns = [
            r'^第[一二三四五六七八九十\d]+章\s+(.+)$',  # 第一章 標題
            r'^第[一二三四五六七八九十\d]+節\s+(.+)$',  # 第一節 標題
            r'^[一二三四五六七八九十\d]+[、\.]\s*(.+)$',  # 一、標題 或 1. 標題
            r'^[A-Z\d]+[、\.]\s*(.+)$',  # A、標題 或 A. 標題
            r'^#+\s+(.+)$',  # Markdown 標題 # 標題
            r'^(.+)\n=+$',  # 下劃線標題
            r'^(.+)\n-+$',  # 破折號標題
        ]
        
        lines = content.split('\n')
        current_section = []
        current_title = "文檔開始"
        chunk_index = 0
        
        for line_num, line in enumerate(lines):
            line = line.strip()
            
            # 檢查是否為標題
            is_title = False
            title_text = None
            
            for pattern in title_patterns:
                match = re.match(pattern, line, re.MULTILINE)
                if match:
                    is_title = True
                    title_text = match.group(1) if match.groups() else line
                    break
            
            # 如果沒有匹配到複雜模式，檢查簡單的標題模式
            if not is_title and line:
                # 檢查是否為全大寫標題（長度適中）
                if line.isupper() and 3 <= len(line) <= 50:
                    is_title = True
                    title_text = line
                # 檢查是否為居中標題（前後有空格或特殊字符）
                elif re.match(r'^\s*[=\-*]+\s*(.+?)\s*[=\-*]+\s*$', line):
                    is_title = True
                    title_text = re.match(r'^\s*[=\-*]+\s*(.+?)\s*[=\-*]+\s*$', line).group(1)
            
            if is_title and current_section:
                # 保存前一個章節
                section_content = '\n'.join(current_section).strip()
                if section_content:
                    doc = Document(
                        page_content=section_content,
                        metadata={
                            'source': file_path,
                            'type': 'txt_title_section',
                            'title': current_title,
                            'chunk_index': chunk_index,
                            'line_start': current_section_start,
                            'line_end': line_num - 1
                        }
                    )
                    documents.append(doc)
                    chunk_index += 1
                
                # 開始新章節
                current_title = title_text or line
                current_section = []
                current_section_start = line_num
            else:
                # 添加到當前章節
                current_section.append(line)
                if not hasattr(self, 'current_section_start'):
                    current_section_start = line_num
        
        # 處理最後一個章節
        if current_section:
            section_content = '\n'.join(current_section).strip()
            if section_content:
                doc = Document(
                    page_content=section_content,
                    metadata={
                        'source': file_path,
                        'type': 'txt_title_section',
                        'title': current_title,
                        'chunk_index': chunk_index,
                        'line_start': current_section_start,
                        'line_end': len(lines) - 1
                    }
                )
                documents.append(doc)
        
        return documents
    
    def _split_by_paragraphs(self, content: str, file_path: str) -> List[Document]:
        """按段落分割文本"""
        documents = []
        
        # 按雙換行符分割段落
        paragraphs = re.split(r'\n\s*\n', content)
        
        for i, paragraph in enumerate(paragraphs):
            paragraph = paragraph.strip()
            if paragraph:  # 跳過空段落
                doc = Document(
                    page_content=paragraph,
                    metadata={
                        'source': file_path,
                        'type': 'txt_paragraph',
                        'chunk_index': i,
                        'paragraph_number': i + 1
                    }
                )
                documents.append(doc)
        
        return documents
    
    def get_title_preview(self, file_path: str, max_lines: int = 50) -> List[str]:
        """
        預覽文件中可能的標題，幫助用戶決定是否使用標題分割
        
        Args:
            file_path: TXT 文件路徑
            max_lines: 最多檢查的行數
            
        Returns:
            List[str]: 檢測到的可能標題列表
        """
        content = FileReader.read_text_file(file_path)
        lines = content.split('\n')[:max_lines]
        
        potential_titles = []
        title_patterns = [
            r'^第[一二三四五六七八九十\d]+章\s+(.+)$',
            r'^第[一二三四五六七八九十\d]+節\s+(.+)$',
            r'^[一二三四五六七八九十\d]+[、\.]\s*(.+)$',
            r'^[A-Z\d]+[、\.]\s*(.+)$',
            r'^#+\s+(.+)$',
        ]
        
        for line_num, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
                
            # 檢查標題模式
            for pattern in title_patterns:
                if re.match(pattern, line):
                    potential_titles.append(f"第{line_num+1}行: {line}")
                    break
            else:
                # 檢查其他標題特徵
                if line.isupper() and 3 <= len(line) <= 50:
                    potential_titles.append(f"第{line_num+1}行: {line} (全大寫)")
                elif re.match(r'^\s*[=\-*]+\s*(.+?)\s*[=\-*]+\s*$', line):
                    potential_titles.append(f"第{line_num+1}行: {line} (裝飾標題)")
        
        return potential_titles
