# 使用 Python 3.10 官方映像
FROM python:3.10-slim

# 設置工作目錄
WORKDIR /app

# 設置環境變數
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV DEBIAN_FRONTEND=noninteractive

# 安裝系統依賴
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    wget \
    git \
    pkg-config \
    libpq-dev \
    gcc \
    g++ \
    # Tesseract OCR 依賴
    tesseract-ocr \
    tesseract-ocr-chi-tra \
    tesseract-ocr-chi-sim \
    libtesseract-dev \
    # Poppler 依賴 (PDF 處理)
    poppler-utils \
    libpoppler-dev \
    # 圖片處理依賴
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    # 清理快取
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 安裝 Poetry
RUN pip install --no-cache-dir poetry==1.8.3

# 複製 Poetry 配置文件
COPY pyproject.toml poetry.lock* ./

# 配置 Poetry
RUN poetry config virtualenvs.create false \
    && poetry config virtualenvs.in-project false

# 安裝 Python 依賴
RUN poetry install --only=main --no-dev --no-interaction --no-ansi

# 創建必要的目錄結構
RUN mkdir -p /app/data/input \
    && mkdir -p /app/data/output \
    && mkdir -p /app/data/照片 \
    && mkdir -p /app/data/檔案 \
    && mkdir -p /app/src/embeddings \
    && mkdir -p /app/src/tesseract-ocr \
    && mkdir -p /app/src/poppler-24.08.0

# 複製應用程式碼
COPY . .

# 設置權限
RUN chmod +x /app/src/tesseract-ocr/tesseract.exe || true \
    && chmod -R 755 /app/data \
    && chmod -R 755 /app/src

# 設置環境變數
ENV ENVIRONMENT=production
ENV PORT=8080
ENV PYTHONPATH=/app
ENV TESSDATA_PREFIX=/usr/share/tesseract-ocr/5/tessdata/

# 暴露端口
EXPOSE 8080

# 健康檢查
HEALTHCHECK --interval=30s --timeout=30s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 啟動命令
CMD ["python", "-m", "uvicorn", "api_server:app", "--host", "0.0.0.0", "--port", "8080", "--workers", "1"]
