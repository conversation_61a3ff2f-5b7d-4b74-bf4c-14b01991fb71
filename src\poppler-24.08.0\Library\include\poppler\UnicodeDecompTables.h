// Generated by gen-unicode-tables.py

typedef struct
{
    Unicode character;
    int length;
    int offset;
} decomposition;

#define DECOMP_TABLE_LENGTH 5722

static const decomposition decomp_table[] = {
    { 0xa0, 1, 0 },       { 0xa8, 2, 1 },       { 0xaa, 1, 3 },       { 0xaf, 2, 4 },       { 0xb2, 1, 6 },       { 0xb3, 1, 7 },       { 0xb4, 2, 8 },       { 0xb5, 1, 10 },      { 0xb8, 2, 11 },      { 0xb9, 1, 13 },
    { 0xba, 1, 14 },      { 0xbc, 3, 15 },      { 0xbd, 3, 18 },      { 0xbe, 3, 21 },      { 0xc0, 2, 24 },      { 0xc1, 2, 26 },      { 0xc2, 2, 28 },      { 0xc3, 2, 30 },      { 0xc4, 2, 32 },      { 0xc5, 2, 34 },
    { 0xc7, 2, 36 },      { 0xc8, 2, 38 },      { 0xc9, 2, 40 },      { 0xca, 2, 42 },      { 0xcb, 2, 44 },      { 0xcc, 2, 46 },      { 0xcd, 2, 48 },      { 0xce, 2, 50 },      { 0xcf, 2, 52 },      { 0xd1, 2, 54 },
    { 0xd2, 2, 56 },      { 0xd3, 2, 58 },      { 0xd4, 2, 60 },      { 0xd5, 2, 62 },      { 0xd6, 2, 64 },      { 0xd9, 2, 66 },      { 0xda, 2, 68 },      { 0xdb, 2, 70 },      { 0xdc, 2, 72 },      { 0xdd, 2, 74 },
    { 0xe0, 2, 76 },      { 0xe1, 2, 78 },      { 0xe2, 2, 80 },      { 0xe3, 2, 82 },      { 0xe4, 2, 84 },      { 0xe5, 2, 86 },      { 0xe7, 2, 88 },      { 0xe8, 2, 90 },      { 0xe9, 2, 92 },      { 0xea, 2, 94 },
    { 0xeb, 2, 96 },      { 0xec, 2, 98 },      { 0xed, 2, 100 },     { 0xee, 2, 102 },     { 0xef, 2, 104 },     { 0xf1, 2, 106 },     { 0xf2, 2, 108 },     { 0xf3, 2, 110 },     { 0xf4, 2, 112 },     { 0xf5, 2, 114 },
    { 0xf6, 2, 116 },     { 0xf9, 2, 118 },     { 0xfa, 2, 120 },     { 0xfb, 2, 122 },     { 0xfc, 2, 124 },     { 0xfd, 2, 126 },     { 0xff, 2, 128 },     { 0x100, 2, 130 },    { 0x101, 2, 132 },    { 0x102, 2, 134 },
    { 0x103, 2, 136 },    { 0x104, 2, 138 },    { 0x105, 2, 140 },    { 0x106, 2, 142 },    { 0x107, 2, 144 },    { 0x108, 2, 146 },    { 0x109, 2, 148 },    { 0x10a, 2, 150 },    { 0x10b, 2, 152 },    { 0x10c, 2, 154 },
    { 0x10d, 2, 156 },    { 0x10e, 2, 158 },    { 0x10f, 2, 160 },    { 0x112, 2, 162 },    { 0x113, 2, 164 },    { 0x114, 2, 166 },    { 0x115, 2, 168 },    { 0x116, 2, 170 },    { 0x117, 2, 172 },    { 0x118, 2, 174 },
    { 0x119, 2, 176 },    { 0x11a, 2, 178 },    { 0x11b, 2, 180 },    { 0x11c, 2, 182 },    { 0x11d, 2, 184 },    { 0x11e, 2, 186 },    { 0x11f, 2, 188 },    { 0x120, 2, 190 },    { 0x121, 2, 192 },    { 0x122, 2, 194 },
    { 0x123, 2, 196 },    { 0x124, 2, 198 },    { 0x125, 2, 200 },    { 0x128, 2, 202 },    { 0x129, 2, 204 },    { 0x12a, 2, 206 },    { 0x12b, 2, 208 },    { 0x12c, 2, 210 },    { 0x12d, 2, 212 },    { 0x12e, 2, 214 },
    { 0x12f, 2, 216 },    { 0x130, 2, 218 },    { 0x132, 2, 220 },    { 0x133, 2, 222 },    { 0x134, 2, 224 },    { 0x135, 2, 226 },    { 0x136, 2, 228 },    { 0x137, 2, 230 },    { 0x139, 2, 232 },    { 0x13a, 2, 234 },
    { 0x13b, 2, 236 },    { 0x13c, 2, 238 },    { 0x13d, 2, 240 },    { 0x13e, 2, 242 },    { 0x13f, 2, 244 },    { 0x140, 2, 246 },    { 0x143, 2, 248 },    { 0x144, 2, 250 },    { 0x145, 2, 252 },    { 0x146, 2, 254 },
    { 0x147, 2, 256 },    { 0x148, 2, 258 },    { 0x149, 2, 260 },    { 0x14c, 2, 262 },    { 0x14d, 2, 264 },    { 0x14e, 2, 266 },    { 0x14f, 2, 268 },    { 0x150, 2, 270 },    { 0x151, 2, 272 },    { 0x154, 2, 274 },
    { 0x155, 2, 276 },    { 0x156, 2, 278 },    { 0x157, 2, 280 },    { 0x158, 2, 282 },    { 0x159, 2, 284 },    { 0x15a, 2, 286 },    { 0x15b, 2, 288 },    { 0x15c, 2, 290 },    { 0x15d, 2, 292 },    { 0x15e, 2, 294 },
    { 0x15f, 2, 296 },    { 0x160, 2, 298 },    { 0x161, 2, 300 },    { 0x162, 2, 302 },    { 0x163, 2, 304 },    { 0x164, 2, 306 },    { 0x165, 2, 308 },    { 0x168, 2, 310 },    { 0x169, 2, 312 },    { 0x16a, 2, 314 },
    { 0x16b, 2, 316 },    { 0x16c, 2, 318 },    { 0x16d, 2, 320 },    { 0x16e, 2, 322 },    { 0x16f, 2, 324 },    { 0x170, 2, 326 },    { 0x171, 2, 328 },    { 0x172, 2, 330 },    { 0x173, 2, 332 },    { 0x174, 2, 334 },
    { 0x175, 2, 336 },    { 0x176, 2, 338 },    { 0x177, 2, 340 },    { 0x178, 2, 342 },    { 0x179, 2, 344 },    { 0x17a, 2, 346 },    { 0x17b, 2, 348 },    { 0x17c, 2, 350 },    { 0x17d, 2, 352 },    { 0x17e, 2, 354 },
    { 0x17f, 1, 356 },    { 0x1a0, 2, 357 },    { 0x1a1, 2, 359 },    { 0x1af, 2, 361 },    { 0x1b0, 2, 363 },    { 0x1c4, 3, 365 },    { 0x1c5, 3, 368 },    { 0x1c6, 3, 371 },    { 0x1c7, 2, 374 },    { 0x1c8, 2, 376 },
    { 0x1c9, 2, 378 },    { 0x1ca, 2, 380 },    { 0x1cb, 2, 382 },    { 0x1cc, 2, 384 },    { 0x1cd, 2, 386 },    { 0x1ce, 2, 388 },    { 0x1cf, 2, 390 },    { 0x1d0, 2, 392 },    { 0x1d1, 2, 394 },    { 0x1d2, 2, 396 },
    { 0x1d3, 2, 398 },    { 0x1d4, 2, 400 },    { 0x1d5, 3, 402 },    { 0x1d6, 3, 405 },    { 0x1d7, 3, 408 },    { 0x1d8, 3, 411 },    { 0x1d9, 3, 414 },    { 0x1da, 3, 417 },    { 0x1db, 3, 420 },    { 0x1dc, 3, 423 },
    { 0x1de, 3, 426 },    { 0x1df, 3, 429 },    { 0x1e0, 3, 432 },    { 0x1e1, 3, 435 },    { 0x1e2, 2, 438 },    { 0x1e3, 2, 440 },    { 0x1e6, 2, 442 },    { 0x1e7, 2, 444 },    { 0x1e8, 2, 446 },    { 0x1e9, 2, 448 },
    { 0x1ea, 2, 450 },    { 0x1eb, 2, 452 },    { 0x1ec, 3, 454 },    { 0x1ed, 3, 457 },    { 0x1ee, 2, 460 },    { 0x1ef, 2, 462 },    { 0x1f0, 2, 464 },    { 0x1f1, 2, 466 },    { 0x1f2, 2, 468 },    { 0x1f3, 2, 470 },
    { 0x1f4, 2, 472 },    { 0x1f5, 2, 474 },    { 0x1f8, 2, 476 },    { 0x1f9, 2, 478 },    { 0x1fa, 3, 480 },    { 0x1fb, 3, 483 },    { 0x1fc, 2, 486 },    { 0x1fd, 2, 488 },    { 0x1fe, 2, 490 },    { 0x1ff, 2, 492 },
    { 0x200, 2, 494 },    { 0x201, 2, 496 },    { 0x202, 2, 498 },    { 0x203, 2, 500 },    { 0x204, 2, 502 },    { 0x205, 2, 504 },    { 0x206, 2, 506 },    { 0x207, 2, 508 },    { 0x208, 2, 510 },    { 0x209, 2, 512 },
    { 0x20a, 2, 514 },    { 0x20b, 2, 516 },    { 0x20c, 2, 518 },    { 0x20d, 2, 520 },    { 0x20e, 2, 522 },    { 0x20f, 2, 524 },    { 0x210, 2, 526 },    { 0x211, 2, 528 },    { 0x212, 2, 530 },    { 0x213, 2, 532 },
    { 0x214, 2, 534 },    { 0x215, 2, 536 },    { 0x216, 2, 538 },    { 0x217, 2, 540 },    { 0x218, 2, 542 },    { 0x219, 2, 544 },    { 0x21a, 2, 546 },    { 0x21b, 2, 548 },    { 0x21e, 2, 550 },    { 0x21f, 2, 552 },
    { 0x226, 2, 554 },    { 0x227, 2, 556 },    { 0x228, 2, 558 },    { 0x229, 2, 560 },    { 0x22a, 3, 562 },    { 0x22b, 3, 565 },    { 0x22c, 3, 568 },    { 0x22d, 3, 571 },    { 0x22e, 2, 574 },    { 0x22f, 2, 576 },
    { 0x230, 3, 578 },    { 0x231, 3, 581 },    { 0x232, 2, 584 },    { 0x233, 2, 586 },    { 0x2b0, 1, 588 },    { 0x2b1, 1, 589 },    { 0x2b2, 1, 590 },    { 0x2b3, 1, 591 },    { 0x2b4, 1, 592 },    { 0x2b5, 1, 593 },
    { 0x2b6, 1, 594 },    { 0x2b7, 1, 595 },    { 0x2b8, 1, 596 },    { 0x2d8, 2, 597 },    { 0x2d9, 2, 599 },    { 0x2da, 2, 601 },    { 0x2db, 2, 603 },    { 0x2dc, 2, 605 },    { 0x2dd, 2, 607 },    { 0x2e0, 1, 609 },
    { 0x2e1, 1, 610 },    { 0x2e2, 1, 356 },    { 0x2e3, 1, 611 },    { 0x2e4, 1, 612 },    { 0x340, 1, 613 },    { 0x341, 1, 614 },    { 0x343, 1, 615 },    { 0x344, 2, 616 },    { 0x374, 1, 618 },    { 0x37a, 2, 619 },
    { 0x37e, 1, 621 },    { 0x384, 2, 8 },      { 0x385, 3, 622 },    { 0x386, 2, 625 },    { 0x387, 1, 627 },    { 0x388, 2, 628 },    { 0x389, 2, 630 },    { 0x38a, 2, 632 },    { 0x38c, 2, 634 },    { 0x38e, 2, 636 },
    { 0x38f, 2, 638 },    { 0x390, 3, 640 },    { 0x3aa, 2, 643 },    { 0x3ab, 2, 645 },    { 0x3ac, 2, 647 },    { 0x3ad, 2, 649 },    { 0x3ae, 2, 651 },    { 0x3af, 2, 653 },    { 0x3b0, 3, 655 },    { 0x3ca, 2, 658 },
    { 0x3cb, 2, 660 },    { 0x3cc, 2, 662 },    { 0x3cd, 2, 664 },    { 0x3ce, 2, 666 },    { 0x3d0, 1, 668 },    { 0x3d1, 1, 669 },    { 0x3d2, 1, 670 },    { 0x3d3, 2, 636 },    { 0x3d4, 2, 645 },    { 0x3d5, 1, 671 },
    { 0x3d6, 1, 672 },    { 0x3f0, 1, 673 },    { 0x3f1, 1, 674 },    { 0x3f2, 1, 675 },    { 0x3f4, 1, 676 },    { 0x3f5, 1, 677 },    { 0x3f9, 1, 678 },    { 0x400, 2, 679 },    { 0x401, 2, 681 },    { 0x403, 2, 683 },
    { 0x407, 2, 685 },    { 0x40c, 2, 687 },    { 0x40d, 2, 689 },    { 0x40e, 2, 691 },    { 0x419, 2, 693 },    { 0x439, 2, 695 },    { 0x450, 2, 697 },    { 0x451, 2, 699 },    { 0x453, 2, 701 },    { 0x457, 2, 703 },
    { 0x45c, 2, 705 },    { 0x45d, 2, 707 },    { 0x45e, 2, 709 },    { 0x476, 2, 711 },    { 0x477, 2, 713 },    { 0x4c1, 2, 715 },    { 0x4c2, 2, 717 },    { 0x4d0, 2, 719 },    { 0x4d1, 2, 721 },    { 0x4d2, 2, 723 },
    { 0x4d3, 2, 725 },    { 0x4d6, 2, 727 },    { 0x4d7, 2, 729 },    { 0x4da, 2, 731 },    { 0x4db, 2, 733 },    { 0x4dc, 2, 735 },    { 0x4dd, 2, 737 },    { 0x4de, 2, 739 },    { 0x4df, 2, 741 },    { 0x4e2, 2, 743 },
    { 0x4e3, 2, 745 },    { 0x4e4, 2, 747 },    { 0x4e5, 2, 749 },    { 0x4e6, 2, 751 },    { 0x4e7, 2, 753 },    { 0x4ea, 2, 755 },    { 0x4eb, 2, 757 },    { 0x4ec, 2, 759 },    { 0x4ed, 2, 761 },    { 0x4ee, 2, 763 },
    { 0x4ef, 2, 765 },    { 0x4f0, 2, 767 },    { 0x4f1, 2, 769 },    { 0x4f2, 2, 771 },    { 0x4f3, 2, 773 },    { 0x4f4, 2, 775 },    { 0x4f5, 2, 777 },    { 0x4f8, 2, 779 },    { 0x4f9, 2, 781 },    { 0x587, 2, 783 },
    { 0x622, 2, 785 },    { 0x623, 2, 787 },    { 0x624, 2, 789 },    { 0x625, 2, 791 },    { 0x626, 2, 793 },    { 0x675, 2, 795 },    { 0x676, 2, 797 },    { 0x677, 2, 799 },    { 0x678, 2, 801 },    { 0x6c0, 2, 803 },
    { 0x6c2, 2, 805 },    { 0x6d3, 2, 807 },    { 0x929, 2, 809 },    { 0x931, 2, 811 },    { 0x934, 2, 813 },    { 0x958, 2, 815 },    { 0x959, 2, 817 },    { 0x95a, 2, 819 },    { 0x95b, 2, 821 },    { 0x95c, 2, 823 },
    { 0x95d, 2, 825 },    { 0x95e, 2, 827 },    { 0x95f, 2, 829 },    { 0x9cb, 2, 831 },    { 0x9cc, 2, 833 },    { 0x9dc, 2, 835 },    { 0x9dd, 2, 837 },    { 0x9df, 2, 839 },    { 0xa33, 2, 841 },    { 0xa36, 2, 843 },
    { 0xa59, 2, 845 },    { 0xa5a, 2, 847 },    { 0xa5b, 2, 849 },    { 0xa5e, 2, 851 },    { 0xb48, 2, 853 },    { 0xb4b, 2, 855 },    { 0xb4c, 2, 857 },    { 0xb5c, 2, 859 },    { 0xb5d, 2, 861 },    { 0xb94, 2, 863 },
    { 0xbca, 2, 865 },    { 0xbcb, 2, 867 },    { 0xbcc, 2, 869 },    { 0xc48, 2, 871 },    { 0xcc0, 2, 873 },    { 0xcc7, 2, 875 },    { 0xcc8, 2, 877 },    { 0xcca, 2, 879 },    { 0xccb, 3, 881 },    { 0xd4a, 2, 884 },
    { 0xd4b, 2, 886 },    { 0xd4c, 2, 888 },    { 0xdda, 2, 890 },    { 0xddc, 2, 892 },    { 0xddd, 3, 894 },    { 0xdde, 2, 897 },    { 0xe33, 2, 899 },    { 0xeb3, 2, 901 },    { 0xedc, 2, 903 },    { 0xedd, 2, 905 },
    { 0xf0c, 1, 907 },    { 0xf43, 2, 908 },    { 0xf4d, 2, 910 },    { 0xf52, 2, 912 },    { 0xf57, 2, 914 },    { 0xf5c, 2, 916 },    { 0xf69, 2, 918 },    { 0xf73, 2, 920 },    { 0xf75, 2, 922 },    { 0xf76, 2, 924 },
    { 0xf77, 3, 926 },    { 0xf78, 2, 929 },    { 0xf79, 3, 931 },    { 0xf81, 2, 934 },    { 0xf93, 2, 936 },    { 0xf9d, 2, 938 },    { 0xfa2, 2, 940 },    { 0xfa7, 2, 942 },    { 0xfac, 2, 944 },    { 0xfb9, 2, 946 },
    { 0x1026, 2, 948 },   { 0x10fc, 1, 950 },   { 0x1b06, 2, 951 },   { 0x1b08, 2, 953 },   { 0x1b0a, 2, 955 },   { 0x1b0c, 2, 957 },   { 0x1b0e, 2, 959 },   { 0x1b12, 2, 961 },   { 0x1b3b, 2, 963 },   { 0x1b3d, 2, 965 },
    { 0x1b40, 2, 967 },   { 0x1b41, 2, 969 },   { 0x1b43, 2, 971 },   { 0x1d2c, 1, 973 },   { 0x1d2d, 1, 974 },   { 0x1d2e, 1, 975 },   { 0x1d30, 1, 976 },   { 0x1d31, 1, 977 },   { 0x1d32, 1, 978 },   { 0x1d33, 1, 979 },
    { 0x1d34, 1, 980 },   { 0x1d35, 1, 981 },   { 0x1d36, 1, 982 },   { 0x1d37, 1, 983 },   { 0x1d38, 1, 984 },   { 0x1d39, 1, 985 },   { 0x1d3a, 1, 986 },   { 0x1d3c, 1, 987 },   { 0x1d3d, 1, 988 },   { 0x1d3e, 1, 989 },
    { 0x1d3f, 1, 990 },   { 0x1d40, 1, 991 },   { 0x1d41, 1, 992 },   { 0x1d42, 1, 993 },   { 0x1d43, 1, 3 },     { 0x1d44, 1, 994 },   { 0x1d45, 1, 995 },   { 0x1d46, 1, 996 },   { 0x1d47, 1, 997 },   { 0x1d48, 1, 998 },
    { 0x1d49, 1, 999 },   { 0x1d4a, 1, 1000 },  { 0x1d4b, 1, 1001 },  { 0x1d4c, 1, 1002 },  { 0x1d4d, 1, 1003 },  { 0x1d4f, 1, 1004 },  { 0x1d50, 1, 1005 },  { 0x1d51, 1, 1006 },  { 0x1d52, 1, 14 },    { 0x1d53, 1, 1007 },
    { 0x1d54, 1, 1008 },  { 0x1d55, 1, 1009 },  { 0x1d56, 1, 1010 },  { 0x1d57, 1, 1011 },  { 0x1d58, 1, 1012 },  { 0x1d59, 1, 1013 },  { 0x1d5a, 1, 1014 },  { 0x1d5b, 1, 1015 },  { 0x1d5c, 1, 1016 },  { 0x1d5d, 1, 668 },
    { 0x1d5e, 1, 1017 },  { 0x1d5f, 1, 1018 },  { 0x1d60, 1, 671 },   { 0x1d61, 1, 1019 },  { 0x1d62, 1, 1020 },  { 0x1d63, 1, 591 },   { 0x1d64, 1, 1012 },  { 0x1d65, 1, 1015 },  { 0x1d66, 1, 668 },   { 0x1d67, 1, 1017 },
    { 0x1d68, 1, 674 },   { 0x1d69, 1, 671 },   { 0x1d6a, 1, 1019 },  { 0x1d78, 1, 1021 },  { 0x1d9b, 1, 1022 },  { 0x1d9c, 1, 1023 },  { 0x1d9d, 1, 1024 },  { 0x1d9e, 1, 1025 },  { 0x1d9f, 1, 1002 },  { 0x1da0, 1, 1026 },
    { 0x1da1, 1, 1027 },  { 0x1da2, 1, 1028 },  { 0x1da3, 1, 1029 },  { 0x1da4, 1, 1030 },  { 0x1da5, 1, 1031 },  { 0x1da6, 1, 1032 },  { 0x1da7, 1, 1033 },  { 0x1da8, 1, 1034 },  { 0x1da9, 1, 1035 },  { 0x1daa, 1, 1036 },
    { 0x1dab, 1, 1037 },  { 0x1dac, 1, 1038 },  { 0x1dad, 1, 1039 },  { 0x1dae, 1, 1040 },  { 0x1daf, 1, 1041 },  { 0x1db0, 1, 1042 },  { 0x1db1, 1, 1043 },  { 0x1db2, 1, 1044 },  { 0x1db3, 1, 1045 },  { 0x1db4, 1, 1046 },
    { 0x1db5, 1, 1047 },  { 0x1db6, 1, 1048 },  { 0x1db7, 1, 1049 },  { 0x1db8, 1, 1050 },  { 0x1db9, 1, 1051 },  { 0x1dba, 1, 1052 },  { 0x1dbb, 1, 1053 },  { 0x1dbc, 1, 1054 },  { 0x1dbd, 1, 1055 },  { 0x1dbe, 1, 1056 },
    { 0x1dbf, 1, 669 },   { 0x1e00, 2, 1057 },  { 0x1e01, 2, 1059 },  { 0x1e02, 2, 1061 },  { 0x1e03, 2, 1063 },  { 0x1e04, 2, 1065 },  { 0x1e05, 2, 1067 },  { 0x1e06, 2, 1069 },  { 0x1e07, 2, 1071 },  { 0x1e08, 3, 1073 },
    { 0x1e09, 3, 1076 },  { 0x1e0a, 2, 1079 },  { 0x1e0b, 2, 1081 },  { 0x1e0c, 2, 1083 },  { 0x1e0d, 2, 1085 },  { 0x1e0e, 2, 1087 },  { 0x1e0f, 2, 1089 },  { 0x1e10, 2, 1091 },  { 0x1e11, 2, 1093 },  { 0x1e12, 2, 1095 },
    { 0x1e13, 2, 1097 },  { 0x1e14, 3, 1099 },  { 0x1e15, 3, 1102 },  { 0x1e16, 3, 1105 },  { 0x1e17, 3, 1108 },  { 0x1e18, 2, 1111 },  { 0x1e19, 2, 1113 },  { 0x1e1a, 2, 1115 },  { 0x1e1b, 2, 1117 },  { 0x1e1c, 3, 1119 },
    { 0x1e1d, 3, 1122 },  { 0x1e1e, 2, 1125 },  { 0x1e1f, 2, 1127 },  { 0x1e20, 2, 1129 },  { 0x1e21, 2, 1131 },  { 0x1e22, 2, 1133 },  { 0x1e23, 2, 1135 },  { 0x1e24, 2, 1137 },  { 0x1e25, 2, 1139 },  { 0x1e26, 2, 1141 },
    { 0x1e27, 2, 1143 },  { 0x1e28, 2, 1145 },  { 0x1e29, 2, 1147 },  { 0x1e2a, 2, 1149 },  { 0x1e2b, 2, 1151 },  { 0x1e2c, 2, 1153 },  { 0x1e2d, 2, 1155 },  { 0x1e2e, 3, 1157 },  { 0x1e2f, 3, 1160 },  { 0x1e30, 2, 1163 },
    { 0x1e31, 2, 1165 },  { 0x1e32, 2, 1167 },  { 0x1e33, 2, 1169 },  { 0x1e34, 2, 1171 },  { 0x1e35, 2, 1173 },  { 0x1e36, 2, 1175 },  { 0x1e37, 2, 1177 },  { 0x1e38, 3, 1179 },  { 0x1e39, 3, 1182 },  { 0x1e3a, 2, 1185 },
    { 0x1e3b, 2, 1187 },  { 0x1e3c, 2, 1189 },  { 0x1e3d, 2, 1191 },  { 0x1e3e, 2, 1193 },  { 0x1e3f, 2, 1195 },  { 0x1e40, 2, 1197 },  { 0x1e41, 2, 1199 },  { 0x1e42, 2, 1201 },  { 0x1e43, 2, 1203 },  { 0x1e44, 2, 1205 },
    { 0x1e45, 2, 1207 },  { 0x1e46, 2, 1209 },  { 0x1e47, 2, 1211 },  { 0x1e48, 2, 1213 },  { 0x1e49, 2, 1215 },  { 0x1e4a, 2, 1217 },  { 0x1e4b, 2, 1219 },  { 0x1e4c, 3, 1221 },  { 0x1e4d, 3, 1224 },  { 0x1e4e, 3, 1227 },
    { 0x1e4f, 3, 1230 },  { 0x1e50, 3, 1233 },  { 0x1e51, 3, 1236 },  { 0x1e52, 3, 1239 },  { 0x1e53, 3, 1242 },  { 0x1e54, 2, 1245 },  { 0x1e55, 2, 1247 },  { 0x1e56, 2, 1249 },  { 0x1e57, 2, 1251 },  { 0x1e58, 2, 1253 },
    { 0x1e59, 2, 1255 },  { 0x1e5a, 2, 1257 },  { 0x1e5b, 2, 1259 },  { 0x1e5c, 3, 1261 },  { 0x1e5d, 3, 1264 },  { 0x1e5e, 2, 1267 },  { 0x1e5f, 2, 1269 },  { 0x1e60, 2, 1271 },  { 0x1e61, 2, 1273 },  { 0x1e62, 2, 1275 },
    { 0x1e63, 2, 1277 },  { 0x1e64, 3, 1279 },  { 0x1e65, 3, 1282 },  { 0x1e66, 3, 1285 },  { 0x1e67, 3, 1288 },  { 0x1e68, 3, 1291 },  { 0x1e69, 3, 1294 },  { 0x1e6a, 2, 1297 },  { 0x1e6b, 2, 1299 },  { 0x1e6c, 2, 1301 },
    { 0x1e6d, 2, 1303 },  { 0x1e6e, 2, 1305 },  { 0x1e6f, 2, 1307 },  { 0x1e70, 2, 1309 },  { 0x1e71, 2, 1311 },  { 0x1e72, 2, 1313 },  { 0x1e73, 2, 1315 },  { 0x1e74, 2, 1317 },  { 0x1e75, 2, 1319 },  { 0x1e76, 2, 1321 },
    { 0x1e77, 2, 1323 },  { 0x1e78, 3, 1325 },  { 0x1e79, 3, 1328 },  { 0x1e7a, 3, 1331 },  { 0x1e7b, 3, 1334 },  { 0x1e7c, 2, 1337 },  { 0x1e7d, 2, 1339 },  { 0x1e7e, 2, 1341 },  { 0x1e7f, 2, 1343 },  { 0x1e80, 2, 1345 },
    { 0x1e81, 2, 1347 },  { 0x1e82, 2, 1349 },  { 0x1e83, 2, 1351 },  { 0x1e84, 2, 1353 },  { 0x1e85, 2, 1355 },  { 0x1e86, 2, 1357 },  { 0x1e87, 2, 1359 },  { 0x1e88, 2, 1361 },  { 0x1e89, 2, 1363 },  { 0x1e8a, 2, 1365 },
    { 0x1e8b, 2, 1367 },  { 0x1e8c, 2, 1369 },  { 0x1e8d, 2, 1371 },  { 0x1e8e, 2, 1373 },  { 0x1e8f, 2, 1375 },  { 0x1e90, 2, 1377 },  { 0x1e91, 2, 1379 },  { 0x1e92, 2, 1381 },  { 0x1e93, 2, 1383 },  { 0x1e94, 2, 1385 },
    { 0x1e95, 2, 1387 },  { 0x1e96, 2, 1389 },  { 0x1e97, 2, 1391 },  { 0x1e98, 2, 1393 },  { 0x1e99, 2, 1395 },  { 0x1e9a, 2, 1397 },  { 0x1e9b, 2, 1273 },  { 0x1ea0, 2, 1399 },  { 0x1ea1, 2, 1401 },  { 0x1ea2, 2, 1403 },
    { 0x1ea3, 2, 1405 },  { 0x1ea4, 3, 1407 },  { 0x1ea5, 3, 1410 },  { 0x1ea6, 3, 1413 },  { 0x1ea7, 3, 1416 },  { 0x1ea8, 3, 1419 },  { 0x1ea9, 3, 1422 },  { 0x1eaa, 3, 1425 },  { 0x1eab, 3, 1428 },  { 0x1eac, 3, 1431 },
    { 0x1ead, 3, 1434 },  { 0x1eae, 3, 1437 },  { 0x1eaf, 3, 1440 },  { 0x1eb0, 3, 1443 },  { 0x1eb1, 3, 1446 },  { 0x1eb2, 3, 1449 },  { 0x1eb3, 3, 1452 },  { 0x1eb4, 3, 1455 },  { 0x1eb5, 3, 1458 },  { 0x1eb6, 3, 1461 },
    { 0x1eb7, 3, 1464 },  { 0x1eb8, 2, 1467 },  { 0x1eb9, 2, 1469 },  { 0x1eba, 2, 1471 },  { 0x1ebb, 2, 1473 },  { 0x1ebc, 2, 1475 },  { 0x1ebd, 2, 1477 },  { 0x1ebe, 3, 1479 },  { 0x1ebf, 3, 1482 },  { 0x1ec0, 3, 1485 },
    { 0x1ec1, 3, 1488 },  { 0x1ec2, 3, 1491 },  { 0x1ec3, 3, 1494 },  { 0x1ec4, 3, 1497 },  { 0x1ec5, 3, 1500 },  { 0x1ec6, 3, 1503 },  { 0x1ec7, 3, 1506 },  { 0x1ec8, 2, 1509 },  { 0x1ec9, 2, 1511 },  { 0x1eca, 2, 1513 },
    { 0x1ecb, 2, 1515 },  { 0x1ecc, 2, 1517 },  { 0x1ecd, 2, 1519 },  { 0x1ece, 2, 1521 },  { 0x1ecf, 2, 1523 },  { 0x1ed0, 3, 1525 },  { 0x1ed1, 3, 1528 },  { 0x1ed2, 3, 1531 },  { 0x1ed3, 3, 1534 },  { 0x1ed4, 3, 1537 },
    { 0x1ed5, 3, 1540 },  { 0x1ed6, 3, 1543 },  { 0x1ed7, 3, 1546 },  { 0x1ed8, 3, 1549 },  { 0x1ed9, 3, 1552 },  { 0x1eda, 3, 1555 },  { 0x1edb, 3, 1558 },  { 0x1edc, 3, 1561 },  { 0x1edd, 3, 1564 },  { 0x1ede, 3, 1567 },
    { 0x1edf, 3, 1570 },  { 0x1ee0, 3, 1573 },  { 0x1ee1, 3, 1576 },  { 0x1ee2, 3, 1579 },  { 0x1ee3, 3, 1582 },  { 0x1ee4, 2, 1585 },  { 0x1ee5, 2, 1587 },  { 0x1ee6, 2, 1589 },  { 0x1ee7, 2, 1591 },  { 0x1ee8, 3, 1593 },
    { 0x1ee9, 3, 1596 },  { 0x1eea, 3, 1599 },  { 0x1eeb, 3, 1602 },  { 0x1eec, 3, 1605 },  { 0x1eed, 3, 1608 },  { 0x1eee, 3, 1611 },  { 0x1eef, 3, 1614 },  { 0x1ef0, 3, 1617 },  { 0x1ef1, 3, 1620 },  { 0x1ef2, 2, 1623 },
    { 0x1ef3, 2, 1625 },  { 0x1ef4, 2, 1627 },  { 0x1ef5, 2, 1629 },  { 0x1ef6, 2, 1631 },  { 0x1ef7, 2, 1633 },  { 0x1ef8, 2, 1635 },  { 0x1ef9, 2, 1637 },  { 0x1f00, 2, 1639 },  { 0x1f01, 2, 1641 },  { 0x1f02, 3, 1643 },
    { 0x1f03, 3, 1646 },  { 0x1f04, 3, 1649 },  { 0x1f05, 3, 1652 },  { 0x1f06, 3, 1655 },  { 0x1f07, 3, 1658 },  { 0x1f08, 2, 1661 },  { 0x1f09, 2, 1663 },  { 0x1f0a, 3, 1665 },  { 0x1f0b, 3, 1668 },  { 0x1f0c, 3, 1671 },
    { 0x1f0d, 3, 1674 },  { 0x1f0e, 3, 1677 },  { 0x1f0f, 3, 1680 },  { 0x1f10, 2, 1683 },  { 0x1f11, 2, 1685 },  { 0x1f12, 3, 1687 },  { 0x1f13, 3, 1690 },  { 0x1f14, 3, 1693 },  { 0x1f15, 3, 1696 },  { 0x1f18, 2, 1699 },
    { 0x1f19, 2, 1701 },  { 0x1f1a, 3, 1703 },  { 0x1f1b, 3, 1706 },  { 0x1f1c, 3, 1709 },  { 0x1f1d, 3, 1712 },  { 0x1f20, 2, 1715 },  { 0x1f21, 2, 1717 },  { 0x1f22, 3, 1719 },  { 0x1f23, 3, 1722 },  { 0x1f24, 3, 1725 },
    { 0x1f25, 3, 1728 },  { 0x1f26, 3, 1731 },  { 0x1f27, 3, 1734 },  { 0x1f28, 2, 1737 },  { 0x1f29, 2, 1739 },  { 0x1f2a, 3, 1741 },  { 0x1f2b, 3, 1744 },  { 0x1f2c, 3, 1747 },  { 0x1f2d, 3, 1750 },  { 0x1f2e, 3, 1753 },
    { 0x1f2f, 3, 1756 },  { 0x1f30, 2, 1759 },  { 0x1f31, 2, 1761 },  { 0x1f32, 3, 1763 },  { 0x1f33, 3, 1766 },  { 0x1f34, 3, 1769 },  { 0x1f35, 3, 1772 },  { 0x1f36, 3, 1775 },  { 0x1f37, 3, 1778 },  { 0x1f38, 2, 1781 },
    { 0x1f39, 2, 1783 },  { 0x1f3a, 3, 1785 },  { 0x1f3b, 3, 1788 },  { 0x1f3c, 3, 1791 },  { 0x1f3d, 3, 1794 },  { 0x1f3e, 3, 1797 },  { 0x1f3f, 3, 1800 },  { 0x1f40, 2, 1803 },  { 0x1f41, 2, 1805 },  { 0x1f42, 3, 1807 },
    { 0x1f43, 3, 1810 },  { 0x1f44, 3, 1813 },  { 0x1f45, 3, 1816 },  { 0x1f48, 2, 1819 },  { 0x1f49, 2, 1821 },  { 0x1f4a, 3, 1823 },  { 0x1f4b, 3, 1826 },  { 0x1f4c, 3, 1829 },  { 0x1f4d, 3, 1832 },  { 0x1f50, 2, 1835 },
    { 0x1f51, 2, 1837 },  { 0x1f52, 3, 1839 },  { 0x1f53, 3, 1842 },  { 0x1f54, 3, 1845 },  { 0x1f55, 3, 1848 },  { 0x1f56, 3, 1851 },  { 0x1f57, 3, 1854 },  { 0x1f59, 2, 1857 },  { 0x1f5b, 3, 1859 },  { 0x1f5d, 3, 1862 },
    { 0x1f5f, 3, 1865 },  { 0x1f60, 2, 1868 },  { 0x1f61, 2, 1870 },  { 0x1f62, 3, 1872 },  { 0x1f63, 3, 1875 },  { 0x1f64, 3, 1878 },  { 0x1f65, 3, 1881 },  { 0x1f66, 3, 1884 },  { 0x1f67, 3, 1887 },  { 0x1f68, 2, 1890 },
    { 0x1f69, 2, 1892 },  { 0x1f6a, 3, 1894 },  { 0x1f6b, 3, 1897 },  { 0x1f6c, 3, 1900 },  { 0x1f6d, 3, 1903 },  { 0x1f6e, 3, 1906 },  { 0x1f6f, 3, 1909 },  { 0x1f70, 2, 1912 },  { 0x1f71, 2, 647 },   { 0x1f72, 2, 1914 },
    { 0x1f73, 2, 649 },   { 0x1f74, 2, 1916 },  { 0x1f75, 2, 651 },   { 0x1f76, 2, 1918 },  { 0x1f77, 2, 653 },   { 0x1f78, 2, 1920 },  { 0x1f79, 2, 662 },   { 0x1f7a, 2, 1922 },  { 0x1f7b, 2, 664 },   { 0x1f7c, 2, 1924 },
    { 0x1f7d, 2, 666 },   { 0x1f80, 3, 1926 },  { 0x1f81, 3, 1929 },  { 0x1f82, 4, 1932 },  { 0x1f83, 4, 1936 },  { 0x1f84, 4, 1940 },  { 0x1f85, 4, 1944 },  { 0x1f86, 4, 1948 },  { 0x1f87, 4, 1952 },  { 0x1f88, 3, 1956 },
    { 0x1f89, 3, 1959 },  { 0x1f8a, 4, 1962 },  { 0x1f8b, 4, 1966 },  { 0x1f8c, 4, 1970 },  { 0x1f8d, 4, 1974 },  { 0x1f8e, 4, 1978 },  { 0x1f8f, 4, 1982 },  { 0x1f90, 3, 1986 },  { 0x1f91, 3, 1989 },  { 0x1f92, 4, 1992 },
    { 0x1f93, 4, 1996 },  { 0x1f94, 4, 2000 },  { 0x1f95, 4, 2004 },  { 0x1f96, 4, 2008 },  { 0x1f97, 4, 2012 },  { 0x1f98, 3, 2016 },  { 0x1f99, 3, 2019 },  { 0x1f9a, 4, 2022 },  { 0x1f9b, 4, 2026 },  { 0x1f9c, 4, 2030 },
    { 0x1f9d, 4, 2034 },  { 0x1f9e, 4, 2038 },  { 0x1f9f, 4, 2042 },  { 0x1fa0, 3, 2046 },  { 0x1fa1, 3, 2049 },  { 0x1fa2, 4, 2052 },  { 0x1fa3, 4, 2056 },  { 0x1fa4, 4, 2060 },  { 0x1fa5, 4, 2064 },  { 0x1fa6, 4, 2068 },
    { 0x1fa7, 4, 2072 },  { 0x1fa8, 3, 2076 },  { 0x1fa9, 3, 2079 },  { 0x1faa, 4, 2082 },  { 0x1fab, 4, 2086 },  { 0x1fac, 4, 2090 },  { 0x1fad, 4, 2094 },  { 0x1fae, 4, 2098 },  { 0x1faf, 4, 2102 },  { 0x1fb0, 2, 2106 },
    { 0x1fb1, 2, 2108 },  { 0x1fb2, 3, 2110 },  { 0x1fb3, 2, 2113 },  { 0x1fb4, 3, 2115 },  { 0x1fb6, 2, 2118 },  { 0x1fb7, 3, 2120 },  { 0x1fb8, 2, 2123 },  { 0x1fb9, 2, 2125 },  { 0x1fba, 2, 2127 },  { 0x1fbb, 2, 625 },
    { 0x1fbc, 2, 2129 },  { 0x1fbd, 2, 2131 },  { 0x1fbe, 1, 2133 },  { 0x1fbf, 2, 2131 },  { 0x1fc0, 2, 2134 },  { 0x1fc1, 3, 2136 },  { 0x1fc2, 3, 2139 },  { 0x1fc3, 2, 2142 },  { 0x1fc4, 3, 2144 },  { 0x1fc6, 2, 2147 },
    { 0x1fc7, 3, 2149 },  { 0x1fc8, 2, 2152 },  { 0x1fc9, 2, 628 },   { 0x1fca, 2, 2154 },  { 0x1fcb, 2, 630 },   { 0x1fcc, 2, 2156 },  { 0x1fcd, 3, 2158 },  { 0x1fce, 3, 2161 },  { 0x1fcf, 3, 2164 },  { 0x1fd0, 2, 2167 },
    { 0x1fd1, 2, 2169 },  { 0x1fd2, 3, 2171 },  { 0x1fd3, 3, 640 },   { 0x1fd6, 2, 2174 },  { 0x1fd7, 3, 2176 },  { 0x1fd8, 2, 2179 },  { 0x1fd9, 2, 2181 },  { 0x1fda, 2, 2183 },  { 0x1fdb, 2, 632 },   { 0x1fdd, 3, 2185 },
    { 0x1fde, 3, 2188 },  { 0x1fdf, 3, 2191 },  { 0x1fe0, 2, 2194 },  { 0x1fe1, 2, 2196 },  { 0x1fe2, 3, 2198 },  { 0x1fe3, 3, 655 },   { 0x1fe4, 2, 2201 },  { 0x1fe5, 2, 2203 },  { 0x1fe6, 2, 2205 },  { 0x1fe7, 3, 2207 },
    { 0x1fe8, 2, 2210 },  { 0x1fe9, 2, 2212 },  { 0x1fea, 2, 2214 },  { 0x1feb, 2, 636 },   { 0x1fec, 2, 2216 },  { 0x1fed, 3, 2218 },  { 0x1fee, 3, 622 },   { 0x1fef, 1, 2221 },  { 0x1ff2, 3, 2222 },  { 0x1ff3, 2, 2225 },
    { 0x1ff4, 3, 2227 },  { 0x1ff6, 2, 2230 },  { 0x1ff7, 3, 2232 },  { 0x1ff8, 2, 2235 },  { 0x1ff9, 2, 634 },   { 0x1ffa, 2, 2237 },  { 0x1ffb, 2, 638 },   { 0x1ffc, 2, 2239 },  { 0x1ffd, 2, 8 },     { 0x1ffe, 2, 2241 },
    { 0x2000, 1, 0 },     { 0x2001, 1, 0 },     { 0x2002, 1, 0 },     { 0x2003, 1, 0 },     { 0x2004, 1, 0 },     { 0x2005, 1, 0 },     { 0x2006, 1, 0 },     { 0x2007, 1, 0 },     { 0x2008, 1, 0 },     { 0x2009, 1, 0 },
    { 0x200a, 1, 0 },     { 0x2011, 1, 2243 },  { 0x2017, 2, 2244 },  { 0x2024, 1, 2246 },  { 0x2025, 2, 2247 },  { 0x2026, 3, 2249 },  { 0x202f, 1, 0 },     { 0x2033, 2, 2252 },  { 0x2034, 3, 2254 },  { 0x2036, 2, 2257 },
    { 0x2037, 3, 2259 },  { 0x203c, 2, 2262 },  { 0x203e, 2, 2264 },  { 0x2047, 2, 2266 },  { 0x2048, 2, 2268 },  { 0x2049, 2, 2270 },  { 0x2057, 4, 2272 },  { 0x205f, 1, 0 },     { 0x2070, 1, 2276 },  { 0x2071, 1, 1020 },
    { 0x2074, 1, 2277 },  { 0x2075, 1, 2278 },  { 0x2076, 1, 2279 },  { 0x2077, 1, 2280 },  { 0x2078, 1, 2281 },  { 0x2079, 1, 2282 },  { 0x207a, 1, 2283 },  { 0x207b, 1, 2284 },  { 0x207c, 1, 2285 },  { 0x207d, 1, 2286 },
    { 0x207e, 1, 2287 },  { 0x207f, 1, 2288 },  { 0x2080, 1, 2276 },  { 0x2081, 1, 13 },    { 0x2082, 1, 6 },     { 0x2083, 1, 7 },     { 0x2084, 1, 2277 },  { 0x2085, 1, 2278 },  { 0x2086, 1, 2279 },  { 0x2087, 1, 2280 },
    { 0x2088, 1, 2281 },  { 0x2089, 1, 2282 },  { 0x208a, 1, 2283 },  { 0x208b, 1, 2284 },  { 0x208c, 1, 2285 },  { 0x208d, 1, 2286 },  { 0x208e, 1, 2287 },  { 0x2090, 1, 3 },     { 0x2091, 1, 999 },   { 0x2092, 1, 14 },
    { 0x2093, 1, 611 },   { 0x2094, 1, 1000 },  { 0x2095, 1, 588 },   { 0x2096, 1, 1004 },  { 0x2097, 1, 610 },   { 0x2098, 1, 1005 },  { 0x2099, 1, 2288 },  { 0x209a, 1, 1010 },  { 0x209b, 1, 356 },   { 0x209c, 1, 1011 },
    { 0x20a8, 2, 2289 },  { 0x2100, 3, 2291 },  { 0x2101, 3, 2294 },  { 0x2102, 1, 2297 },  { 0x2103, 2, 2298 },  { 0x2105, 3, 2300 },  { 0x2106, 3, 2303 },  { 0x2107, 1, 2306 },  { 0x2109, 2, 2307 },  { 0x210a, 1, 1003 },
    { 0x210b, 1, 980 },   { 0x210c, 1, 980 },   { 0x210d, 1, 980 },   { 0x210e, 1, 588 },   { 0x210f, 1, 2309 },  { 0x2110, 1, 981 },   { 0x2111, 1, 981 },   { 0x2112, 1, 984 },   { 0x2113, 1, 610 },   { 0x2115, 1, 986 },
    { 0x2116, 2, 2310 },  { 0x2119, 1, 989 },   { 0x211a, 1, 2312 },  { 0x211b, 1, 990 },   { 0x211c, 1, 990 },   { 0x211d, 1, 990 },   { 0x2120, 2, 2313 },  { 0x2121, 3, 2315 },  { 0x2122, 2, 2318 },  { 0x2124, 1, 2320 },
    { 0x2126, 1, 2321 },  { 0x2128, 1, 2320 },  { 0x212a, 1, 983 },   { 0x212b, 2, 34 },    { 0x212c, 1, 975 },   { 0x212d, 1, 2297 },  { 0x212f, 1, 999 },   { 0x2130, 1, 977 },   { 0x2131, 1, 2322 },  { 0x2133, 1, 985 },
    { 0x2134, 1, 14 },    { 0x2135, 1, 2323 },  { 0x2136, 1, 2324 },  { 0x2137, 1, 2325 },  { 0x2138, 1, 2326 },  { 0x2139, 1, 1020 },  { 0x213b, 3, 2327 },  { 0x213c, 1, 672 },   { 0x213d, 1, 1017 },  { 0x213e, 1, 2330 },
    { 0x213f, 1, 2331 },  { 0x2140, 1, 2332 },  { 0x2145, 1, 976 },   { 0x2146, 1, 998 },   { 0x2147, 1, 999 },   { 0x2148, 1, 1020 },  { 0x2149, 1, 590 },   { 0x2150, 3, 2333 },  { 0x2151, 3, 2336 },  { 0x2152, 4, 2339 },
    { 0x2153, 3, 2343 },  { 0x2154, 3, 2346 },  { 0x2155, 3, 2349 },  { 0x2156, 3, 2352 },  { 0x2157, 3, 2355 },  { 0x2158, 3, 2358 },  { 0x2159, 3, 2361 },  { 0x215a, 3, 2364 },  { 0x215b, 3, 2367 },  { 0x215c, 3, 2370 },
    { 0x215d, 3, 2373 },  { 0x215e, 3, 2376 },  { 0x215f, 2, 2379 },  { 0x2160, 1, 981 },   { 0x2161, 2, 2381 },  { 0x2162, 3, 2383 },  { 0x2163, 2, 2386 },  { 0x2164, 1, 2388 },  { 0x2165, 2, 2389 },  { 0x2166, 3, 2391 },
    { 0x2167, 4, 2394 },  { 0x2168, 2, 2398 },  { 0x2169, 1, 2400 },  { 0x216a, 2, 2401 },  { 0x216b, 3, 2403 },  { 0x216c, 1, 984 },   { 0x216d, 1, 2297 },  { 0x216e, 1, 976 },   { 0x216f, 1, 985 },   { 0x2170, 1, 1020 },
    { 0x2171, 2, 2406 },  { 0x2172, 3, 2408 },  { 0x2173, 2, 2411 },  { 0x2174, 1, 1015 },  { 0x2175, 2, 2413 },  { 0x2176, 3, 2415 },  { 0x2177, 4, 2418 },  { 0x2178, 2, 2422 },  { 0x2179, 1, 611 },   { 0x217a, 2, 2424 },
    { 0x217b, 3, 2426 },  { 0x217c, 1, 610 },   { 0x217d, 1, 1023 },  { 0x217e, 1, 998 },   { 0x217f, 1, 1005 },  { 0x2189, 3, 2429 },  { 0x219a, 2, 2432 },  { 0x219b, 2, 2434 },  { 0x21ae, 2, 2436 },  { 0x21cd, 2, 2438 },
    { 0x21ce, 2, 2440 },  { 0x21cf, 2, 2442 },  { 0x2204, 2, 2444 },  { 0x2209, 2, 2446 },  { 0x220c, 2, 2448 },  { 0x2224, 2, 2450 },  { 0x2226, 2, 2452 },  { 0x222c, 2, 2454 },  { 0x222d, 3, 2456 },  { 0x222f, 2, 2459 },
    { 0x2230, 3, 2461 },  { 0x2241, 2, 2464 },  { 0x2244, 2, 2466 },  { 0x2247, 2, 2468 },  { 0x2249, 2, 2470 },  { 0x2260, 2, 2472 },  { 0x2262, 2, 2474 },  { 0x226d, 2, 2476 },  { 0x226e, 2, 2478 },  { 0x226f, 2, 2480 },
    { 0x2270, 2, 2482 },  { 0x2271, 2, 2484 },  { 0x2274, 2, 2486 },  { 0x2275, 2, 2488 },  { 0x2278, 2, 2490 },  { 0x2279, 2, 2492 },  { 0x2280, 2, 2494 },  { 0x2281, 2, 2496 },  { 0x2284, 2, 2498 },  { 0x2285, 2, 2500 },
    { 0x2288, 2, 2502 },  { 0x2289, 2, 2504 },  { 0x22ac, 2, 2506 },  { 0x22ad, 2, 2508 },  { 0x22ae, 2, 2510 },  { 0x22af, 2, 2512 },  { 0x22e0, 2, 2514 },  { 0x22e1, 2, 2516 },  { 0x22e2, 2, 2518 },  { 0x22e3, 2, 2520 },
    { 0x22ea, 2, 2522 },  { 0x22eb, 2, 2524 },  { 0x22ec, 2, 2526 },  { 0x22ed, 2, 2528 },  { 0x2329, 1, 2530 },  { 0x232a, 1, 2531 },  { 0x2460, 1, 13 },    { 0x2461, 1, 6 },     { 0x2462, 1, 7 },     { 0x2463, 1, 2277 },
    { 0x2464, 1, 2278 },  { 0x2465, 1, 2279 },  { 0x2466, 1, 2280 },  { 0x2467, 1, 2281 },  { 0x2468, 1, 2282 },  { 0x2469, 2, 2532 },  { 0x246a, 2, 2534 },  { 0x246b, 2, 2536 },  { 0x246c, 2, 2538 },  { 0x246d, 2, 2540 },
    { 0x246e, 2, 2542 },  { 0x246f, 2, 2544 },  { 0x2470, 2, 2546 },  { 0x2471, 2, 2548 },  { 0x2472, 2, 2550 },  { 0x2473, 2, 2552 },  { 0x2474, 3, 2554 },  { 0x2475, 3, 2557 },  { 0x2476, 3, 2560 },  { 0x2477, 3, 2563 },
    { 0x2478, 3, 2566 },  { 0x2479, 3, 2569 },  { 0x247a, 3, 2572 },  { 0x247b, 3, 2575 },  { 0x247c, 3, 2578 },  { 0x247d, 4, 2581 },  { 0x247e, 4, 2585 },  { 0x247f, 4, 2589 },  { 0x2480, 4, 2593 },  { 0x2481, 4, 2597 },
    { 0x2482, 4, 2601 },  { 0x2483, 4, 2605 },  { 0x2484, 4, 2609 },  { 0x2485, 4, 2613 },  { 0x2486, 4, 2617 },  { 0x2487, 4, 2621 },  { 0x2488, 2, 2625 },  { 0x2489, 2, 2627 },  { 0x248a, 2, 2629 },  { 0x248b, 2, 2631 },
    { 0x248c, 2, 2633 },  { 0x248d, 2, 2635 },  { 0x248e, 2, 2637 },  { 0x248f, 2, 2639 },  { 0x2490, 2, 2641 },  { 0x2491, 3, 2643 },  { 0x2492, 3, 2646 },  { 0x2493, 3, 2649 },  { 0x2494, 3, 2652 },  { 0x2495, 3, 2655 },
    { 0x2496, 3, 2658 },  { 0x2497, 3, 2661 },  { 0x2498, 3, 2664 },  { 0x2499, 3, 2667 },  { 0x249a, 3, 2670 },  { 0x249b, 3, 2673 },  { 0x249c, 3, 2676 },  { 0x249d, 3, 2679 },  { 0x249e, 3, 2682 },  { 0x249f, 3, 2685 },
    { 0x24a0, 3, 2688 },  { 0x24a1, 3, 2691 },  { 0x24a2, 3, 2694 },  { 0x24a3, 3, 2697 },  { 0x24a4, 3, 2700 },  { 0x24a5, 3, 2703 },  { 0x24a6, 3, 2706 },  { 0x24a7, 3, 2709 },  { 0x24a8, 3, 2712 },  { 0x24a9, 3, 2715 },
    { 0x24aa, 3, 2718 },  { 0x24ab, 3, 2721 },  { 0x24ac, 3, 2724 },  { 0x24ad, 3, 2727 },  { 0x24ae, 3, 2730 },  { 0x24af, 3, 2733 },  { 0x24b0, 3, 2736 },  { 0x24b1, 3, 2739 },  { 0x24b2, 3, 2742 },  { 0x24b3, 3, 2745 },
    { 0x24b4, 3, 2748 },  { 0x24b5, 3, 2751 },  { 0x24b6, 1, 973 },   { 0x24b7, 1, 975 },   { 0x24b8, 1, 2297 },  { 0x24b9, 1, 976 },   { 0x24ba, 1, 977 },   { 0x24bb, 1, 2322 },  { 0x24bc, 1, 979 },   { 0x24bd, 1, 980 },
    { 0x24be, 1, 981 },   { 0x24bf, 1, 982 },   { 0x24c0, 1, 983 },   { 0x24c1, 1, 984 },   { 0x24c2, 1, 985 },   { 0x24c3, 1, 986 },   { 0x24c4, 1, 987 },   { 0x24c5, 1, 989 },   { 0x24c6, 1, 2312 },  { 0x24c7, 1, 990 },
    { 0x24c8, 1, 2754 },  { 0x24c9, 1, 991 },   { 0x24ca, 1, 992 },   { 0x24cb, 1, 2388 },  { 0x24cc, 1, 993 },   { 0x24cd, 1, 2400 },  { 0x24ce, 1, 2755 },  { 0x24cf, 1, 2320 },  { 0x24d0, 1, 3 },     { 0x24d1, 1, 997 },
    { 0x24d2, 1, 1023 },  { 0x24d3, 1, 998 },   { 0x24d4, 1, 999 },   { 0x24d5, 1, 1026 },  { 0x24d6, 1, 1003 },  { 0x24d7, 1, 588 },   { 0x24d8, 1, 1020 },  { 0x24d9, 1, 590 },   { 0x24da, 1, 1004 },  { 0x24db, 1, 610 },
    { 0x24dc, 1, 1005 },  { 0x24dd, 1, 2288 },  { 0x24de, 1, 14 },    { 0x24df, 1, 1010 },  { 0x24e0, 1, 2756 },  { 0x24e1, 1, 591 },   { 0x24e2, 1, 356 },   { 0x24e3, 1, 1011 },  { 0x24e4, 1, 1012 },  { 0x24e5, 1, 1015 },
    { 0x24e6, 1, 595 },   { 0x24e7, 1, 611 },   { 0x24e8, 1, 596 },   { 0x24e9, 1, 1053 },  { 0x24ea, 1, 2276 },  { 0x2a0c, 4, 2757 },  { 0x2a74, 3, 2761 },  { 0x2a75, 2, 2764 },  { 0x2a76, 3, 2766 },  { 0x2adc, 2, 2769 },
    { 0x2c7c, 1, 590 },   { 0x2c7d, 1, 2388 },  { 0x2d6f, 1, 2771 },  { 0x2e9f, 1, 2772 },  { 0x2ef3, 1, 2773 },  { 0x2f00, 1, 2774 },  { 0x2f01, 1, 2775 },  { 0x2f02, 1, 2776 },  { 0x2f03, 1, 2777 },  { 0x2f04, 1, 2778 },
    { 0x2f05, 1, 2779 },  { 0x2f06, 1, 2780 },  { 0x2f07, 1, 2781 },  { 0x2f08, 1, 2782 },  { 0x2f09, 1, 2783 },  { 0x2f0a, 1, 2784 },  { 0x2f0b, 1, 2785 },  { 0x2f0c, 1, 2786 },  { 0x2f0d, 1, 2787 },  { 0x2f0e, 1, 2788 },
    { 0x2f0f, 1, 2789 },  { 0x2f10, 1, 2790 },  { 0x2f11, 1, 2791 },  { 0x2f12, 1, 2792 },  { 0x2f13, 1, 2793 },  { 0x2f14, 1, 2794 },  { 0x2f15, 1, 2795 },  { 0x2f16, 1, 2796 },  { 0x2f17, 1, 2797 },  { 0x2f18, 1, 2798 },
    { 0x2f19, 1, 2799 },  { 0x2f1a, 1, 2800 },  { 0x2f1b, 1, 2801 },  { 0x2f1c, 1, 2802 },  { 0x2f1d, 1, 2803 },  { 0x2f1e, 1, 2804 },  { 0x2f1f, 1, 2805 },  { 0x2f20, 1, 2806 },  { 0x2f21, 1, 2807 },  { 0x2f22, 1, 2808 },
    { 0x2f23, 1, 2809 },  { 0x2f24, 1, 2810 },  { 0x2f25, 1, 2811 },  { 0x2f26, 1, 2812 },  { 0x2f27, 1, 2813 },  { 0x2f28, 1, 2814 },  { 0x2f29, 1, 2815 },  { 0x2f2a, 1, 2816 },  { 0x2f2b, 1, 2817 },  { 0x2f2c, 1, 2818 },
    { 0x2f2d, 1, 2819 },  { 0x2f2e, 1, 2820 },  { 0x2f2f, 1, 2821 },  { 0x2f30, 1, 2822 },  { 0x2f31, 1, 2823 },  { 0x2f32, 1, 2824 },  { 0x2f33, 1, 2825 },  { 0x2f34, 1, 2826 },  { 0x2f35, 1, 2827 },  { 0x2f36, 1, 2828 },
    { 0x2f37, 1, 2829 },  { 0x2f38, 1, 2830 },  { 0x2f39, 1, 2831 },  { 0x2f3a, 1, 2832 },  { 0x2f3b, 1, 2833 },  { 0x2f3c, 1, 2834 },  { 0x2f3d, 1, 2835 },  { 0x2f3e, 1, 2836 },  { 0x2f3f, 1, 2837 },  { 0x2f40, 1, 2838 },
    { 0x2f41, 1, 2839 },  { 0x2f42, 1, 2840 },  { 0x2f43, 1, 2841 },  { 0x2f44, 1, 2842 },  { 0x2f45, 1, 2843 },  { 0x2f46, 1, 2844 },  { 0x2f47, 1, 2845 },  { 0x2f48, 1, 2846 },  { 0x2f49, 1, 2847 },  { 0x2f4a, 1, 2848 },
    { 0x2f4b, 1, 2849 },  { 0x2f4c, 1, 2850 },  { 0x2f4d, 1, 2851 },  { 0x2f4e, 1, 2852 },  { 0x2f4f, 1, 2853 },  { 0x2f50, 1, 2854 },  { 0x2f51, 1, 2855 },  { 0x2f52, 1, 2856 },  { 0x2f53, 1, 2857 },  { 0x2f54, 1, 2858 },
    { 0x2f55, 1, 2859 },  { 0x2f56, 1, 2860 },  { 0x2f57, 1, 2861 },  { 0x2f58, 1, 2862 },  { 0x2f59, 1, 2863 },  { 0x2f5a, 1, 2864 },  { 0x2f5b, 1, 2865 },  { 0x2f5c, 1, 2866 },  { 0x2f5d, 1, 2867 },  { 0x2f5e, 1, 2868 },
    { 0x2f5f, 1, 2869 },  { 0x2f60, 1, 2870 },  { 0x2f61, 1, 2871 },  { 0x2f62, 1, 2872 },  { 0x2f63, 1, 2873 },  { 0x2f64, 1, 2874 },  { 0x2f65, 1, 2875 },  { 0x2f66, 1, 2876 },  { 0x2f67, 1, 2877 },  { 0x2f68, 1, 2878 },
    { 0x2f69, 1, 2879 },  { 0x2f6a, 1, 2880 },  { 0x2f6b, 1, 2881 },  { 0x2f6c, 1, 2882 },  { 0x2f6d, 1, 2883 },  { 0x2f6e, 1, 2884 },  { 0x2f6f, 1, 2885 },  { 0x2f70, 1, 2886 },  { 0x2f71, 1, 2887 },  { 0x2f72, 1, 2888 },
    { 0x2f73, 1, 2889 },  { 0x2f74, 1, 2890 },  { 0x2f75, 1, 2891 },  { 0x2f76, 1, 2892 },  { 0x2f77, 1, 2893 },  { 0x2f78, 1, 2894 },  { 0x2f79, 1, 2895 },  { 0x2f7a, 1, 2896 },  { 0x2f7b, 1, 2897 },  { 0x2f7c, 1, 2898 },
    { 0x2f7d, 1, 2899 },  { 0x2f7e, 1, 2900 },  { 0x2f7f, 1, 2901 },  { 0x2f80, 1, 2902 },  { 0x2f81, 1, 2903 },  { 0x2f82, 1, 2904 },  { 0x2f83, 1, 2905 },  { 0x2f84, 1, 2906 },  { 0x2f85, 1, 2907 },  { 0x2f86, 1, 2908 },
    { 0x2f87, 1, 2909 },  { 0x2f88, 1, 2910 },  { 0x2f89, 1, 2911 },  { 0x2f8a, 1, 2912 },  { 0x2f8b, 1, 2913 },  { 0x2f8c, 1, 2914 },  { 0x2f8d, 1, 2915 },  { 0x2f8e, 1, 2916 },  { 0x2f8f, 1, 2917 },  { 0x2f90, 1, 2918 },
    { 0x2f91, 1, 2919 },  { 0x2f92, 1, 2920 },  { 0x2f93, 1, 2921 },  { 0x2f94, 1, 2922 },  { 0x2f95, 1, 2923 },  { 0x2f96, 1, 2924 },  { 0x2f97, 1, 2925 },  { 0x2f98, 1, 2926 },  { 0x2f99, 1, 2927 },  { 0x2f9a, 1, 2928 },
    { 0x2f9b, 1, 2929 },  { 0x2f9c, 1, 2930 },  { 0x2f9d, 1, 2931 },  { 0x2f9e, 1, 2932 },  { 0x2f9f, 1, 2933 },  { 0x2fa0, 1, 2934 },  { 0x2fa1, 1, 2935 },  { 0x2fa2, 1, 2936 },  { 0x2fa3, 1, 2937 },  { 0x2fa4, 1, 2938 },
    { 0x2fa5, 1, 2939 },  { 0x2fa6, 1, 2940 },  { 0x2fa7, 1, 2941 },  { 0x2fa8, 1, 2942 },  { 0x2fa9, 1, 2943 },  { 0x2faa, 1, 2944 },  { 0x2fab, 1, 2945 },  { 0x2fac, 1, 2946 },  { 0x2fad, 1, 2947 },  { 0x2fae, 1, 2948 },
    { 0x2faf, 1, 2949 },  { 0x2fb0, 1, 2950 },  { 0x2fb1, 1, 2951 },  { 0x2fb2, 1, 2952 },  { 0x2fb3, 1, 2953 },  { 0x2fb4, 1, 2954 },  { 0x2fb5, 1, 2955 },  { 0x2fb6, 1, 2956 },  { 0x2fb7, 1, 2957 },  { 0x2fb8, 1, 2958 },
    { 0x2fb9, 1, 2959 },  { 0x2fba, 1, 2960 },  { 0x2fbb, 1, 2961 },  { 0x2fbc, 1, 2962 },  { 0x2fbd, 1, 2963 },  { 0x2fbe, 1, 2964 },  { 0x2fbf, 1, 2965 },  { 0x2fc0, 1, 2966 },  { 0x2fc1, 1, 2967 },  { 0x2fc2, 1, 2968 },
    { 0x2fc3, 1, 2969 },  { 0x2fc4, 1, 2970 },  { 0x2fc5, 1, 2971 },  { 0x2fc6, 1, 2972 },  { 0x2fc7, 1, 2973 },  { 0x2fc8, 1, 2974 },  { 0x2fc9, 1, 2975 },  { 0x2fca, 1, 2976 },  { 0x2fcb, 1, 2977 },  { 0x2fcc, 1, 2978 },
    { 0x2fcd, 1, 2979 },  { 0x2fce, 1, 2980 },  { 0x2fcf, 1, 2981 },  { 0x2fd0, 1, 2982 },  { 0x2fd1, 1, 2983 },  { 0x2fd2, 1, 2984 },  { 0x2fd3, 1, 2985 },  { 0x2fd4, 1, 2986 },  { 0x2fd5, 1, 2987 },  { 0x3000, 1, 0 },
    { 0x3036, 1, 2988 },  { 0x3038, 1, 2797 },  { 0x3039, 1, 2989 },  { 0x303a, 1, 2990 },  { 0x304c, 2, 2991 },  { 0x304e, 2, 2993 },  { 0x3050, 2, 2995 },  { 0x3052, 2, 2997 },  { 0x3054, 2, 2999 },  { 0x3056, 2, 3001 },
    { 0x3058, 2, 3003 },  { 0x305a, 2, 3005 },  { 0x305c, 2, 3007 },  { 0x305e, 2, 3009 },  { 0x3060, 2, 3011 },  { 0x3062, 2, 3013 },  { 0x3065, 2, 3015 },  { 0x3067, 2, 3017 },  { 0x3069, 2, 3019 },  { 0x3070, 2, 3021 },
    { 0x3071, 2, 3023 },  { 0x3073, 2, 3025 },  { 0x3074, 2, 3027 },  { 0x3076, 2, 3029 },  { 0x3077, 2, 3031 },  { 0x3079, 2, 3033 },  { 0x307a, 2, 3035 },  { 0x307c, 2, 3037 },  { 0x307d, 2, 3039 },  { 0x3094, 2, 3041 },
    { 0x309b, 2, 3043 },  { 0x309c, 2, 3045 },  { 0x309e, 2, 3047 },  { 0x309f, 2, 3049 },  { 0x30ac, 2, 3051 },  { 0x30ae, 2, 3053 },  { 0x30b0, 2, 3055 },  { 0x30b2, 2, 3057 },  { 0x30b4, 2, 3059 },  { 0x30b6, 2, 3061 },
    { 0x30b8, 2, 3063 },  { 0x30ba, 2, 3065 },  { 0x30bc, 2, 3067 },  { 0x30be, 2, 3069 },  { 0x30c0, 2, 3071 },  { 0x30c2, 2, 3073 },  { 0x30c5, 2, 3075 },  { 0x30c7, 2, 3077 },  { 0x30c9, 2, 3079 },  { 0x30d0, 2, 3081 },
    { 0x30d1, 2, 3083 },  { 0x30d3, 2, 3085 },  { 0x30d4, 2, 3087 },  { 0x30d6, 2, 3089 },  { 0x30d7, 2, 3091 },  { 0x30d9, 2, 3093 },  { 0x30da, 2, 3095 },  { 0x30dc, 2, 3097 },  { 0x30dd, 2, 3099 },  { 0x30f4, 2, 3101 },
    { 0x30f7, 2, 3103 },  { 0x30f8, 2, 3105 },  { 0x30f9, 2, 3107 },  { 0x30fa, 2, 3109 },  { 0x30fe, 2, 3111 },  { 0x30ff, 2, 3113 },  { 0x3131, 1, 3115 },  { 0x3132, 1, 3116 },  { 0x3133, 1, 3117 },  { 0x3134, 1, 3118 },
    { 0x3135, 1, 3119 },  { 0x3136, 1, 3120 },  { 0x3137, 1, 3121 },  { 0x3138, 1, 3122 },  { 0x3139, 1, 3123 },  { 0x313a, 1, 3124 },  { 0x313b, 1, 3125 },  { 0x313c, 1, 3126 },  { 0x313d, 1, 3127 },  { 0x313e, 1, 3128 },
    { 0x313f, 1, 3129 },  { 0x3140, 1, 3130 },  { 0x3141, 1, 3131 },  { 0x3142, 1, 3132 },  { 0x3143, 1, 3133 },  { 0x3144, 1, 3134 },  { 0x3145, 1, 3135 },  { 0x3146, 1, 3136 },  { 0x3147, 1, 3137 },  { 0x3148, 1, 3138 },
    { 0x3149, 1, 3139 },  { 0x314a, 1, 3140 },  { 0x314b, 1, 3141 },  { 0x314c, 1, 3142 },  { 0x314d, 1, 3143 },  { 0x314e, 1, 3144 },  { 0x314f, 1, 3145 },  { 0x3150, 1, 3146 },  { 0x3151, 1, 3147 },  { 0x3152, 1, 3148 },
    { 0x3153, 1, 3149 },  { 0x3154, 1, 3150 },  { 0x3155, 1, 3151 },  { 0x3156, 1, 3152 },  { 0x3157, 1, 3153 },  { 0x3158, 1, 3154 },  { 0x3159, 1, 3155 },  { 0x315a, 1, 3156 },  { 0x315b, 1, 3157 },  { 0x315c, 1, 3158 },
    { 0x315d, 1, 3159 },  { 0x315e, 1, 3160 },  { 0x315f, 1, 3161 },  { 0x3160, 1, 3162 },  { 0x3161, 1, 3163 },  { 0x3162, 1, 3164 },  { 0x3163, 1, 3165 },  { 0x3164, 1, 3166 },  { 0x3165, 1, 3167 },  { 0x3166, 1, 3168 },
    { 0x3167, 1, 3169 },  { 0x3168, 1, 3170 },  { 0x3169, 1, 3171 },  { 0x316a, 1, 3172 },  { 0x316b, 1, 3173 },  { 0x316c, 1, 3174 },  { 0x316d, 1, 3175 },  { 0x316e, 1, 3176 },  { 0x316f, 1, 3177 },  { 0x3170, 1, 3178 },
    { 0x3171, 1, 3179 },  { 0x3172, 1, 3180 },  { 0x3173, 1, 3181 },  { 0x3174, 1, 3182 },  { 0x3175, 1, 3183 },  { 0x3176, 1, 3184 },  { 0x3177, 1, 3185 },  { 0x3178, 1, 3186 },  { 0x3179, 1, 3187 },  { 0x317a, 1, 3188 },
    { 0x317b, 1, 3189 },  { 0x317c, 1, 3190 },  { 0x317d, 1, 3191 },  { 0x317e, 1, 3192 },  { 0x317f, 1, 3193 },  { 0x3180, 1, 3194 },  { 0x3181, 1, 3195 },  { 0x3182, 1, 3196 },  { 0x3183, 1, 3197 },  { 0x3184, 1, 3198 },
    { 0x3185, 1, 3199 },  { 0x3186, 1, 3200 },  { 0x3187, 1, 3201 },  { 0x3188, 1, 3202 },  { 0x3189, 1, 3203 },  { 0x318a, 1, 3204 },  { 0x318b, 1, 3205 },  { 0x318c, 1, 3206 },  { 0x318d, 1, 3207 },  { 0x318e, 1, 3208 },
    { 0x3192, 1, 2774 },  { 0x3193, 1, 2780 },  { 0x3194, 1, 3209 },  { 0x3195, 1, 3210 },  { 0x3196, 1, 3211 },  { 0x3197, 1, 3212 },  { 0x3198, 1, 3213 },  { 0x3199, 1, 3214 },  { 0x319a, 1, 2778 },  { 0x319b, 1, 3215 },
    { 0x319c, 1, 3216 },  { 0x319d, 1, 3217 },  { 0x319e, 1, 3218 },  { 0x319f, 1, 2782 },  { 0x3200, 3, 3219 },  { 0x3201, 3, 3222 },  { 0x3202, 3, 3225 },  { 0x3203, 3, 3228 },  { 0x3204, 3, 3231 },  { 0x3205, 3, 3234 },
    { 0x3206, 3, 3237 },  { 0x3207, 3, 3240 },  { 0x3208, 3, 3243 },  { 0x3209, 3, 3246 },  { 0x320a, 3, 3249 },  { 0x320b, 3, 3252 },  { 0x320c, 3, 3255 },  { 0x320d, 3, 3258 },  { 0x320e, 4, 3261 },  { 0x320f, 4, 3265 },
    { 0x3210, 4, 3269 },  { 0x3211, 4, 3273 },  { 0x3212, 4, 3277 },  { 0x3213, 4, 3281 },  { 0x3214, 4, 3285 },  { 0x3215, 4, 3289 },  { 0x3216, 4, 3293 },  { 0x3217, 4, 3297 },  { 0x3218, 4, 3301 },  { 0x3219, 4, 3305 },
    { 0x321a, 4, 3309 },  { 0x321b, 4, 3313 },  { 0x321c, 4, 3317 },  { 0x321d, 7, 3321 },  { 0x321e, 6, 3328 },  { 0x3220, 3, 3334 },  { 0x3221, 3, 3337 },  { 0x3222, 3, 3340 },  { 0x3223, 3, 3343 },  { 0x3224, 3, 3346 },
    { 0x3225, 3, 3349 },  { 0x3226, 3, 3352 },  { 0x3227, 3, 3355 },  { 0x3228, 3, 3358 },  { 0x3229, 3, 3361 },  { 0x322a, 3, 3364 },  { 0x322b, 3, 3367 },  { 0x322c, 3, 3370 },  { 0x322d, 3, 3373 },  { 0x322e, 3, 3376 },
    { 0x322f, 3, 3379 },  { 0x3230, 3, 3382 },  { 0x3231, 3, 3385 },  { 0x3232, 3, 3388 },  { 0x3233, 3, 3391 },  { 0x3234, 3, 3394 },  { 0x3235, 3, 3397 },  { 0x3236, 3, 3400 },  { 0x3237, 3, 3403 },  { 0x3238, 3, 3406 },
    { 0x3239, 3, 3409 },  { 0x323a, 3, 3412 },  { 0x323b, 3, 3415 },  { 0x323c, 3, 3418 },  { 0x323d, 3, 3421 },  { 0x323e, 3, 3424 },  { 0x323f, 3, 3427 },  { 0x3240, 3, 3430 },  { 0x3241, 3, 3433 },  { 0x3242, 3, 3436 },
    { 0x3243, 3, 3439 },  { 0x3244, 1, 3442 },  { 0x3245, 1, 3443 },  { 0x3246, 1, 2840 },  { 0x3247, 1, 3444 },  { 0x3250, 3, 3445 },  { 0x3251, 2, 3448 },  { 0x3252, 2, 3450 },  { 0x3253, 2, 3452 },  { 0x3254, 2, 3454 },
    { 0x3255, 2, 3456 },  { 0x3256, 2, 3458 },  { 0x3257, 2, 3460 },  { 0x3258, 2, 3462 },  { 0x3259, 2, 3464 },  { 0x325a, 2, 3466 },  { 0x325b, 2, 3468 },  { 0x325c, 2, 3470 },  { 0x325d, 2, 3472 },  { 0x325e, 2, 3474 },
    { 0x325f, 2, 3476 },  { 0x3260, 1, 3115 },  { 0x3261, 1, 3118 },  { 0x3262, 1, 3121 },  { 0x3263, 1, 3123 },  { 0x3264, 1, 3131 },  { 0x3265, 1, 3132 },  { 0x3266, 1, 3135 },  { 0x3267, 1, 3137 },  { 0x3268, 1, 3138 },
    { 0x3269, 1, 3140 },  { 0x326a, 1, 3141 },  { 0x326b, 1, 3142 },  { 0x326c, 1, 3143 },  { 0x326d, 1, 3144 },  { 0x326e, 2, 3478 },  { 0x326f, 2, 3480 },  { 0x3270, 2, 3482 },  { 0x3271, 2, 3484 },  { 0x3272, 2, 3486 },
    { 0x3273, 2, 3488 },  { 0x3274, 2, 3490 },  { 0x3275, 2, 3492 },  { 0x3276, 2, 3494 },  { 0x3277, 2, 3496 },  { 0x3278, 2, 3498 },  { 0x3279, 2, 3500 },  { 0x327a, 2, 3502 },  { 0x327b, 2, 3504 },  { 0x327c, 5, 3506 },
    { 0x327d, 4, 3511 },  { 0x327e, 2, 3515 },  { 0x3280, 1, 2774 },  { 0x3281, 1, 2780 },  { 0x3282, 1, 3209 },  { 0x3283, 1, 3210 },  { 0x3284, 1, 3517 },  { 0x3285, 1, 3518 },  { 0x3286, 1, 3519 },  { 0x3287, 1, 2785 },
    { 0x3288, 1, 3520 },  { 0x3289, 1, 2797 },  { 0x328a, 1, 2847 },  { 0x328b, 1, 2859 },  { 0x328c, 1, 2858 },  { 0x328d, 1, 2848 },  { 0x328e, 1, 2940 },  { 0x328f, 1, 2805 },  { 0x3290, 1, 2845 },  { 0x3291, 1, 3521 },
    { 0x3292, 1, 3522 },  { 0x3293, 1, 3523 },  { 0x3294, 1, 3524 },  { 0x3295, 1, 3525 },  { 0x3296, 1, 3526 },  { 0x3297, 1, 3527 },  { 0x3298, 1, 3528 },  { 0x3299, 1, 3529 },  { 0x329a, 1, 3530 },  { 0x329b, 1, 2811 },
    { 0x329c, 1, 3531 },  { 0x329d, 1, 3532 },  { 0x329e, 1, 3533 },  { 0x329f, 1, 3534 },  { 0x32a0, 1, 3535 },  { 0x32a1, 1, 3536 },  { 0x32a2, 1, 3537 },  { 0x32a3, 1, 3538 },  { 0x32a4, 1, 3211 },  { 0x32a5, 1, 3212 },
    { 0x32a6, 1, 3213 },  { 0x32a7, 1, 3539 },  { 0x32a8, 1, 3540 },  { 0x32a9, 1, 3541 },  { 0x32aa, 1, 3542 },  { 0x32ab, 1, 3543 },  { 0x32ac, 1, 3544 },  { 0x32ad, 1, 3545 },  { 0x32ae, 1, 3546 },  { 0x32af, 1, 3547 },
    { 0x32b0, 1, 3548 },  { 0x32b1, 2, 3549 },  { 0x32b2, 2, 3551 },  { 0x32b3, 2, 3553 },  { 0x32b4, 2, 3555 },  { 0x32b5, 2, 3557 },  { 0x32b6, 2, 3559 },  { 0x32b7, 2, 3561 },  { 0x32b8, 2, 3563 },  { 0x32b9, 2, 3565 },
    { 0x32ba, 2, 3567 },  { 0x32bb, 2, 3569 },  { 0x32bc, 2, 3571 },  { 0x32bd, 2, 3573 },  { 0x32be, 2, 3575 },  { 0x32bf, 2, 3577 },  { 0x32c0, 2, 3579 },  { 0x32c1, 2, 3581 },  { 0x32c2, 2, 3583 },  { 0x32c3, 2, 3585 },
    { 0x32c4, 2, 3587 },  { 0x32c5, 2, 3589 },  { 0x32c6, 2, 3591 },  { 0x32c7, 2, 3593 },  { 0x32c8, 2, 3595 },  { 0x32c9, 3, 3597 },  { 0x32ca, 3, 3600 },  { 0x32cb, 3, 3603 },  { 0x32cc, 2, 3606 },  { 0x32cd, 3, 3608 },
    { 0x32ce, 2, 3611 },  { 0x32cf, 3, 3613 },  { 0x32d0, 1, 3616 },  { 0x32d1, 1, 3617 },  { 0x32d2, 1, 3618 },  { 0x32d3, 1, 3619 },  { 0x32d4, 1, 3620 },  { 0x32d5, 1, 3621 },  { 0x32d6, 1, 3622 },  { 0x32d7, 1, 3623 },
    { 0x32d8, 1, 3624 },  { 0x32d9, 1, 3625 },  { 0x32da, 1, 3626 },  { 0x32db, 1, 3627 },  { 0x32dc, 1, 3628 },  { 0x32dd, 1, 3629 },  { 0x32de, 1, 3630 },  { 0x32df, 1, 3631 },  { 0x32e0, 1, 3632 },  { 0x32e1, 1, 3633 },
    { 0x32e2, 1, 3634 },  { 0x32e3, 1, 3635 },  { 0x32e4, 1, 3636 },  { 0x32e5, 1, 3637 },  { 0x32e6, 1, 3638 },  { 0x32e7, 1, 3639 },  { 0x32e8, 1, 3640 },  { 0x32e9, 1, 3641 },  { 0x32ea, 1, 3642 },  { 0x32eb, 1, 3643 },
    { 0x32ec, 1, 3644 },  { 0x32ed, 1, 3645 },  { 0x32ee, 1, 3646 },  { 0x32ef, 1, 3647 },  { 0x32f0, 1, 3648 },  { 0x32f1, 1, 3649 },  { 0x32f2, 1, 3650 },  { 0x32f3, 1, 3651 },  { 0x32f4, 1, 3652 },  { 0x32f5, 1, 3653 },
    { 0x32f6, 1, 3654 },  { 0x32f7, 1, 3655 },  { 0x32f8, 1, 3656 },  { 0x32f9, 1, 3657 },  { 0x32fa, 1, 3658 },  { 0x32fb, 1, 3659 },  { 0x32fc, 1, 3660 },  { 0x32fd, 1, 3661 },  { 0x32fe, 1, 3662 },  { 0x3300, 5, 3663 },
    { 0x3301, 4, 3668 },  { 0x3302, 5, 3672 },  { 0x3303, 3, 3677 },  { 0x3304, 5, 3680 },  { 0x3305, 3, 3685 },  { 0x3306, 3, 3688 },  { 0x3307, 6, 3691 },  { 0x3308, 4, 3697 },  { 0x3309, 3, 3701 },  { 0x330a, 3, 3704 },
    { 0x330b, 3, 3707 },  { 0x330c, 4, 3710 },  { 0x330d, 4, 3714 },  { 0x330e, 4, 3718 },  { 0x330f, 4, 3722 },  { 0x3310, 4, 3726 },  { 0x3311, 4, 3730 },  { 0x3312, 4, 3734 },  { 0x3313, 6, 3738 },  { 0x3314, 2, 3744 },
    { 0x3315, 6, 3746 },  { 0x3316, 6, 3752 },  { 0x3317, 5, 3758 },  { 0x3318, 4, 3763 },  { 0x3319, 6, 3767 },  { 0x331a, 6, 3773 },  { 0x331b, 4, 3779 },  { 0x331c, 3, 3783 },  { 0x331d, 3, 3786 },  { 0x331e, 4, 3789 },
    { 0x331f, 4, 3793 },  { 0x3320, 5, 3797 },  { 0x3321, 5, 3802 },  { 0x3322, 3, 3807 },  { 0x3323, 3, 3810 },  { 0x3324, 4, 3813 },  { 0x3325, 3, 3817 },  { 0x3326, 3, 3820 },  { 0x3327, 2, 3823 },  { 0x3328, 2, 3825 },
    { 0x3329, 3, 3827 },  { 0x332a, 3, 3830 },  { 0x332b, 6, 3833 },  { 0x332c, 4, 3839 },  { 0x332d, 5, 3843 },  { 0x332e, 6, 3848 },  { 0x332f, 4, 3854 },  { 0x3330, 3, 3858 },  { 0x3331, 3, 3861 },  { 0x3332, 6, 3864 },
    { 0x3333, 4, 3870 },  { 0x3334, 6, 3874 },  { 0x3335, 3, 3880 },  { 0x3336, 5, 3883 },  { 0x3337, 3, 3888 },  { 0x3338, 4, 3891 },  { 0x3339, 3, 3895 },  { 0x333a, 4, 3898 },  { 0x333b, 5, 3902 },  { 0x333c, 4, 3907 },
    { 0x333d, 5, 3911 },  { 0x333e, 4, 3916 },  { 0x333f, 2, 3920 },  { 0x3340, 5, 3922 },  { 0x3341, 3, 3927 },  { 0x3342, 3, 3930 },  { 0x3343, 4, 3933 },  { 0x3344, 3, 3937 },  { 0x3345, 3, 3940 },  { 0x3346, 3, 3943 },
    { 0x3347, 5, 3946 },  { 0x3348, 4, 3951 },  { 0x3349, 2, 3955 },  { 0x334a, 6, 3957 },  { 0x334b, 3, 3963 },  { 0x334c, 5, 3966 },  { 0x334d, 4, 3971 },  { 0x334e, 4, 3975 },  { 0x334f, 3, 3979 },  { 0x3350, 3, 3982 },
    { 0x3351, 4, 3985 },  { 0x3352, 2, 3989 },  { 0x3353, 4, 3991 },  { 0x3354, 5, 3995 },  { 0x3355, 2, 4000 },  { 0x3356, 6, 4002 },  { 0x3357, 3, 4008 },  { 0x3358, 2, 4011 },  { 0x3359, 2, 4013 },  { 0x335a, 2, 4015 },
    { 0x335b, 2, 4017 },  { 0x335c, 2, 4019 },  { 0x335d, 2, 4021 },  { 0x335e, 2, 4023 },  { 0x335f, 2, 4025 },  { 0x3360, 2, 4027 },  { 0x3361, 2, 4029 },  { 0x3362, 3, 4031 },  { 0x3363, 3, 4034 },  { 0x3364, 3, 4037 },
    { 0x3365, 3, 4040 },  { 0x3366, 3, 4043 },  { 0x3367, 3, 4046 },  { 0x3368, 3, 4049 },  { 0x3369, 3, 4052 },  { 0x336a, 3, 4055 },  { 0x336b, 3, 4058 },  { 0x336c, 3, 4061 },  { 0x336d, 3, 4064 },  { 0x336e, 3, 4067 },
    { 0x336f, 3, 4070 },  { 0x3370, 3, 4073 },  { 0x3371, 3, 4076 },  { 0x3372, 2, 4079 },  { 0x3373, 2, 4081 },  { 0x3374, 3, 4083 },  { 0x3375, 2, 4086 },  { 0x3376, 2, 4088 },  { 0x3377, 2, 4090 },  { 0x3378, 3, 4092 },
    { 0x3379, 3, 4095 },  { 0x337a, 2, 4098 },  { 0x337b, 2, 4100 },  { 0x337c, 2, 4102 },  { 0x337d, 2, 4104 },  { 0x337e, 2, 4106 },  { 0x337f, 4, 4108 },  { 0x3380, 2, 4112 },  { 0x3381, 2, 4114 },  { 0x3382, 2, 4116 },
    { 0x3383, 2, 4118 },  { 0x3384, 2, 4120 },  { 0x3385, 2, 4122 },  { 0x3386, 2, 4124 },  { 0x3387, 2, 4126 },  { 0x3388, 3, 4128 },  { 0x3389, 4, 4131 },  { 0x338a, 2, 4135 },  { 0x338b, 2, 4137 },  { 0x338c, 2, 4139 },
    { 0x338d, 2, 4141 },  { 0x338e, 2, 4143 },  { 0x338f, 2, 4145 },  { 0x3390, 2, 4147 },  { 0x3391, 3, 4149 },  { 0x3392, 3, 4152 },  { 0x3393, 3, 4155 },  { 0x3394, 3, 4158 },  { 0x3395, 2, 4161 },  { 0x3396, 2, 4163 },
    { 0x3397, 2, 4165 },  { 0x3398, 2, 4167 },  { 0x3399, 2, 4169 },  { 0x339a, 2, 4171 },  { 0x339b, 2, 4173 },  { 0x339c, 2, 4175 },  { 0x339d, 2, 4177 },  { 0x339e, 2, 4179 },  { 0x339f, 3, 4181 },  { 0x33a0, 3, 4184 },
    { 0x33a1, 2, 4187 },  { 0x33a2, 3, 4189 },  { 0x33a3, 3, 4192 },  { 0x33a4, 3, 4195 },  { 0x33a5, 2, 4198 },  { 0x33a6, 3, 4200 },  { 0x33a7, 3, 4203 },  { 0x33a8, 4, 4206 },  { 0x33a9, 2, 4210 },  { 0x33aa, 3, 4212 },
    { 0x33ab, 3, 4215 },  { 0x33ac, 3, 4218 },  { 0x33ad, 3, 4221 },  { 0x33ae, 5, 4224 },  { 0x33af, 6, 4229 },  { 0x33b0, 2, 4235 },  { 0x33b1, 2, 4237 },  { 0x33b2, 2, 4239 },  { 0x33b3, 2, 4241 },  { 0x33b4, 2, 4243 },
    { 0x33b5, 2, 4245 },  { 0x33b6, 2, 4247 },  { 0x33b7, 2, 4249 },  { 0x33b8, 2, 4251 },  { 0x33b9, 2, 4253 },  { 0x33ba, 2, 4255 },  { 0x33bb, 2, 4257 },  { 0x33bc, 2, 4259 },  { 0x33bd, 2, 4261 },  { 0x33be, 2, 4263 },
    { 0x33bf, 2, 4265 },  { 0x33c0, 2, 4267 },  { 0x33c1, 2, 4269 },  { 0x33c2, 4, 4271 },  { 0x33c3, 2, 4275 },  { 0x33c4, 2, 4277 },  { 0x33c5, 2, 4279 },  { 0x33c6, 4, 4281 },  { 0x33c7, 3, 4285 },  { 0x33c8, 2, 4288 },
    { 0x33c9, 2, 4290 },  { 0x33ca, 2, 4292 },  { 0x33cb, 2, 4294 },  { 0x33cc, 2, 4296 },  { 0x33cd, 2, 4298 },  { 0x33ce, 2, 4300 },  { 0x33cf, 2, 4302 },  { 0x33d0, 2, 4304 },  { 0x33d1, 2, 4306 },  { 0x33d2, 3, 4308 },
    { 0x33d3, 2, 4311 },  { 0x33d4, 2, 4313 },  { 0x33d5, 3, 4315 },  { 0x33d6, 3, 4318 },  { 0x33d7, 2, 4321 },  { 0x33d8, 4, 4323 },  { 0x33d9, 3, 4327 },  { 0x33da, 2, 4330 },  { 0x33db, 2, 4332 },  { 0x33dc, 2, 4334 },
    { 0x33dd, 2, 4336 },  { 0x33de, 3, 4338 },  { 0x33df, 3, 4341 },  { 0x33e0, 2, 4344 },  { 0x33e1, 2, 4346 },  { 0x33e2, 2, 4348 },  { 0x33e3, 2, 4350 },  { 0x33e4, 2, 4352 },  { 0x33e5, 2, 4354 },  { 0x33e6, 2, 4356 },
    { 0x33e7, 2, 4358 },  { 0x33e8, 2, 4360 },  { 0x33e9, 3, 4362 },  { 0x33ea, 3, 4365 },  { 0x33eb, 3, 4368 },  { 0x33ec, 3, 4371 },  { 0x33ed, 3, 4374 },  { 0x33ee, 3, 4377 },  { 0x33ef, 3, 4380 },  { 0x33f0, 3, 4383 },
    { 0x33f1, 3, 4386 },  { 0x33f2, 3, 4389 },  { 0x33f3, 3, 4392 },  { 0x33f4, 3, 4395 },  { 0x33f5, 3, 4398 },  { 0x33f6, 3, 4401 },  { 0x33f7, 3, 4404 },  { 0x33f8, 3, 4407 },  { 0x33f9, 3, 4410 },  { 0x33fa, 3, 4413 },
    { 0x33fb, 3, 4416 },  { 0x33fc, 3, 4419 },  { 0x33fd, 3, 4422 },  { 0x33fe, 3, 4425 },  { 0x33ff, 3, 4428 },  { 0xa69c, 1, 4431 },  { 0xa69d, 1, 4432 },  { 0xa770, 1, 4433 },  { 0xa7f8, 1, 4434 },  { 0xa7f9, 1, 4435 },
    { 0xab5c, 1, 4436 },  { 0xab5d, 1, 4437 },  { 0xab5e, 1, 4438 },  { 0xab5f, 1, 4439 },  { 0xf900, 1, 4440 },  { 0xf901, 1, 4441 },  { 0xf902, 1, 2932 },  { 0xf903, 1, 4442 },  { 0xf904, 1, 4443 },  { 0xf905, 1, 4444 },
    { 0xf906, 1, 4445 },  { 0xf907, 1, 2986 },  { 0xf908, 1, 2986 },  { 0xf909, 1, 4446 },  { 0xf90a, 1, 2940 },  { 0xf90b, 1, 4447 },  { 0xf90c, 1, 4448 },  { 0xf90d, 1, 4449 },  { 0xf90e, 1, 4450 },  { 0xf90f, 1, 4451 },
    { 0xf910, 1, 4452 },  { 0xf911, 1, 4453 },  { 0xf912, 1, 4454 },  { 0xf913, 1, 4455 },  { 0xf914, 1, 4456 },  { 0xf915, 1, 4457 },  { 0xf916, 1, 4458 },  { 0xf917, 1, 4459 },  { 0xf918, 1, 4460 },  { 0xf919, 1, 4461 },
    { 0xf91a, 1, 4462 },  { 0xf91b, 1, 4463 },  { 0xf91c, 1, 4464 },  { 0xf91d, 1, 4465 },  { 0xf91e, 1, 4466 },  { 0xf91f, 1, 4467 },  { 0xf920, 1, 4468 },  { 0xf921, 1, 4469 },  { 0xf922, 1, 4470 },  { 0xf923, 1, 4471 },
    { 0xf924, 1, 4472 },  { 0xf925, 1, 4473 },  { 0xf926, 1, 4474 },  { 0xf927, 1, 4475 },  { 0xf928, 1, 4476 },  { 0xf929, 1, 4477 },  { 0xf92a, 1, 4478 },  { 0xf92b, 1, 4479 },  { 0xf92c, 1, 4480 },  { 0xf92d, 1, 4481 },
    { 0xf92e, 1, 4482 },  { 0xf92f, 1, 4483 },  { 0xf930, 1, 4484 },  { 0xf931, 1, 4485 },  { 0xf932, 1, 4486 },  { 0xf933, 1, 4487 },  { 0xf934, 1, 2898 },  { 0xf935, 1, 4488 },  { 0xf936, 1, 4489 },  { 0xf937, 1, 4490 },
    { 0xf938, 1, 4491 },  { 0xf939, 1, 4492 },  { 0xf93a, 1, 4493 },  { 0xf93b, 1, 4494 },  { 0xf93c, 1, 4495 },  { 0xf93d, 1, 4496 },  { 0xf93e, 1, 4497 },  { 0xf93f, 1, 4498 },  { 0xf940, 1, 2971 },  { 0xf941, 1, 4499 },
    { 0xf942, 1, 4500 },  { 0xf943, 1, 4501 },  { 0xf944, 1, 4502 },  { 0xf945, 1, 4503 },  { 0xf946, 1, 4504 },  { 0xf947, 1, 4505 },  { 0xf948, 1, 4506 },  { 0xf949, 1, 4507 },  { 0xf94a, 1, 4508 },  { 0xf94b, 1, 4509 },
    { 0xf94c, 1, 4510 },  { 0xf94d, 1, 4511 },  { 0xf94e, 1, 4512 },  { 0xf94f, 1, 4513 },  { 0xf950, 1, 4514 },  { 0xf951, 1, 4515 },  { 0xf952, 1, 4516 },  { 0xf953, 1, 4517 },  { 0xf954, 1, 4518 },  { 0xf955, 1, 4519 },
    { 0xf956, 1, 4520 },  { 0xf957, 1, 4521 },  { 0xf958, 1, 4522 },  { 0xf959, 1, 4523 },  { 0xf95a, 1, 4524 },  { 0xf95b, 1, 4525 },  { 0xf95c, 1, 4456 },  { 0xf95d, 1, 4526 },  { 0xf95e, 1, 4527 },  { 0xf95f, 1, 4528 },
    { 0xf960, 1, 4529 },  { 0xf961, 1, 4530 },  { 0xf962, 1, 4531 },  { 0xf963, 1, 4532 },  { 0xf964, 1, 4533 },  { 0xf965, 1, 4534 },  { 0xf966, 1, 4535 },  { 0xf967, 1, 4536 },  { 0xf968, 1, 4537 },  { 0xf969, 1, 4538 },
    { 0xf96a, 1, 4539 },  { 0xf96b, 1, 4540 },  { 0xf96c, 1, 4541 },  { 0xf96d, 1, 4542 },  { 0xf96e, 1, 4543 },  { 0xf96f, 1, 4544 },  { 0xf970, 1, 4545 },  { 0xf971, 1, 2934 },  { 0xf972, 1, 4546 },  { 0xf973, 1, 4547 },
    { 0xf974, 1, 4548 },  { 0xf975, 1, 4549 },  { 0xf976, 1, 4550 },  { 0xf977, 1, 4551 },  { 0xf978, 1, 4552 },  { 0xf979, 1, 4553 },  { 0xf97a, 1, 4554 },  { 0xf97b, 1, 4555 },  { 0xf97c, 1, 4556 },  { 0xf97d, 1, 4557 },
    { 0xf97e, 1, 4558 },  { 0xf97f, 1, 4559 },  { 0xf980, 1, 4560 },  { 0xf981, 1, 2811 },  { 0xf982, 1, 4561 },  { 0xf983, 1, 4562 },  { 0xf984, 1, 4563 },  { 0xf985, 1, 4564 },  { 0xf986, 1, 4565 },  { 0xf987, 1, 4566 },
    { 0xf988, 1, 4567 },  { 0xf989, 1, 4568 },  { 0xf98a, 1, 2792 },  { 0xf98b, 1, 4569 },  { 0xf98c, 1, 4570 },  { 0xf98d, 1, 4571 },  { 0xf98e, 1, 4572 },  { 0xf98f, 1, 4573 },  { 0xf990, 1, 4574 },  { 0xf991, 1, 4575 },
    { 0xf992, 1, 4576 },  { 0xf993, 1, 4577 },  { 0xf994, 1, 4578 },  { 0xf995, 1, 4579 },  { 0xf996, 1, 4580 },  { 0xf997, 1, 4581 },  { 0xf998, 1, 4582 },  { 0xf999, 1, 4583 },  { 0xf99a, 1, 4584 },  { 0xf99b, 1, 4585 },
    { 0xf99c, 1, 4586 },  { 0xf99d, 1, 4587 },  { 0xf99e, 1, 4588 },  { 0xf99f, 1, 4589 },  { 0xf9a0, 1, 4590 },  { 0xf9a1, 1, 4544 },  { 0xf9a2, 1, 4591 },  { 0xf9a3, 1, 4592 },  { 0xf9a4, 1, 4593 },  { 0xf9a5, 1, 4594 },
    { 0xf9a6, 1, 4595 },  { 0xf9a7, 1, 4596 },  { 0xf9a8, 1, 4597 },  { 0xf9a9, 1, 4598 },  { 0xf9aa, 1, 4528 },  { 0xf9ab, 1, 4599 },  { 0xf9ac, 1, 4600 },  { 0xf9ad, 1, 4601 },  { 0xf9ae, 1, 4602 },  { 0xf9af, 1, 4603 },
    { 0xf9b0, 1, 4604 },  { 0xf9b1, 1, 4605 },  { 0xf9b2, 1, 4606 },  { 0xf9b3, 1, 4607 },  { 0xf9b4, 1, 4608 },  { 0xf9b5, 1, 4609 },  { 0xf9b6, 1, 4610 },  { 0xf9b7, 1, 4611 },  { 0xf9b8, 1, 4612 },  { 0xf9b9, 1, 4613 },
    { 0xf9ba, 1, 4614 },  { 0xf9bb, 1, 4615 },  { 0xf9bc, 1, 4616 },  { 0xf9bd, 1, 4617 },  { 0xf9be, 1, 4618 },  { 0xf9bf, 1, 4456 },  { 0xf9c0, 1, 4619 },  { 0xf9c1, 1, 4620 },  { 0xf9c2, 1, 4621 },  { 0xf9c3, 1, 4622 },
    { 0xf9c4, 1, 2985 },  { 0xf9c5, 1, 4623 },  { 0xf9c6, 1, 4624 },  { 0xf9c7, 1, 4625 },  { 0xf9c8, 1, 4626 },  { 0xf9c9, 1, 4627 },  { 0xf9ca, 1, 4628 },  { 0xf9cb, 1, 4629 },  { 0xf9cc, 1, 4630 },  { 0xf9cd, 1, 4631 },
    { 0xf9ce, 1, 4632 },  { 0xf9cf, 1, 4633 },  { 0xf9d0, 1, 4634 },  { 0xf9d1, 1, 3518 },  { 0xf9d2, 1, 4635 },  { 0xf9d3, 1, 4636 },  { 0xf9d4, 1, 4637 },  { 0xf9d5, 1, 4638 },  { 0xf9d6, 1, 4639 },  { 0xf9d7, 1, 4640 },
    { 0xf9d8, 1, 4641 },  { 0xf9d9, 1, 4642 },  { 0xf9da, 1, 4643 },  { 0xf9db, 1, 4530 },  { 0xf9dc, 1, 4644 },  { 0xf9dd, 1, 4645 },  { 0xf9de, 1, 4646 },  { 0xf9df, 1, 4647 },  { 0xf9e0, 1, 4648 },  { 0xf9e1, 1, 4649 },
    { 0xf9e2, 1, 4650 },  { 0xf9e3, 1, 4651 },  { 0xf9e4, 1, 4652 },  { 0xf9e5, 1, 4653 },  { 0xf9e6, 1, 4654 },  { 0xf9e7, 1, 4655 },  { 0xf9e8, 1, 4656 },  { 0xf9e9, 1, 2939 },  { 0xf9ea, 1, 4657 },  { 0xf9eb, 1, 4658 },
    { 0xf9ec, 1, 4659 },  { 0xf9ed, 1, 4660 },  { 0xf9ee, 1, 4661 },  { 0xf9ef, 1, 4662 },  { 0xf9f0, 1, 4663 },  { 0xf9f1, 1, 4664 },  { 0xf9f2, 1, 4665 },  { 0xf9f3, 1, 4666 },  { 0xf9f4, 1, 4667 },  { 0xf9f5, 1, 4668 },
    { 0xf9f6, 1, 4669 },  { 0xf9f7, 1, 2890 },  { 0xf9f8, 1, 4670 },  { 0xf9f9, 1, 4671 },  { 0xf9fa, 1, 4672 },  { 0xf9fb, 1, 4673 },  { 0xf9fc, 1, 4674 },  { 0xf9fd, 1, 4675 },  { 0xf9fe, 1, 4676 },  { 0xf9ff, 1, 4677 },
    { 0xfa00, 1, 4678 },  { 0xfa01, 1, 4679 },  { 0xfa02, 1, 4680 },  { 0xfa03, 1, 4681 },  { 0xfa04, 1, 4682 },  { 0xfa05, 1, 4683 },  { 0xfa06, 1, 4684 },  { 0xfa07, 1, 4685 },  { 0xfa08, 1, 2917 },  { 0xfa09, 1, 4686 },
    { 0xfa0a, 1, 2920 },  { 0xfa0b, 1, 4687 },  { 0xfa0c, 1, 4688 },  { 0xfa0d, 1, 4689 },  { 0xfa10, 1, 4690 },  { 0xfa12, 1, 4691 },  { 0xfa15, 1, 4692 },  { 0xfa16, 1, 4693 },  { 0xfa17, 1, 4694 },  { 0xfa18, 1, 4695 },
    { 0xfa19, 1, 4696 },  { 0xfa1a, 1, 4697 },  { 0xfa1b, 1, 4698 },  { 0xfa1c, 1, 4699 },  { 0xfa1d, 1, 4700 },  { 0xfa1e, 1, 2897 },  { 0xfa20, 1, 4701 },  { 0xfa22, 1, 4702 },  { 0xfa25, 1, 4703 },  { 0xfa26, 1, 4704 },
    { 0xfa2a, 1, 4705 },  { 0xfa2b, 1, 4706 },  { 0xfa2c, 1, 4707 },  { 0xfa2d, 1, 4708 },  { 0xfa2e, 1, 4709 },  { 0xfa2f, 1, 4710 },  { 0xfa30, 1, 4711 },  { 0xfa31, 1, 4712 },  { 0xfa32, 1, 4713 },  { 0xfa33, 1, 4714 },
    { 0xfa34, 1, 4715 },  { 0xfa35, 1, 4716 },  { 0xfa36, 1, 4717 },  { 0xfa37, 1, 4718 },  { 0xfa38, 1, 4719 },  { 0xfa39, 1, 4720 },  { 0xfa3a, 1, 4721 },  { 0xfa3b, 1, 4722 },  { 0xfa3c, 1, 2818 },  { 0xfa3d, 1, 4723 },
    { 0xfa3e, 1, 4724 },  { 0xfa3f, 1, 4725 },  { 0xfa40, 1, 4726 },  { 0xfa41, 1, 4727 },  { 0xfa42, 1, 4728 },  { 0xfa43, 1, 4729 },  { 0xfa44, 1, 4730 },  { 0xfa45, 1, 4731 },  { 0xfa46, 1, 4732 },  { 0xfa47, 1, 4733 },
    { 0xfa48, 1, 4734 },  { 0xfa49, 1, 4735 },  { 0xfa4a, 1, 4736 },  { 0xfa4b, 1, 4737 },  { 0xfa4c, 1, 3523 },  { 0xfa4d, 1, 4738 },  { 0xfa4e, 1, 4739 },  { 0xfa4f, 1, 4740 },  { 0xfa50, 1, 4741 },  { 0xfa51, 1, 3527 },
    { 0xfa52, 1, 4742 },  { 0xfa53, 1, 4743 },  { 0xfa54, 1, 4744 },  { 0xfa55, 1, 4745 },  { 0xfa56, 1, 4746 },  { 0xfa57, 1, 4580 },  { 0xfa58, 1, 4747 },  { 0xfa59, 1, 4748 },  { 0xfa5a, 1, 4749 },  { 0xfa5b, 1, 4750 },
    { 0xfa5c, 1, 4751 },  { 0xfa5d, 1, 4752 },  { 0xfa5e, 1, 4752 },  { 0xfa5f, 1, 4753 },  { 0xfa60, 1, 4754 },  { 0xfa61, 1, 4755 },  { 0xfa62, 1, 4756 },  { 0xfa63, 1, 4757 },  { 0xfa64, 1, 4758 },  { 0xfa65, 1, 4759 },
    { 0xfa66, 1, 4760 },  { 0xfa67, 1, 4703 },  { 0xfa68, 1, 4761 },  { 0xfa69, 1, 4762 },  { 0xfa6a, 1, 4763 },  { 0xfa6b, 1, 4764 },  { 0xfa6c, 1, 4765 },  { 0xfa6d, 1, 4766 },  { 0xfa70, 1, 4767 },  { 0xfa71, 1, 4768 },
    { 0xfa72, 1, 4769 },  { 0xfa73, 1, 4770 },  { 0xfa74, 1, 4771 },  { 0xfa75, 1, 4772 },  { 0xfa76, 1, 4773 },  { 0xfa77, 1, 4774 },  { 0xfa78, 1, 4717 },  { 0xfa79, 1, 4775 },  { 0xfa7a, 1, 4776 },  { 0xfa7b, 1, 4777 },
    { 0xfa7c, 1, 4690 },  { 0xfa7d, 1, 4778 },  { 0xfa7e, 1, 4779 },  { 0xfa7f, 1, 4780 },  { 0xfa80, 1, 4781 },  { 0xfa81, 1, 4782 },  { 0xfa82, 1, 4783 },  { 0xfa83, 1, 4784 },  { 0xfa84, 1, 4785 },  { 0xfa85, 1, 4786 },
    { 0xfa86, 1, 4787 },  { 0xfa87, 1, 4788 },  { 0xfa88, 1, 4789 },  { 0xfa89, 1, 4725 },  { 0xfa8a, 1, 4790 },  { 0xfa8b, 1, 4726 },  { 0xfa8c, 1, 4791 },  { 0xfa8d, 1, 4792 },  { 0xfa8e, 1, 4793 },  { 0xfa8f, 1, 4794 },
    { 0xfa90, 1, 4795 },  { 0xfa91, 1, 4691 },  { 0xfa92, 1, 4477 },  { 0xfa93, 1, 4796 },  { 0xfa94, 1, 4797 },  { 0xfa95, 1, 2851 },  { 0xfa96, 1, 4545 },  { 0xfa97, 1, 4628 },  { 0xfa98, 1, 4798 },  { 0xfa99, 1, 4799 },
    { 0xfa9a, 1, 4733 },  { 0xfa9b, 1, 4800 },  { 0xfa9c, 1, 4734 },  { 0xfa9d, 1, 4801 },  { 0xfa9e, 1, 4802 },  { 0xfa9f, 1, 4803 },  { 0xfaa0, 1, 4693 },  { 0xfaa1, 1, 4804 },  { 0xfaa2, 1, 4805 },  { 0xfaa3, 1, 4806 },
    { 0xfaa4, 1, 4807 },  { 0xfaa5, 1, 4808 },  { 0xfaa6, 1, 4694 },  { 0xfaa7, 1, 4809 },  { 0xfaa8, 1, 4810 },  { 0xfaa9, 1, 4811 },  { 0xfaaa, 1, 4812 },  { 0xfaab, 1, 4813 },  { 0xfaac, 1, 4814 },  { 0xfaad, 1, 4746 },
    { 0xfaae, 1, 4815 },  { 0xfaaf, 1, 4816 },  { 0xfab0, 1, 4580 },  { 0xfab1, 1, 4817 },  { 0xfab2, 1, 4750 },  { 0xfab3, 1, 4818 },  { 0xfab4, 1, 4819 },  { 0xfab5, 1, 4820 },  { 0xfab6, 1, 4821 },  { 0xfab7, 1, 4822 },
    { 0xfab8, 1, 4755 },  { 0xfab9, 1, 4823 },  { 0xfaba, 1, 4702 },  { 0xfabb, 1, 4824 },  { 0xfabc, 1, 4756 },  { 0xfabd, 1, 4526 },  { 0xfabe, 1, 4825 },  { 0xfabf, 1, 4757 },  { 0xfac0, 1, 4826 },  { 0xfac1, 1, 4759 },
    { 0xfac2, 1, 4827 },  { 0xfac3, 1, 4828 },  { 0xfac4, 1, 4829 },  { 0xfac5, 1, 4830 },  { 0xfac6, 1, 4831 },  { 0xfac7, 1, 4761 },  { 0xfac8, 1, 4699 },  { 0xfac9, 1, 4832 },  { 0xfaca, 1, 4762 },  { 0xfacb, 1, 4833 },
    { 0xfacc, 1, 4763 },  { 0xfacd, 1, 4834 },  { 0xface, 1, 2986 },  { 0xfacf, 1, 4835 },  { 0xfad0, 1, 4836 },  { 0xfad1, 1, 4837 },  { 0xfad2, 1, 4838 },  { 0xfad3, 1, 4839 },  { 0xfad4, 1, 4840 },  { 0xfad5, 1, 4841 },
    { 0xfad6, 1, 4842 },  { 0xfad7, 1, 4843 },  { 0xfad8, 1, 4844 },  { 0xfad9, 1, 4845 },  { 0xfb00, 2, 4846 },  { 0xfb01, 2, 4848 },  { 0xfb02, 2, 4850 },  { 0xfb03, 3, 4852 },  { 0xfb04, 3, 4855 },  { 0xfb05, 2, 4858 },
    { 0xfb06, 2, 4858 },  { 0xfb13, 2, 4860 },  { 0xfb14, 2, 4862 },  { 0xfb15, 2, 4864 },  { 0xfb16, 2, 4866 },  { 0xfb17, 2, 4868 },  { 0xfb1d, 2, 4870 },  { 0xfb1f, 2, 4872 },  { 0xfb20, 1, 4874 },  { 0xfb21, 1, 2323 },
    { 0xfb22, 1, 2326 },  { 0xfb23, 1, 4875 },  { 0xfb24, 1, 4876 },  { 0xfb25, 1, 4877 },  { 0xfb26, 1, 4878 },  { 0xfb27, 1, 4879 },  { 0xfb28, 1, 4880 },  { 0xfb29, 1, 2283 },  { 0xfb2a, 2, 4881 },  { 0xfb2b, 2, 4883 },
    { 0xfb2c, 3, 4885 },  { 0xfb2d, 3, 4888 },  { 0xfb2e, 2, 4891 },  { 0xfb2f, 2, 4893 },  { 0xfb30, 2, 4895 },  { 0xfb31, 2, 4897 },  { 0xfb32, 2, 4899 },  { 0xfb33, 2, 4901 },  { 0xfb34, 2, 4903 },  { 0xfb35, 2, 4905 },
    { 0xfb36, 2, 4907 },  { 0xfb38, 2, 4909 },  { 0xfb39, 2, 4911 },  { 0xfb3a, 2, 4913 },  { 0xfb3b, 2, 4915 },  { 0xfb3c, 2, 4917 },  { 0xfb3e, 2, 4919 },  { 0xfb40, 2, 4921 },  { 0xfb41, 2, 4923 },  { 0xfb43, 2, 4925 },
    { 0xfb44, 2, 4927 },  { 0xfb46, 2, 4929 },  { 0xfb47, 2, 4931 },  { 0xfb48, 2, 4933 },  { 0xfb49, 2, 4935 },  { 0xfb4a, 2, 4937 },  { 0xfb4b, 2, 4939 },  { 0xfb4c, 2, 4941 },  { 0xfb4d, 2, 4943 },  { 0xfb4e, 2, 4945 },
    { 0xfb4f, 2, 4947 },  { 0xfb50, 1, 4949 },  { 0xfb51, 1, 4949 },  { 0xfb52, 1, 4950 },  { 0xfb53, 1, 4950 },  { 0xfb54, 1, 4950 },  { 0xfb55, 1, 4950 },  { 0xfb56, 1, 4951 },  { 0xfb57, 1, 4951 },  { 0xfb58, 1, 4951 },
    { 0xfb59, 1, 4951 },  { 0xfb5a, 1, 4952 },  { 0xfb5b, 1, 4952 },  { 0xfb5c, 1, 4952 },  { 0xfb5d, 1, 4952 },  { 0xfb5e, 1, 4953 },  { 0xfb5f, 1, 4953 },  { 0xfb60, 1, 4953 },  { 0xfb61, 1, 4953 },  { 0xfb62, 1, 4954 },
    { 0xfb63, 1, 4954 },  { 0xfb64, 1, 4954 },  { 0xfb65, 1, 4954 },  { 0xfb66, 1, 4955 },  { 0xfb67, 1, 4955 },  { 0xfb68, 1, 4955 },  { 0xfb69, 1, 4955 },  { 0xfb6a, 1, 4956 },  { 0xfb6b, 1, 4956 },  { 0xfb6c, 1, 4956 },
    { 0xfb6d, 1, 4956 },  { 0xfb6e, 1, 4957 },  { 0xfb6f, 1, 4957 },  { 0xfb70, 1, 4957 },  { 0xfb71, 1, 4957 },  { 0xfb72, 1, 4958 },  { 0xfb73, 1, 4958 },  { 0xfb74, 1, 4958 },  { 0xfb75, 1, 4958 },  { 0xfb76, 1, 4959 },
    { 0xfb77, 1, 4959 },  { 0xfb78, 1, 4959 },  { 0xfb79, 1, 4959 },  { 0xfb7a, 1, 4960 },  { 0xfb7b, 1, 4960 },  { 0xfb7c, 1, 4960 },  { 0xfb7d, 1, 4960 },  { 0xfb7e, 1, 4961 },  { 0xfb7f, 1, 4961 },  { 0xfb80, 1, 4961 },
    { 0xfb81, 1, 4961 },  { 0xfb82, 1, 4962 },  { 0xfb83, 1, 4962 },  { 0xfb84, 1, 4963 },  { 0xfb85, 1, 4963 },  { 0xfb86, 1, 4964 },  { 0xfb87, 1, 4964 },  { 0xfb88, 1, 4965 },  { 0xfb89, 1, 4965 },  { 0xfb8a, 1, 4966 },
    { 0xfb8b, 1, 4966 },  { 0xfb8c, 1, 4967 },  { 0xfb8d, 1, 4967 },  { 0xfb8e, 1, 4968 },  { 0xfb8f, 1, 4968 },  { 0xfb90, 1, 4968 },  { 0xfb91, 1, 4968 },  { 0xfb92, 1, 4969 },  { 0xfb93, 1, 4969 },  { 0xfb94, 1, 4969 },
    { 0xfb95, 1, 4969 },  { 0xfb96, 1, 4970 },  { 0xfb97, 1, 4970 },  { 0xfb98, 1, 4970 },  { 0xfb99, 1, 4970 },  { 0xfb9a, 1, 4971 },  { 0xfb9b, 1, 4971 },  { 0xfb9c, 1, 4971 },  { 0xfb9d, 1, 4971 },  { 0xfb9e, 1, 4972 },
    { 0xfb9f, 1, 4972 },  { 0xfba0, 1, 4973 },  { 0xfba1, 1, 4973 },  { 0xfba2, 1, 4973 },  { 0xfba3, 1, 4973 },  { 0xfba4, 2, 803 },   { 0xfba5, 2, 803 },   { 0xfba6, 1, 4974 },  { 0xfba7, 1, 4974 },  { 0xfba8, 1, 4974 },
    { 0xfba9, 1, 4974 },  { 0xfbaa, 1, 4975 },  { 0xfbab, 1, 4975 },  { 0xfbac, 1, 4975 },  { 0xfbad, 1, 4975 },  { 0xfbae, 1, 4976 },  { 0xfbaf, 1, 4976 },  { 0xfbb0, 2, 807 },   { 0xfbb1, 2, 807 },   { 0xfbd3, 1, 4977 },
    { 0xfbd4, 1, 4977 },  { 0xfbd5, 1, 4977 },  { 0xfbd6, 1, 4977 },  { 0xfbd7, 1, 4978 },  { 0xfbd8, 1, 4978 },  { 0xfbd9, 1, 4979 },  { 0xfbda, 1, 4979 },  { 0xfbdb, 1, 4980 },  { 0xfbdc, 1, 4980 },  { 0xfbdd, 2, 799 },
    { 0xfbde, 1, 4981 },  { 0xfbdf, 1, 4981 },  { 0xfbe0, 1, 4982 },  { 0xfbe1, 1, 4982 },  { 0xfbe2, 1, 4983 },  { 0xfbe3, 1, 4983 },  { 0xfbe4, 1, 4984 },  { 0xfbe5, 1, 4984 },  { 0xfbe6, 1, 4984 },  { 0xfbe7, 1, 4984 },
    { 0xfbe8, 1, 4985 },  { 0xfbe9, 1, 4985 },  { 0xfbea, 3, 4986 },  { 0xfbeb, 3, 4986 },  { 0xfbec, 3, 4989 },  { 0xfbed, 3, 4989 },  { 0xfbee, 3, 4992 },  { 0xfbef, 3, 4992 },  { 0xfbf0, 3, 4995 },  { 0xfbf1, 3, 4995 },
    { 0xfbf2, 3, 4998 },  { 0xfbf3, 3, 4998 },  { 0xfbf4, 3, 5001 },  { 0xfbf5, 3, 5001 },  { 0xfbf6, 3, 5004 },  { 0xfbf7, 3, 5004 },  { 0xfbf8, 3, 5004 },  { 0xfbf9, 3, 5007 },  { 0xfbfa, 3, 5007 },  { 0xfbfb, 3, 5007 },
    { 0xfbfc, 1, 5010 },  { 0xfbfd, 1, 5010 },  { 0xfbfe, 1, 5010 },  { 0xfbff, 1, 5010 },  { 0xfc00, 3, 5011 },  { 0xfc01, 3, 5014 },  { 0xfc02, 3, 5017 },  { 0xfc03, 3, 5007 },  { 0xfc04, 3, 5020 },  { 0xfc05, 2, 5023 },
    { 0xfc06, 2, 5025 },  { 0xfc07, 2, 5027 },  { 0xfc08, 2, 5029 },  { 0xfc09, 2, 5031 },  { 0xfc0a, 2, 5033 },  { 0xfc0b, 2, 5035 },  { 0xfc0c, 2, 5037 },  { 0xfc0d, 2, 5039 },  { 0xfc0e, 2, 5041 },  { 0xfc0f, 2, 5043 },
    { 0xfc10, 2, 5045 },  { 0xfc11, 2, 5047 },  { 0xfc12, 2, 5049 },  { 0xfc13, 2, 5051 },  { 0xfc14, 2, 5053 },  { 0xfc15, 2, 5055 },  { 0xfc16, 2, 5057 },  { 0xfc17, 2, 5059 },  { 0xfc18, 2, 5061 },  { 0xfc19, 2, 5063 },
    { 0xfc1a, 2, 5065 },  { 0xfc1b, 2, 5067 },  { 0xfc1c, 2, 5069 },  { 0xfc1d, 2, 5071 },  { 0xfc1e, 2, 5073 },  { 0xfc1f, 2, 5075 },  { 0xfc20, 2, 5077 },  { 0xfc21, 2, 5079 },  { 0xfc22, 2, 5081 },  { 0xfc23, 2, 5083 },
    { 0xfc24, 2, 5085 },  { 0xfc25, 2, 5087 },  { 0xfc26, 2, 5089 },  { 0xfc27, 2, 5091 },  { 0xfc28, 2, 5093 },  { 0xfc29, 2, 5095 },  { 0xfc2a, 2, 5097 },  { 0xfc2b, 2, 5099 },  { 0xfc2c, 2, 5101 },  { 0xfc2d, 2, 5103 },
    { 0xfc2e, 2, 5105 },  { 0xfc2f, 2, 5107 },  { 0xfc30, 2, 5109 },  { 0xfc31, 2, 5111 },  { 0xfc32, 2, 5113 },  { 0xfc33, 2, 5115 },  { 0xfc34, 2, 5117 },  { 0xfc35, 2, 5119 },  { 0xfc36, 2, 5121 },  { 0xfc37, 2, 5123 },
    { 0xfc38, 2, 5125 },  { 0xfc39, 2, 5127 },  { 0xfc3a, 2, 5129 },  { 0xfc3b, 2, 5131 },  { 0xfc3c, 2, 5133 },  { 0xfc3d, 2, 5135 },  { 0xfc3e, 2, 5137 },  { 0xfc3f, 2, 5139 },  { 0xfc40, 2, 5141 },  { 0xfc41, 2, 5143 },
    { 0xfc42, 2, 5145 },  { 0xfc43, 2, 5147 },  { 0xfc44, 2, 5149 },  { 0xfc45, 2, 5151 },  { 0xfc46, 2, 5153 },  { 0xfc47, 2, 5155 },  { 0xfc48, 2, 5157 },  { 0xfc49, 2, 5159 },  { 0xfc4a, 2, 5161 },  { 0xfc4b, 2, 5163 },
    { 0xfc4c, 2, 5165 },  { 0xfc4d, 2, 5167 },  { 0xfc4e, 2, 5169 },  { 0xfc4f, 2, 5171 },  { 0xfc50, 2, 5173 },  { 0xfc51, 2, 5175 },  { 0xfc52, 2, 5177 },  { 0xfc53, 2, 5179 },  { 0xfc54, 2, 5181 },  { 0xfc55, 2, 5183 },
    { 0xfc56, 2, 5185 },  { 0xfc57, 2, 5187 },  { 0xfc58, 2, 5189 },  { 0xfc59, 2, 5191 },  { 0xfc5a, 2, 5193 },  { 0xfc5b, 2, 5195 },  { 0xfc5c, 2, 5197 },  { 0xfc5d, 2, 5199 },  { 0xfc5e, 3, 5201 },  { 0xfc5f, 3, 5204 },
    { 0xfc60, 3, 5207 },  { 0xfc61, 3, 5210 },  { 0xfc62, 3, 5213 },  { 0xfc63, 3, 5216 },  { 0xfc64, 3, 5219 },  { 0xfc65, 3, 5222 },  { 0xfc66, 3, 5017 },  { 0xfc67, 3, 5225 },  { 0xfc68, 3, 5007 },  { 0xfc69, 3, 5020 },
    { 0xfc6a, 2, 5228 },  { 0xfc6b, 2, 5230 },  { 0xfc6c, 2, 5029 },  { 0xfc6d, 2, 5232 },  { 0xfc6e, 2, 5031 },  { 0xfc6f, 2, 5033 },  { 0xfc70, 2, 5234 },  { 0xfc71, 2, 5236 },  { 0xfc72, 2, 5041 },  { 0xfc73, 2, 5238 },
    { 0xfc74, 2, 5043 },  { 0xfc75, 2, 5045 },  { 0xfc76, 2, 5240 },  { 0xfc77, 2, 5242 },  { 0xfc78, 2, 5049 },  { 0xfc79, 2, 5244 },  { 0xfc7a, 2, 5051 },  { 0xfc7b, 2, 5053 },  { 0xfc7c, 2, 5111 },  { 0xfc7d, 2, 5113 },
    { 0xfc7e, 2, 5119 },  { 0xfc7f, 2, 5121 },  { 0xfc80, 2, 5123 },  { 0xfc81, 2, 5131 },  { 0xfc82, 2, 5133 },  { 0xfc83, 2, 5135 },  { 0xfc84, 2, 5137 },  { 0xfc85, 2, 5145 },  { 0xfc86, 2, 5147 },  { 0xfc87, 2, 5149 },
    { 0xfc88, 2, 5246 },  { 0xfc89, 2, 5157 },  { 0xfc8a, 2, 5248 },  { 0xfc8b, 2, 5250 },  { 0xfc8c, 2, 5169 },  { 0xfc8d, 2, 5252 },  { 0xfc8e, 2, 5171 },  { 0xfc8f, 2, 5173 },  { 0xfc90, 2, 5199 },  { 0xfc91, 2, 5254 },
    { 0xfc92, 2, 5256 },  { 0xfc93, 2, 5189 },  { 0xfc94, 2, 5258 },  { 0xfc95, 2, 5191 },  { 0xfc96, 2, 5193 },  { 0xfc97, 3, 5011 },  { 0xfc98, 3, 5014 },  { 0xfc99, 3, 5260 },  { 0xfc9a, 3, 5017 },  { 0xfc9b, 3, 5263 },
    { 0xfc9c, 2, 5023 },  { 0xfc9d, 2, 5025 },  { 0xfc9e, 2, 5027 },  { 0xfc9f, 2, 5029 },  { 0xfca0, 2, 5266 },  { 0xfca1, 2, 5035 },  { 0xfca2, 2, 5037 },  { 0xfca3, 2, 5039 },  { 0xfca4, 2, 5041 },  { 0xfca5, 2, 5268 },
    { 0xfca6, 2, 5049 },  { 0xfca7, 2, 5055 },  { 0xfca8, 2, 5057 },  { 0xfca9, 2, 5059 },  { 0xfcaa, 2, 5061 },  { 0xfcab, 2, 5063 },  { 0xfcac, 2, 5067 },  { 0xfcad, 2, 5069 },  { 0xfcae, 2, 5071 },  { 0xfcaf, 2, 5073 },
    { 0xfcb0, 2, 5075 },  { 0xfcb1, 2, 5077 },  { 0xfcb2, 2, 5270 },  { 0xfcb3, 2, 5079 },  { 0xfcb4, 2, 5081 },  { 0xfcb5, 2, 5083 },  { 0xfcb6, 2, 5085 },  { 0xfcb7, 2, 5087 },  { 0xfcb8, 2, 5089 },  { 0xfcb9, 2, 5093 },
    { 0xfcba, 2, 5095 },  { 0xfcbb, 2, 5097 },  { 0xfcbc, 2, 5099 },  { 0xfcbd, 2, 5101 },  { 0xfcbe, 2, 5103 },  { 0xfcbf, 2, 5105 },  { 0xfcc0, 2, 5107 },  { 0xfcc1, 2, 5109 },  { 0xfcc2, 2, 5115 },  { 0xfcc3, 2, 5117 },
    { 0xfcc4, 2, 5125 },  { 0xfcc5, 2, 5127 },  { 0xfcc6, 2, 5129 },  { 0xfcc7, 2, 5131 },  { 0xfcc8, 2, 5133 },  { 0xfcc9, 2, 5139 },  { 0xfcca, 2, 5141 },  { 0xfccb, 2, 5143 },  { 0xfccc, 2, 5145 },  { 0xfccd, 2, 5272 },
    { 0xfcce, 2, 5151 },  { 0xfccf, 2, 5153 },  { 0xfcd0, 2, 5155 },  { 0xfcd1, 2, 5157 },  { 0xfcd2, 2, 5163 },  { 0xfcd3, 2, 5165 },  { 0xfcd4, 2, 5167 },  { 0xfcd5, 2, 5169 },  { 0xfcd6, 2, 5274 },  { 0xfcd7, 2, 5175 },
    { 0xfcd8, 2, 5177 },  { 0xfcd9, 2, 5276 },  { 0xfcda, 2, 5183 },  { 0xfcdb, 2, 5185 },  { 0xfcdc, 2, 5187 },  { 0xfcdd, 2, 5189 },  { 0xfcde, 2, 5278 },  { 0xfcdf, 3, 5017 },  { 0xfce0, 3, 5263 },  { 0xfce1, 2, 5029 },
    { 0xfce2, 2, 5266 },  { 0xfce3, 2, 5041 },  { 0xfce4, 2, 5268 },  { 0xfce5, 2, 5049 },  { 0xfce6, 2, 5280 },  { 0xfce7, 2, 5075 },  { 0xfce8, 2, 5282 },  { 0xfce9, 2, 5284 },  { 0xfcea, 2, 5286 },  { 0xfceb, 2, 5131 },
    { 0xfcec, 2, 5133 },  { 0xfced, 2, 5145 },  { 0xfcee, 2, 5169 },  { 0xfcef, 2, 5274 },  { 0xfcf0, 2, 5189 },  { 0xfcf1, 2, 5278 },  { 0xfcf2, 3, 5288 },  { 0xfcf3, 3, 5291 },  { 0xfcf4, 3, 5294 },  { 0xfcf5, 2, 5297 },
    { 0xfcf6, 2, 5299 },  { 0xfcf7, 2, 5301 },  { 0xfcf8, 2, 5303 },  { 0xfcf9, 2, 5305 },  { 0xfcfa, 2, 5307 },  { 0xfcfb, 2, 5309 },  { 0xfcfc, 2, 5311 },  { 0xfcfd, 2, 5313 },  { 0xfcfe, 2, 5315 },  { 0xfcff, 2, 5317 },
    { 0xfd00, 2, 5319 },  { 0xfd01, 2, 5321 },  { 0xfd02, 2, 5323 },  { 0xfd03, 2, 5325 },  { 0xfd04, 2, 5327 },  { 0xfd05, 2, 5329 },  { 0xfd06, 2, 5331 },  { 0xfd07, 2, 5333 },  { 0xfd08, 2, 5335 },  { 0xfd09, 2, 5337 },
    { 0xfd0a, 2, 5339 },  { 0xfd0b, 2, 5341 },  { 0xfd0c, 2, 5284 },  { 0xfd0d, 2, 5343 },  { 0xfd0e, 2, 5345 },  { 0xfd0f, 2, 5347 },  { 0xfd10, 2, 5349 },  { 0xfd11, 2, 5297 },  { 0xfd12, 2, 5299 },  { 0xfd13, 2, 5301 },
    { 0xfd14, 2, 5303 },  { 0xfd15, 2, 5305 },  { 0xfd16, 2, 5307 },  { 0xfd17, 2, 5309 },  { 0xfd18, 2, 5311 },  { 0xfd19, 2, 5313 },  { 0xfd1a, 2, 5315 },  { 0xfd1b, 2, 5317 },  { 0xfd1c, 2, 5319 },  { 0xfd1d, 2, 5321 },
    { 0xfd1e, 2, 5323 },  { 0xfd1f, 2, 5325 },  { 0xfd20, 2, 5327 },  { 0xfd21, 2, 5329 },  { 0xfd22, 2, 5331 },  { 0xfd23, 2, 5333 },  { 0xfd24, 2, 5335 },  { 0xfd25, 2, 5337 },  { 0xfd26, 2, 5339 },  { 0xfd27, 2, 5341 },
    { 0xfd28, 2, 5284 },  { 0xfd29, 2, 5343 },  { 0xfd2a, 2, 5345 },  { 0xfd2b, 2, 5347 },  { 0xfd2c, 2, 5349 },  { 0xfd2d, 2, 5337 },  { 0xfd2e, 2, 5339 },  { 0xfd2f, 2, 5341 },  { 0xfd30, 2, 5284 },  { 0xfd31, 2, 5282 },
    { 0xfd32, 2, 5286 },  { 0xfd33, 2, 5091 },  { 0xfd34, 2, 5069 },  { 0xfd35, 2, 5071 },  { 0xfd36, 2, 5073 },  { 0xfd37, 2, 5337 },  { 0xfd38, 2, 5339 },  { 0xfd39, 2, 5341 },  { 0xfd3a, 2, 5091 },  { 0xfd3b, 2, 5093 },
    { 0xfd3c, 2, 5351 },  { 0xfd3d, 2, 5351 },  { 0xfd50, 3, 5353 },  { 0xfd51, 3, 5356 },  { 0xfd52, 3, 5356 },  { 0xfd53, 3, 5359 },  { 0xfd54, 3, 5362 },  { 0xfd55, 3, 5365 },  { 0xfd56, 3, 5368 },  { 0xfd57, 3, 5371 },
    { 0xfd58, 3, 5374 },  { 0xfd59, 3, 5374 },  { 0xfd5a, 3, 5377 },  { 0xfd5b, 3, 5380 },  { 0xfd5c, 3, 5383 },  { 0xfd5d, 3, 5386 },  { 0xfd5e, 3, 5389 },  { 0xfd5f, 3, 5392 },  { 0xfd60, 3, 5392 },  { 0xfd61, 3, 5395 },
    { 0xfd62, 3, 5398 },  { 0xfd63, 3, 5398 },  { 0xfd64, 3, 5401 },  { 0xfd65, 3, 5401 },  { 0xfd66, 3, 5404 },  { 0xfd67, 3, 5407 },  { 0xfd68, 3, 5407 },  { 0xfd69, 3, 5410 },  { 0xfd6a, 3, 5413 },  { 0xfd6b, 3, 5413 },
    { 0xfd6c, 3, 5416 },  { 0xfd6d, 3, 5416 },  { 0xfd6e, 3, 5419 },  { 0xfd6f, 3, 5422 },  { 0xfd70, 3, 5422 },  { 0xfd71, 3, 5425 },  { 0xfd72, 3, 5425 },  { 0xfd73, 3, 5428 },  { 0xfd74, 3, 5431 },  { 0xfd75, 3, 5434 },
    { 0xfd76, 3, 5437 },  { 0xfd77, 3, 5437 },  { 0xfd78, 3, 5440 },  { 0xfd79, 3, 5443 },  { 0xfd7a, 3, 5446 },  { 0xfd7b, 3, 5449 },  { 0xfd7c, 3, 5452 },  { 0xfd7d, 3, 5452 },  { 0xfd7e, 3, 5455 },  { 0xfd7f, 3, 5458 },
    { 0xfd80, 3, 5461 },  { 0xfd81, 3, 5464 },  { 0xfd82, 3, 5467 },  { 0xfd83, 3, 5470 },  { 0xfd84, 3, 5470 },  { 0xfd85, 3, 5473 },  { 0xfd86, 3, 5473 },  { 0xfd87, 3, 5476 },  { 0xfd88, 3, 5476 },  { 0xfd89, 3, 5479 },
    { 0xfd8a, 3, 5482 },  { 0xfd8b, 3, 5485 },  { 0xfd8c, 3, 5488 },  { 0xfd8d, 3, 5491 },  { 0xfd8e, 3, 5494 },  { 0xfd8f, 3, 5497 },  { 0xfd92, 3, 5500 },  { 0xfd93, 3, 5503 },  { 0xfd94, 3, 5506 },  { 0xfd95, 3, 5509 },
    { 0xfd96, 3, 5512 },  { 0xfd97, 3, 5515 },  { 0xfd98, 3, 5515 },  { 0xfd99, 3, 5518 },  { 0xfd9a, 3, 5521 },  { 0xfd9b, 3, 5524 },  { 0xfd9c, 3, 5527 },  { 0xfd9d, 3, 5527 },  { 0xfd9e, 3, 5530 },  { 0xfd9f, 3, 5533 },
    { 0xfda0, 3, 5536 },  { 0xfda1, 3, 5539 },  { 0xfda2, 3, 5542 },  { 0xfda3, 3, 5545 },  { 0xfda4, 3, 5548 },  { 0xfda5, 3, 5551 },  { 0xfda6, 3, 5554 },  { 0xfda7, 3, 5557 },  { 0xfda8, 3, 5560 },  { 0xfda9, 3, 5563 },
    { 0xfdaa, 3, 5566 },  { 0xfdab, 3, 5569 },  { 0xfdac, 3, 5572 },  { 0xfdad, 3, 5575 },  { 0xfdae, 3, 5578 },  { 0xfdaf, 3, 5581 },  { 0xfdb0, 3, 5584 },  { 0xfdb1, 3, 5587 },  { 0xfdb2, 3, 5590 },  { 0xfdb3, 3, 5593 },
    { 0xfdb4, 3, 5455 },  { 0xfdb5, 3, 5461 },  { 0xfdb6, 3, 5596 },  { 0xfdb7, 3, 5599 },  { 0xfdb8, 3, 5602 },  { 0xfdb9, 3, 5605 },  { 0xfdba, 3, 5608 },  { 0xfdbb, 3, 5611 },  { 0xfdbc, 3, 5608 },  { 0xfdbd, 3, 5602 },
    { 0xfdbe, 3, 5614 },  { 0xfdbf, 3, 5617 },  { 0xfdc0, 3, 5620 },  { 0xfdc1, 3, 5623 },  { 0xfdc2, 3, 5626 },  { 0xfdc3, 3, 5611 },  { 0xfdc4, 3, 5434 },  { 0xfdc5, 3, 5404 },  { 0xfdc6, 3, 5629 },  { 0xfdc7, 3, 5632 },
    { 0xfdf0, 3, 5635 },  { 0xfdf1, 3, 5638 },  { 0xfdf2, 4, 5641 },  { 0xfdf3, 4, 5645 },  { 0xfdf4, 4, 5649 },  { 0xfdf5, 4, 5653 },  { 0xfdf6, 4, 5657 },  { 0xfdf7, 4, 5661 },  { 0xfdf8, 4, 5665 },  { 0xfdf9, 3, 5669 },
    { 0xfdfa, 18, 5672 }, { 0xfdfb, 8, 5690 },  { 0xfdfc, 4, 5698 },  { 0xfe10, 1, 5702 },  { 0xfe11, 1, 5703 },  { 0xfe12, 1, 5704 },  { 0xfe13, 1, 5705 },  { 0xfe14, 1, 621 },   { 0xfe15, 1, 5706 },  { 0xfe16, 1, 5707 },
    { 0xfe17, 1, 5708 },  { 0xfe18, 1, 5709 },  { 0xfe19, 3, 2249 },  { 0xfe30, 2, 2247 },  { 0xfe31, 1, 5710 },  { 0xfe32, 1, 5711 },  { 0xfe33, 1, 5712 },  { 0xfe34, 1, 5712 },  { 0xfe35, 1, 2286 },  { 0xfe36, 1, 2287 },
    { 0xfe37, 1, 5713 },  { 0xfe38, 1, 5714 },  { 0xfe39, 1, 5715 },  { 0xfe3a, 1, 5716 },  { 0xfe3b, 1, 5717 },  { 0xfe3c, 1, 5718 },  { 0xfe3d, 1, 5719 },  { 0xfe3e, 1, 5720 },  { 0xfe3f, 1, 2530 },  { 0xfe40, 1, 2531 },
    { 0xfe41, 1, 5721 },  { 0xfe42, 1, 5722 },  { 0xfe43, 1, 5723 },  { 0xfe44, 1, 5724 },  { 0xfe47, 1, 5725 },  { 0xfe48, 1, 5726 },  { 0xfe49, 2, 2264 },  { 0xfe4a, 2, 2264 },  { 0xfe4b, 2, 2264 },  { 0xfe4c, 2, 2264 },
    { 0xfe4d, 1, 5712 },  { 0xfe4e, 1, 5712 },  { 0xfe4f, 1, 5712 },  { 0xfe50, 1, 5702 },  { 0xfe51, 1, 5703 },  { 0xfe52, 1, 2246 },  { 0xfe54, 1, 621 },   { 0xfe55, 1, 5705 },  { 0xfe56, 1, 5707 },  { 0xfe57, 1, 5706 },
    { 0xfe58, 1, 5710 },  { 0xfe59, 1, 2286 },  { 0xfe5a, 1, 2287 },  { 0xfe5b, 1, 5713 },  { 0xfe5c, 1, 5714 },  { 0xfe5d, 1, 5715 },  { 0xfe5e, 1, 5716 },  { 0xfe5f, 1, 5727 },  { 0xfe60, 1, 5728 },  { 0xfe61, 1, 5729 },
    { 0xfe62, 1, 2283 },  { 0xfe63, 1, 5730 },  { 0xfe64, 1, 5731 },  { 0xfe65, 1, 5732 },  { 0xfe66, 1, 2285 },  { 0xfe68, 1, 5733 },  { 0xfe69, 1, 5734 },  { 0xfe6a, 1, 5735 },  { 0xfe6b, 1, 5736 },  { 0xfe70, 2, 5737 },
    { 0xfe71, 2, 5739 },  { 0xfe72, 2, 5741 },  { 0xfe74, 2, 5743 },  { 0xfe76, 2, 5745 },  { 0xfe77, 2, 5747 },  { 0xfe78, 2, 5749 },  { 0xfe79, 2, 5751 },  { 0xfe7a, 2, 5753 },  { 0xfe7b, 2, 5755 },  { 0xfe7c, 2, 5757 },
    { 0xfe7d, 2, 5759 },  { 0xfe7e, 2, 5761 },  { 0xfe7f, 2, 5763 },  { 0xfe80, 1, 5765 },  { 0xfe81, 2, 785 },   { 0xfe82, 2, 785 },   { 0xfe83, 2, 787 },   { 0xfe84, 2, 787 },   { 0xfe85, 2, 789 },   { 0xfe86, 2, 789 },
    { 0xfe87, 2, 791 },   { 0xfe88, 2, 791 },   { 0xfe89, 2, 793 },   { 0xfe8a, 2, 793 },   { 0xfe8b, 2, 793 },   { 0xfe8c, 2, 793 },   { 0xfe8d, 1, 5766 },  { 0xfe8e, 1, 5766 },  { 0xfe8f, 1, 5767 },  { 0xfe90, 1, 5767 },
    { 0xfe91, 1, 5767 },  { 0xfe92, 1, 5767 },  { 0xfe93, 1, 5768 },  { 0xfe94, 1, 5768 },  { 0xfe95, 1, 5769 },  { 0xfe96, 1, 5769 },  { 0xfe97, 1, 5769 },  { 0xfe98, 1, 5769 },  { 0xfe99, 1, 5770 },  { 0xfe9a, 1, 5770 },
    { 0xfe9b, 1, 5770 },  { 0xfe9c, 1, 5770 },  { 0xfe9d, 1, 5771 },  { 0xfe9e, 1, 5771 },  { 0xfe9f, 1, 5771 },  { 0xfea0, 1, 5771 },  { 0xfea1, 1, 5772 },  { 0xfea2, 1, 5772 },  { 0xfea3, 1, 5772 },  { 0xfea4, 1, 5772 },
    { 0xfea5, 1, 5773 },  { 0xfea6, 1, 5773 },  { 0xfea7, 1, 5773 },  { 0xfea8, 1, 5773 },  { 0xfea9, 1, 5774 },  { 0xfeaa, 1, 5774 },  { 0xfeab, 1, 5775 },  { 0xfeac, 1, 5775 },  { 0xfead, 1, 5776 },  { 0xfeae, 1, 5776 },
    { 0xfeaf, 1, 5777 },  { 0xfeb0, 1, 5777 },  { 0xfeb1, 1, 5778 },  { 0xfeb2, 1, 5778 },  { 0xfeb3, 1, 5778 },  { 0xfeb4, 1, 5778 },  { 0xfeb5, 1, 5779 },  { 0xfeb6, 1, 5779 },  { 0xfeb7, 1, 5779 },  { 0xfeb8, 1, 5779 },
    { 0xfeb9, 1, 5780 },  { 0xfeba, 1, 5780 },  { 0xfebb, 1, 5780 },  { 0xfebc, 1, 5780 },  { 0xfebd, 1, 5781 },  { 0xfebe, 1, 5781 },  { 0xfebf, 1, 5781 },  { 0xfec0, 1, 5781 },  { 0xfec1, 1, 5782 },  { 0xfec2, 1, 5782 },
    { 0xfec3, 1, 5782 },  { 0xfec4, 1, 5782 },  { 0xfec5, 1, 5783 },  { 0xfec6, 1, 5783 },  { 0xfec7, 1, 5783 },  { 0xfec8, 1, 5783 },  { 0xfec9, 1, 5784 },  { 0xfeca, 1, 5784 },  { 0xfecb, 1, 5784 },  { 0xfecc, 1, 5784 },
    { 0xfecd, 1, 5785 },  { 0xfece, 1, 5785 },  { 0xfecf, 1, 5785 },  { 0xfed0, 1, 5785 },  { 0xfed1, 1, 5786 },  { 0xfed2, 1, 5786 },  { 0xfed3, 1, 5786 },  { 0xfed4, 1, 5786 },  { 0xfed5, 1, 5787 },  { 0xfed6, 1, 5787 },
    { 0xfed7, 1, 5787 },  { 0xfed8, 1, 5787 },  { 0xfed9, 1, 5788 },  { 0xfeda, 1, 5788 },  { 0xfedb, 1, 5788 },  { 0xfedc, 1, 5788 },  { 0xfedd, 1, 5789 },  { 0xfede, 1, 5789 },  { 0xfedf, 1, 5789 },  { 0xfee0, 1, 5789 },
    { 0xfee1, 1, 5790 },  { 0xfee2, 1, 5790 },  { 0xfee3, 1, 5790 },  { 0xfee4, 1, 5790 },  { 0xfee5, 1, 5791 },  { 0xfee6, 1, 5791 },  { 0xfee7, 1, 5791 },  { 0xfee8, 1, 5791 },  { 0xfee9, 1, 5792 },  { 0xfeea, 1, 5792 },
    { 0xfeeb, 1, 5792 },  { 0xfeec, 1, 5792 },  { 0xfeed, 1, 5793 },  { 0xfeee, 1, 5793 },  { 0xfeef, 1, 4985 },  { 0xfef0, 1, 4985 },  { 0xfef1, 1, 5794 },  { 0xfef2, 1, 5794 },  { 0xfef3, 1, 5794 },  { 0xfef4, 1, 5794 },
    { 0xfef5, 3, 5795 },  { 0xfef6, 3, 5795 },  { 0xfef7, 3, 5798 },  { 0xfef8, 3, 5798 },  { 0xfef9, 3, 5801 },  { 0xfefa, 3, 5801 },  { 0xfefb, 2, 5804 },  { 0xfefc, 2, 5804 },  { 0xff01, 1, 5706 },  { 0xff02, 1, 5806 },
    { 0xff03, 1, 5727 },  { 0xff04, 1, 5734 },  { 0xff05, 1, 5735 },  { 0xff06, 1, 5728 },  { 0xff07, 1, 5807 },  { 0xff08, 1, 2286 },  { 0xff09, 1, 2287 },  { 0xff0a, 1, 5729 },  { 0xff0b, 1, 2283 },  { 0xff0c, 1, 5702 },
    { 0xff0d, 1, 5730 },  { 0xff0e, 1, 2246 },  { 0xff0f, 1, 5808 },  { 0xff10, 1, 2276 },  { 0xff11, 1, 13 },    { 0xff12, 1, 6 },     { 0xff13, 1, 7 },     { 0xff14, 1, 2277 },  { 0xff15, 1, 2278 },  { 0xff16, 1, 2279 },
    { 0xff17, 1, 2280 },  { 0xff18, 1, 2281 },  { 0xff19, 1, 2282 },  { 0xff1a, 1, 5705 },  { 0xff1b, 1, 621 },   { 0xff1c, 1, 5731 },  { 0xff1d, 1, 2285 },  { 0xff1e, 1, 5732 },  { 0xff1f, 1, 5707 },  { 0xff20, 1, 5736 },
    { 0xff21, 1, 973 },   { 0xff22, 1, 975 },   { 0xff23, 1, 2297 },  { 0xff24, 1, 976 },   { 0xff25, 1, 977 },   { 0xff26, 1, 2322 },  { 0xff27, 1, 979 },   { 0xff28, 1, 980 },   { 0xff29, 1, 981 },   { 0xff2a, 1, 982 },
    { 0xff2b, 1, 983 },   { 0xff2c, 1, 984 },   { 0xff2d, 1, 985 },   { 0xff2e, 1, 986 },   { 0xff2f, 1, 987 },   { 0xff30, 1, 989 },   { 0xff31, 1, 2312 },  { 0xff32, 1, 990 },   { 0xff33, 1, 2754 },  { 0xff34, 1, 991 },
    { 0xff35, 1, 992 },   { 0xff36, 1, 2388 },  { 0xff37, 1, 993 },   { 0xff38, 1, 2400 },  { 0xff39, 1, 2755 },  { 0xff3a, 1, 2320 },  { 0xff3b, 1, 5725 },  { 0xff3c, 1, 5733 },  { 0xff3d, 1, 5726 },  { 0xff3e, 1, 5809 },
    { 0xff3f, 1, 5712 },  { 0xff40, 1, 2221 },  { 0xff41, 1, 3 },     { 0xff42, 1, 997 },   { 0xff43, 1, 1023 },  { 0xff44, 1, 998 },   { 0xff45, 1, 999 },   { 0xff46, 1, 1026 },  { 0xff47, 1, 1003 },  { 0xff48, 1, 588 },
    { 0xff49, 1, 1020 },  { 0xff4a, 1, 590 },   { 0xff4b, 1, 1004 },  { 0xff4c, 1, 610 },   { 0xff4d, 1, 1005 },  { 0xff4e, 1, 2288 },  { 0xff4f, 1, 14 },    { 0xff50, 1, 1010 },  { 0xff51, 1, 2756 },  { 0xff52, 1, 591 },
    { 0xff53, 1, 356 },   { 0xff54, 1, 1011 },  { 0xff55, 1, 1012 },  { 0xff56, 1, 1015 },  { 0xff57, 1, 595 },   { 0xff58, 1, 611 },   { 0xff59, 1, 596 },   { 0xff5a, 1, 1053 },  { 0xff5b, 1, 5713 },  { 0xff5c, 1, 5810 },
    { 0xff5d, 1, 5714 },  { 0xff5e, 1, 5811 },  { 0xff5f, 1, 5812 },  { 0xff60, 1, 5813 },  { 0xff61, 1, 5704 },  { 0xff62, 1, 5721 },  { 0xff63, 1, 5722 },  { 0xff64, 1, 5703 },  { 0xff65, 1, 5814 },  { 0xff66, 1, 3662 },
    { 0xff67, 1, 5815 },  { 0xff68, 1, 5816 },  { 0xff69, 1, 5817 },  { 0xff6a, 1, 5818 },  { 0xff6b, 1, 5819 },  { 0xff6c, 1, 5820 },  { 0xff6d, 1, 5821 },  { 0xff6e, 1, 5822 },  { 0xff6f, 1, 5823 },  { 0xff70, 1, 5824 },
    { 0xff71, 1, 3616 },  { 0xff72, 1, 3617 },  { 0xff73, 1, 3618 },  { 0xff74, 1, 3619 },  { 0xff75, 1, 3620 },  { 0xff76, 1, 3621 },  { 0xff77, 1, 3622 },  { 0xff78, 1, 3623 },  { 0xff79, 1, 3624 },  { 0xff7a, 1, 3625 },
    { 0xff7b, 1, 3626 },  { 0xff7c, 1, 3627 },  { 0xff7d, 1, 3628 },  { 0xff7e, 1, 3629 },  { 0xff7f, 1, 3630 },  { 0xff80, 1, 3631 },  { 0xff81, 1, 3632 },  { 0xff82, 1, 3633 },  { 0xff83, 1, 3634 },  { 0xff84, 1, 3635 },
    { 0xff85, 1, 3636 },  { 0xff86, 1, 3637 },  { 0xff87, 1, 3638 },  { 0xff88, 1, 3639 },  { 0xff89, 1, 3640 },  { 0xff8a, 1, 3641 },  { 0xff8b, 1, 3642 },  { 0xff8c, 1, 3643 },  { 0xff8d, 1, 3644 },  { 0xff8e, 1, 3645 },
    { 0xff8f, 1, 3646 },  { 0xff90, 1, 3647 },  { 0xff91, 1, 3648 },  { 0xff92, 1, 3649 },  { 0xff93, 1, 3650 },  { 0xff94, 1, 3651 },  { 0xff95, 1, 3652 },  { 0xff96, 1, 3653 },  { 0xff97, 1, 3654 },  { 0xff98, 1, 3655 },
    { 0xff99, 1, 3656 },  { 0xff9a, 1, 3657 },  { 0xff9b, 1, 3658 },  { 0xff9c, 1, 3659 },  { 0xff9d, 1, 5825 },  { 0xff9e, 1, 5826 },  { 0xff9f, 1, 5827 },  { 0xffa0, 1, 3166 },  { 0xffa1, 1, 3115 },  { 0xffa2, 1, 3116 },
    { 0xffa3, 1, 3117 },  { 0xffa4, 1, 3118 },  { 0xffa5, 1, 3119 },  { 0xffa6, 1, 3120 },  { 0xffa7, 1, 3121 },  { 0xffa8, 1, 3122 },  { 0xffa9, 1, 3123 },  { 0xffaa, 1, 3124 },  { 0xffab, 1, 3125 },  { 0xffac, 1, 3126 },
    { 0xffad, 1, 3127 },  { 0xffae, 1, 3128 },  { 0xffaf, 1, 3129 },  { 0xffb0, 1, 3130 },  { 0xffb1, 1, 3131 },  { 0xffb2, 1, 3132 },  { 0xffb3, 1, 3133 },  { 0xffb4, 1, 3134 },  { 0xffb5, 1, 3135 },  { 0xffb6, 1, 3136 },
    { 0xffb7, 1, 3137 },  { 0xffb8, 1, 3138 },  { 0xffb9, 1, 3139 },  { 0xffba, 1, 3140 },  { 0xffbb, 1, 3141 },  { 0xffbc, 1, 3142 },  { 0xffbd, 1, 3143 },  { 0xffbe, 1, 3144 },  { 0xffc2, 1, 3145 },  { 0xffc3, 1, 3146 },
    { 0xffc4, 1, 3147 },  { 0xffc5, 1, 3148 },  { 0xffc6, 1, 3149 },  { 0xffc7, 1, 3150 },  { 0xffca, 1, 3151 },  { 0xffcb, 1, 3152 },  { 0xffcc, 1, 3153 },  { 0xffcd, 1, 3154 },  { 0xffce, 1, 3155 },  { 0xffcf, 1, 3156 },
    { 0xffd2, 1, 3157 },  { 0xffd3, 1, 3158 },  { 0xffd4, 1, 3159 },  { 0xffd5, 1, 3160 },  { 0xffd6, 1, 3161 },  { 0xffd7, 1, 3162 },  { 0xffda, 1, 3163 },  { 0xffdb, 1, 3164 },  { 0xffdc, 1, 3165 },  { 0xffe0, 1, 5828 },
    { 0xffe1, 1, 5829 },  { 0xffe2, 1, 5830 },  { 0xffe3, 2, 4 },     { 0xffe4, 1, 5831 },  { 0xffe5, 1, 5832 },  { 0xffe6, 1, 5833 },  { 0xffe8, 1, 5834 },  { 0xffe9, 1, 5835 },  { 0xffea, 1, 5836 },  { 0xffeb, 1, 5837 },
    { 0xffec, 1, 5838 },  { 0xffed, 1, 5839 },  { 0xffee, 1, 5840 },  { 0x1109a, 2, 5841 }, { 0x1109c, 2, 5843 }, { 0x110ab, 2, 5845 }, { 0x1112e, 2, 5847 }, { 0x1112f, 2, 5849 }, { 0x1134b, 2, 5851 }, { 0x1134c, 2, 5853 },
    { 0x114bb, 2, 5855 }, { 0x114bc, 2, 5857 }, { 0x114be, 2, 5859 }, { 0x115ba, 2, 5861 }, { 0x115bb, 2, 5863 }, { 0x1d15e, 2, 5865 }, { 0x1d15f, 2, 5867 }, { 0x1d160, 3, 5869 }, { 0x1d161, 3, 5872 }, { 0x1d162, 3, 5875 },
    { 0x1d163, 3, 5878 }, { 0x1d164, 3, 5881 }, { 0x1d1bb, 2, 5884 }, { 0x1d1bc, 2, 5886 }, { 0x1d1bd, 3, 5888 }, { 0x1d1be, 3, 5891 }, { 0x1d1bf, 3, 5894 }, { 0x1d1c0, 3, 5897 }, { 0x1d400, 1, 973 },  { 0x1d401, 1, 975 },
    { 0x1d402, 1, 2297 }, { 0x1d403, 1, 976 },  { 0x1d404, 1, 977 },  { 0x1d405, 1, 2322 }, { 0x1d406, 1, 979 },  { 0x1d407, 1, 980 },  { 0x1d408, 1, 981 },  { 0x1d409, 1, 982 },  { 0x1d40a, 1, 983 },  { 0x1d40b, 1, 984 },
    { 0x1d40c, 1, 985 },  { 0x1d40d, 1, 986 },  { 0x1d40e, 1, 987 },  { 0x1d40f, 1, 989 },  { 0x1d410, 1, 2312 }, { 0x1d411, 1, 990 },  { 0x1d412, 1, 2754 }, { 0x1d413, 1, 991 },  { 0x1d414, 1, 992 },  { 0x1d415, 1, 2388 },
    { 0x1d416, 1, 993 },  { 0x1d417, 1, 2400 }, { 0x1d418, 1, 2755 }, { 0x1d419, 1, 2320 }, { 0x1d41a, 1, 3 },    { 0x1d41b, 1, 997 },  { 0x1d41c, 1, 1023 }, { 0x1d41d, 1, 998 },  { 0x1d41e, 1, 999 },  { 0x1d41f, 1, 1026 },
    { 0x1d420, 1, 1003 }, { 0x1d421, 1, 588 },  { 0x1d422, 1, 1020 }, { 0x1d423, 1, 590 },  { 0x1d424, 1, 1004 }, { 0x1d425, 1, 610 },  { 0x1d426, 1, 1005 }, { 0x1d427, 1, 2288 }, { 0x1d428, 1, 14 },   { 0x1d429, 1, 1010 },
    { 0x1d42a, 1, 2756 }, { 0x1d42b, 1, 591 },  { 0x1d42c, 1, 356 },  { 0x1d42d, 1, 1011 }, { 0x1d42e, 1, 1012 }, { 0x1d42f, 1, 1015 }, { 0x1d430, 1, 595 },  { 0x1d431, 1, 611 },  { 0x1d432, 1, 596 },  { 0x1d433, 1, 1053 },
    { 0x1d434, 1, 973 },  { 0x1d435, 1, 975 },  { 0x1d436, 1, 2297 }, { 0x1d437, 1, 976 },  { 0x1d438, 1, 977 },  { 0x1d439, 1, 2322 }, { 0x1d43a, 1, 979 },  { 0x1d43b, 1, 980 },  { 0x1d43c, 1, 981 },  { 0x1d43d, 1, 982 },
    { 0x1d43e, 1, 983 },  { 0x1d43f, 1, 984 },  { 0x1d440, 1, 985 },  { 0x1d441, 1, 986 },  { 0x1d442, 1, 987 },  { 0x1d443, 1, 989 },  { 0x1d444, 1, 2312 }, { 0x1d445, 1, 990 },  { 0x1d446, 1, 2754 }, { 0x1d447, 1, 991 },
    { 0x1d448, 1, 992 },  { 0x1d449, 1, 2388 }, { 0x1d44a, 1, 993 },  { 0x1d44b, 1, 2400 }, { 0x1d44c, 1, 2755 }, { 0x1d44d, 1, 2320 }, { 0x1d44e, 1, 3 },    { 0x1d44f, 1, 997 },  { 0x1d450, 1, 1023 }, { 0x1d451, 1, 998 },
    { 0x1d452, 1, 999 },  { 0x1d453, 1, 1026 }, { 0x1d454, 1, 1003 }, { 0x1d456, 1, 1020 }, { 0x1d457, 1, 590 },  { 0x1d458, 1, 1004 }, { 0x1d459, 1, 610 },  { 0x1d45a, 1, 1005 }, { 0x1d45b, 1, 2288 }, { 0x1d45c, 1, 14 },
    { 0x1d45d, 1, 1010 }, { 0x1d45e, 1, 2756 }, { 0x1d45f, 1, 591 },  { 0x1d460, 1, 356 },  { 0x1d461, 1, 1011 }, { 0x1d462, 1, 1012 }, { 0x1d463, 1, 1015 }, { 0x1d464, 1, 595 },  { 0x1d465, 1, 611 },  { 0x1d466, 1, 596 },
    { 0x1d467, 1, 1053 }, { 0x1d468, 1, 973 },  { 0x1d469, 1, 975 },  { 0x1d46a, 1, 2297 }, { 0x1d46b, 1, 976 },  { 0x1d46c, 1, 977 },  { 0x1d46d, 1, 2322 }, { 0x1d46e, 1, 979 },  { 0x1d46f, 1, 980 },  { 0x1d470, 1, 981 },
    { 0x1d471, 1, 982 },  { 0x1d472, 1, 983 },  { 0x1d473, 1, 984 },  { 0x1d474, 1, 985 },  { 0x1d475, 1, 986 },  { 0x1d476, 1, 987 },  { 0x1d477, 1, 989 },  { 0x1d478, 1, 2312 }, { 0x1d479, 1, 990 },  { 0x1d47a, 1, 2754 },
    { 0x1d47b, 1, 991 },  { 0x1d47c, 1, 992 },  { 0x1d47d, 1, 2388 }, { 0x1d47e, 1, 993 },  { 0x1d47f, 1, 2400 }, { 0x1d480, 1, 2755 }, { 0x1d481, 1, 2320 }, { 0x1d482, 1, 3 },    { 0x1d483, 1, 997 },  { 0x1d484, 1, 1023 },
    { 0x1d485, 1, 998 },  { 0x1d486, 1, 999 },  { 0x1d487, 1, 1026 }, { 0x1d488, 1, 1003 }, { 0x1d489, 1, 588 },  { 0x1d48a, 1, 1020 }, { 0x1d48b, 1, 590 },  { 0x1d48c, 1, 1004 }, { 0x1d48d, 1, 610 },  { 0x1d48e, 1, 1005 },
    { 0x1d48f, 1, 2288 }, { 0x1d490, 1, 14 },   { 0x1d491, 1, 1010 }, { 0x1d492, 1, 2756 }, { 0x1d493, 1, 591 },  { 0x1d494, 1, 356 },  { 0x1d495, 1, 1011 }, { 0x1d496, 1, 1012 }, { 0x1d497, 1, 1015 }, { 0x1d498, 1, 595 },
    { 0x1d499, 1, 611 },  { 0x1d49a, 1, 596 },  { 0x1d49b, 1, 1053 }, { 0x1d49c, 1, 973 },  { 0x1d49e, 1, 2297 }, { 0x1d49f, 1, 976 },  { 0x1d4a2, 1, 979 },  { 0x1d4a5, 1, 982 },  { 0x1d4a6, 1, 983 },  { 0x1d4a9, 1, 986 },
    { 0x1d4aa, 1, 987 },  { 0x1d4ab, 1, 989 },  { 0x1d4ac, 1, 2312 }, { 0x1d4ae, 1, 2754 }, { 0x1d4af, 1, 991 },  { 0x1d4b0, 1, 992 },  { 0x1d4b1, 1, 2388 }, { 0x1d4b2, 1, 993 },  { 0x1d4b3, 1, 2400 }, { 0x1d4b4, 1, 2755 },
    { 0x1d4b5, 1, 2320 }, { 0x1d4b6, 1, 3 },    { 0x1d4b7, 1, 997 },  { 0x1d4b8, 1, 1023 }, { 0x1d4b9, 1, 998 },  { 0x1d4bb, 1, 1026 }, { 0x1d4bd, 1, 588 },  { 0x1d4be, 1, 1020 }, { 0x1d4bf, 1, 590 },  { 0x1d4c0, 1, 1004 },
    { 0x1d4c1, 1, 610 },  { 0x1d4c2, 1, 1005 }, { 0x1d4c3, 1, 2288 }, { 0x1d4c5, 1, 1010 }, { 0x1d4c6, 1, 2756 }, { 0x1d4c7, 1, 591 },  { 0x1d4c8, 1, 356 },  { 0x1d4c9, 1, 1011 }, { 0x1d4ca, 1, 1012 }, { 0x1d4cb, 1, 1015 },
    { 0x1d4cc, 1, 595 },  { 0x1d4cd, 1, 611 },  { 0x1d4ce, 1, 596 },  { 0x1d4cf, 1, 1053 }, { 0x1d4d0, 1, 973 },  { 0x1d4d1, 1, 975 },  { 0x1d4d2, 1, 2297 }, { 0x1d4d3, 1, 976 },  { 0x1d4d4, 1, 977 },  { 0x1d4d5, 1, 2322 },
    { 0x1d4d6, 1, 979 },  { 0x1d4d7, 1, 980 },  { 0x1d4d8, 1, 981 },  { 0x1d4d9, 1, 982 },  { 0x1d4da, 1, 983 },  { 0x1d4db, 1, 984 },  { 0x1d4dc, 1, 985 },  { 0x1d4dd, 1, 986 },  { 0x1d4de, 1, 987 },  { 0x1d4df, 1, 989 },
    { 0x1d4e0, 1, 2312 }, { 0x1d4e1, 1, 990 },  { 0x1d4e2, 1, 2754 }, { 0x1d4e3, 1, 991 },  { 0x1d4e4, 1, 992 },  { 0x1d4e5, 1, 2388 }, { 0x1d4e6, 1, 993 },  { 0x1d4e7, 1, 2400 }, { 0x1d4e8, 1, 2755 }, { 0x1d4e9, 1, 2320 },
    { 0x1d4ea, 1, 3 },    { 0x1d4eb, 1, 997 },  { 0x1d4ec, 1, 1023 }, { 0x1d4ed, 1, 998 },  { 0x1d4ee, 1, 999 },  { 0x1d4ef, 1, 1026 }, { 0x1d4f0, 1, 1003 }, { 0x1d4f1, 1, 588 },  { 0x1d4f2, 1, 1020 }, { 0x1d4f3, 1, 590 },
    { 0x1d4f4, 1, 1004 }, { 0x1d4f5, 1, 610 },  { 0x1d4f6, 1, 1005 }, { 0x1d4f7, 1, 2288 }, { 0x1d4f8, 1, 14 },   { 0x1d4f9, 1, 1010 }, { 0x1d4fa, 1, 2756 }, { 0x1d4fb, 1, 591 },  { 0x1d4fc, 1, 356 },  { 0x1d4fd, 1, 1011 },
    { 0x1d4fe, 1, 1012 }, { 0x1d4ff, 1, 1015 }, { 0x1d500, 1, 595 },  { 0x1d501, 1, 611 },  { 0x1d502, 1, 596 },  { 0x1d503, 1, 1053 }, { 0x1d504, 1, 973 },  { 0x1d505, 1, 975 },  { 0x1d507, 1, 976 },  { 0x1d508, 1, 977 },
    { 0x1d509, 1, 2322 }, { 0x1d50a, 1, 979 },  { 0x1d50d, 1, 982 },  { 0x1d50e, 1, 983 },  { 0x1d50f, 1, 984 },  { 0x1d510, 1, 985 },  { 0x1d511, 1, 986 },  { 0x1d512, 1, 987 },  { 0x1d513, 1, 989 },  { 0x1d514, 1, 2312 },
    { 0x1d516, 1, 2754 }, { 0x1d517, 1, 991 },  { 0x1d518, 1, 992 },  { 0x1d519, 1, 2388 }, { 0x1d51a, 1, 993 },  { 0x1d51b, 1, 2400 }, { 0x1d51c, 1, 2755 }, { 0x1d51e, 1, 3 },    { 0x1d51f, 1, 997 },  { 0x1d520, 1, 1023 },
    { 0x1d521, 1, 998 },  { 0x1d522, 1, 999 },  { 0x1d523, 1, 1026 }, { 0x1d524, 1, 1003 }, { 0x1d525, 1, 588 },  { 0x1d526, 1, 1020 }, { 0x1d527, 1, 590 },  { 0x1d528, 1, 1004 }, { 0x1d529, 1, 610 },  { 0x1d52a, 1, 1005 },
    { 0x1d52b, 1, 2288 }, { 0x1d52c, 1, 14 },   { 0x1d52d, 1, 1010 }, { 0x1d52e, 1, 2756 }, { 0x1d52f, 1, 591 },  { 0x1d530, 1, 356 },  { 0x1d531, 1, 1011 }, { 0x1d532, 1, 1012 }, { 0x1d533, 1, 1015 }, { 0x1d534, 1, 595 },
    { 0x1d535, 1, 611 },  { 0x1d536, 1, 596 },  { 0x1d537, 1, 1053 }, { 0x1d538, 1, 973 },  { 0x1d539, 1, 975 },  { 0x1d53b, 1, 976 },  { 0x1d53c, 1, 977 },  { 0x1d53d, 1, 2322 }, { 0x1d53e, 1, 979 },  { 0x1d540, 1, 981 },
    { 0x1d541, 1, 982 },  { 0x1d542, 1, 983 },  { 0x1d543, 1, 984 },  { 0x1d544, 1, 985 },  { 0x1d546, 1, 987 },  { 0x1d54a, 1, 2754 }, { 0x1d54b, 1, 991 },  { 0x1d54c, 1, 992 },  { 0x1d54d, 1, 2388 }, { 0x1d54e, 1, 993 },
    { 0x1d54f, 1, 2400 }, { 0x1d550, 1, 2755 }, { 0x1d552, 1, 3 },    { 0x1d553, 1, 997 },  { 0x1d554, 1, 1023 }, { 0x1d555, 1, 998 },  { 0x1d556, 1, 999 },  { 0x1d557, 1, 1026 }, { 0x1d558, 1, 1003 }, { 0x1d559, 1, 588 },
    { 0x1d55a, 1, 1020 }, { 0x1d55b, 1, 590 },  { 0x1d55c, 1, 1004 }, { 0x1d55d, 1, 610 },  { 0x1d55e, 1, 1005 }, { 0x1d55f, 1, 2288 }, { 0x1d560, 1, 14 },   { 0x1d561, 1, 1010 }, { 0x1d562, 1, 2756 }, { 0x1d563, 1, 591 },
    { 0x1d564, 1, 356 },  { 0x1d565, 1, 1011 }, { 0x1d566, 1, 1012 }, { 0x1d567, 1, 1015 }, { 0x1d568, 1, 595 },  { 0x1d569, 1, 611 },  { 0x1d56a, 1, 596 },  { 0x1d56b, 1, 1053 }, { 0x1d56c, 1, 973 },  { 0x1d56d, 1, 975 },
    { 0x1d56e, 1, 2297 }, { 0x1d56f, 1, 976 },  { 0x1d570, 1, 977 },  { 0x1d571, 1, 2322 }, { 0x1d572, 1, 979 },  { 0x1d573, 1, 980 },  { 0x1d574, 1, 981 },  { 0x1d575, 1, 982 },  { 0x1d576, 1, 983 },  { 0x1d577, 1, 984 },
    { 0x1d578, 1, 985 },  { 0x1d579, 1, 986 },  { 0x1d57a, 1, 987 },  { 0x1d57b, 1, 989 },  { 0x1d57c, 1, 2312 }, { 0x1d57d, 1, 990 },  { 0x1d57e, 1, 2754 }, { 0x1d57f, 1, 991 },  { 0x1d580, 1, 992 },  { 0x1d581, 1, 2388 },
    { 0x1d582, 1, 993 },  { 0x1d583, 1, 2400 }, { 0x1d584, 1, 2755 }, { 0x1d585, 1, 2320 }, { 0x1d586, 1, 3 },    { 0x1d587, 1, 997 },  { 0x1d588, 1, 1023 }, { 0x1d589, 1, 998 },  { 0x1d58a, 1, 999 },  { 0x1d58b, 1, 1026 },
    { 0x1d58c, 1, 1003 }, { 0x1d58d, 1, 588 },  { 0x1d58e, 1, 1020 }, { 0x1d58f, 1, 590 },  { 0x1d590, 1, 1004 }, { 0x1d591, 1, 610 },  { 0x1d592, 1, 1005 }, { 0x1d593, 1, 2288 }, { 0x1d594, 1, 14 },   { 0x1d595, 1, 1010 },
    { 0x1d596, 1, 2756 }, { 0x1d597, 1, 591 },  { 0x1d598, 1, 356 },  { 0x1d599, 1, 1011 }, { 0x1d59a, 1, 1012 }, { 0x1d59b, 1, 1015 }, { 0x1d59c, 1, 595 },  { 0x1d59d, 1, 611 },  { 0x1d59e, 1, 596 },  { 0x1d59f, 1, 1053 },
    { 0x1d5a0, 1, 973 },  { 0x1d5a1, 1, 975 },  { 0x1d5a2, 1, 2297 }, { 0x1d5a3, 1, 976 },  { 0x1d5a4, 1, 977 },  { 0x1d5a5, 1, 2322 }, { 0x1d5a6, 1, 979 },  { 0x1d5a7, 1, 980 },  { 0x1d5a8, 1, 981 },  { 0x1d5a9, 1, 982 },
    { 0x1d5aa, 1, 983 },  { 0x1d5ab, 1, 984 },  { 0x1d5ac, 1, 985 },  { 0x1d5ad, 1, 986 },  { 0x1d5ae, 1, 987 },  { 0x1d5af, 1, 989 },  { 0x1d5b0, 1, 2312 }, { 0x1d5b1, 1, 990 },  { 0x1d5b2, 1, 2754 }, { 0x1d5b3, 1, 991 },
    { 0x1d5b4, 1, 992 },  { 0x1d5b5, 1, 2388 }, { 0x1d5b6, 1, 993 },  { 0x1d5b7, 1, 2400 }, { 0x1d5b8, 1, 2755 }, { 0x1d5b9, 1, 2320 }, { 0x1d5ba, 1, 3 },    { 0x1d5bb, 1, 997 },  { 0x1d5bc, 1, 1023 }, { 0x1d5bd, 1, 998 },
    { 0x1d5be, 1, 999 },  { 0x1d5bf, 1, 1026 }, { 0x1d5c0, 1, 1003 }, { 0x1d5c1, 1, 588 },  { 0x1d5c2, 1, 1020 }, { 0x1d5c3, 1, 590 },  { 0x1d5c4, 1, 1004 }, { 0x1d5c5, 1, 610 },  { 0x1d5c6, 1, 1005 }, { 0x1d5c7, 1, 2288 },
    { 0x1d5c8, 1, 14 },   { 0x1d5c9, 1, 1010 }, { 0x1d5ca, 1, 2756 }, { 0x1d5cb, 1, 591 },  { 0x1d5cc, 1, 356 },  { 0x1d5cd, 1, 1011 }, { 0x1d5ce, 1, 1012 }, { 0x1d5cf, 1, 1015 }, { 0x1d5d0, 1, 595 },  { 0x1d5d1, 1, 611 },
    { 0x1d5d2, 1, 596 },  { 0x1d5d3, 1, 1053 }, { 0x1d5d4, 1, 973 },  { 0x1d5d5, 1, 975 },  { 0x1d5d6, 1, 2297 }, { 0x1d5d7, 1, 976 },  { 0x1d5d8, 1, 977 },  { 0x1d5d9, 1, 2322 }, { 0x1d5da, 1, 979 },  { 0x1d5db, 1, 980 },
    { 0x1d5dc, 1, 981 },  { 0x1d5dd, 1, 982 },  { 0x1d5de, 1, 983 },  { 0x1d5df, 1, 984 },  { 0x1d5e0, 1, 985 },  { 0x1d5e1, 1, 986 },  { 0x1d5e2, 1, 987 },  { 0x1d5e3, 1, 989 },  { 0x1d5e4, 1, 2312 }, { 0x1d5e5, 1, 990 },
    { 0x1d5e6, 1, 2754 }, { 0x1d5e7, 1, 991 },  { 0x1d5e8, 1, 992 },  { 0x1d5e9, 1, 2388 }, { 0x1d5ea, 1, 993 },  { 0x1d5eb, 1, 2400 }, { 0x1d5ec, 1, 2755 }, { 0x1d5ed, 1, 2320 }, { 0x1d5ee, 1, 3 },    { 0x1d5ef, 1, 997 },
    { 0x1d5f0, 1, 1023 }, { 0x1d5f1, 1, 998 },  { 0x1d5f2, 1, 999 },  { 0x1d5f3, 1, 1026 }, { 0x1d5f4, 1, 1003 }, { 0x1d5f5, 1, 588 },  { 0x1d5f6, 1, 1020 }, { 0x1d5f7, 1, 590 },  { 0x1d5f8, 1, 1004 }, { 0x1d5f9, 1, 610 },
    { 0x1d5fa, 1, 1005 }, { 0x1d5fb, 1, 2288 }, { 0x1d5fc, 1, 14 },   { 0x1d5fd, 1, 1010 }, { 0x1d5fe, 1, 2756 }, { 0x1d5ff, 1, 591 },  { 0x1d600, 1, 356 },  { 0x1d601, 1, 1011 }, { 0x1d602, 1, 1012 }, { 0x1d603, 1, 1015 },
    { 0x1d604, 1, 595 },  { 0x1d605, 1, 611 },  { 0x1d606, 1, 596 },  { 0x1d607, 1, 1053 }, { 0x1d608, 1, 973 },  { 0x1d609, 1, 975 },  { 0x1d60a, 1, 2297 }, { 0x1d60b, 1, 976 },  { 0x1d60c, 1, 977 },  { 0x1d60d, 1, 2322 },
    { 0x1d60e, 1, 979 },  { 0x1d60f, 1, 980 },  { 0x1d610, 1, 981 },  { 0x1d611, 1, 982 },  { 0x1d612, 1, 983 },  { 0x1d613, 1, 984 },  { 0x1d614, 1, 985 },  { 0x1d615, 1, 986 },  { 0x1d616, 1, 987 },  { 0x1d617, 1, 989 },
    { 0x1d618, 1, 2312 }, { 0x1d619, 1, 990 },  { 0x1d61a, 1, 2754 }, { 0x1d61b, 1, 991 },  { 0x1d61c, 1, 992 },  { 0x1d61d, 1, 2388 }, { 0x1d61e, 1, 993 },  { 0x1d61f, 1, 2400 }, { 0x1d620, 1, 2755 }, { 0x1d621, 1, 2320 },
    { 0x1d622, 1, 3 },    { 0x1d623, 1, 997 },  { 0x1d624, 1, 1023 }, { 0x1d625, 1, 998 },  { 0x1d626, 1, 999 },  { 0x1d627, 1, 1026 }, { 0x1d628, 1, 1003 }, { 0x1d629, 1, 588 },  { 0x1d62a, 1, 1020 }, { 0x1d62b, 1, 590 },
    { 0x1d62c, 1, 1004 }, { 0x1d62d, 1, 610 },  { 0x1d62e, 1, 1005 }, { 0x1d62f, 1, 2288 }, { 0x1d630, 1, 14 },   { 0x1d631, 1, 1010 }, { 0x1d632, 1, 2756 }, { 0x1d633, 1, 591 },  { 0x1d634, 1, 356 },  { 0x1d635, 1, 1011 },
    { 0x1d636, 1, 1012 }, { 0x1d637, 1, 1015 }, { 0x1d638, 1, 595 },  { 0x1d639, 1, 611 },  { 0x1d63a, 1, 596 },  { 0x1d63b, 1, 1053 }, { 0x1d63c, 1, 973 },  { 0x1d63d, 1, 975 },  { 0x1d63e, 1, 2297 }, { 0x1d63f, 1, 976 },
    { 0x1d640, 1, 977 },  { 0x1d641, 1, 2322 }, { 0x1d642, 1, 979 },  { 0x1d643, 1, 980 },  { 0x1d644, 1, 981 },  { 0x1d645, 1, 982 },  { 0x1d646, 1, 983 },  { 0x1d647, 1, 984 },  { 0x1d648, 1, 985 },  { 0x1d649, 1, 986 },
    { 0x1d64a, 1, 987 },  { 0x1d64b, 1, 989 },  { 0x1d64c, 1, 2312 }, { 0x1d64d, 1, 990 },  { 0x1d64e, 1, 2754 }, { 0x1d64f, 1, 991 },  { 0x1d650, 1, 992 },  { 0x1d651, 1, 2388 }, { 0x1d652, 1, 993 },  { 0x1d653, 1, 2400 },
    { 0x1d654, 1, 2755 }, { 0x1d655, 1, 2320 }, { 0x1d656, 1, 3 },    { 0x1d657, 1, 997 },  { 0x1d658, 1, 1023 }, { 0x1d659, 1, 998 },  { 0x1d65a, 1, 999 },  { 0x1d65b, 1, 1026 }, { 0x1d65c, 1, 1003 }, { 0x1d65d, 1, 588 },
    { 0x1d65e, 1, 1020 }, { 0x1d65f, 1, 590 },  { 0x1d660, 1, 1004 }, { 0x1d661, 1, 610 },  { 0x1d662, 1, 1005 }, { 0x1d663, 1, 2288 }, { 0x1d664, 1, 14 },   { 0x1d665, 1, 1010 }, { 0x1d666, 1, 2756 }, { 0x1d667, 1, 591 },
    { 0x1d668, 1, 356 },  { 0x1d669, 1, 1011 }, { 0x1d66a, 1, 1012 }, { 0x1d66b, 1, 1015 }, { 0x1d66c, 1, 595 },  { 0x1d66d, 1, 611 },  { 0x1d66e, 1, 596 },  { 0x1d66f, 1, 1053 }, { 0x1d670, 1, 973 },  { 0x1d671, 1, 975 },
    { 0x1d672, 1, 2297 }, { 0x1d673, 1, 976 },  { 0x1d674, 1, 977 },  { 0x1d675, 1, 2322 }, { 0x1d676, 1, 979 },  { 0x1d677, 1, 980 },  { 0x1d678, 1, 981 },  { 0x1d679, 1, 982 },  { 0x1d67a, 1, 983 },  { 0x1d67b, 1, 984 },
    { 0x1d67c, 1, 985 },  { 0x1d67d, 1, 986 },  { 0x1d67e, 1, 987 },  { 0x1d67f, 1, 989 },  { 0x1d680, 1, 2312 }, { 0x1d681, 1, 990 },  { 0x1d682, 1, 2754 }, { 0x1d683, 1, 991 },  { 0x1d684, 1, 992 },  { 0x1d685, 1, 2388 },
    { 0x1d686, 1, 993 },  { 0x1d687, 1, 2400 }, { 0x1d688, 1, 2755 }, { 0x1d689, 1, 2320 }, { 0x1d68a, 1, 3 },    { 0x1d68b, 1, 997 },  { 0x1d68c, 1, 1023 }, { 0x1d68d, 1, 998 },  { 0x1d68e, 1, 999 },  { 0x1d68f, 1, 1026 },
    { 0x1d690, 1, 1003 }, { 0x1d691, 1, 588 },  { 0x1d692, 1, 1020 }, { 0x1d693, 1, 590 },  { 0x1d694, 1, 1004 }, { 0x1d695, 1, 610 },  { 0x1d696, 1, 1005 }, { 0x1d697, 1, 2288 }, { 0x1d698, 1, 14 },   { 0x1d699, 1, 1010 },
    { 0x1d69a, 1, 2756 }, { 0x1d69b, 1, 591 },  { 0x1d69c, 1, 356 },  { 0x1d69d, 1, 1011 }, { 0x1d69e, 1, 1012 }, { 0x1d69f, 1, 1015 }, { 0x1d6a0, 1, 595 },  { 0x1d6a1, 1, 611 },  { 0x1d6a2, 1, 596 },  { 0x1d6a3, 1, 1053 },
    { 0x1d6a4, 1, 5900 }, { 0x1d6a5, 1, 5901 }, { 0x1d6a8, 1, 5902 }, { 0x1d6a9, 1, 5903 }, { 0x1d6aa, 1, 2330 }, { 0x1d6ab, 1, 5904 }, { 0x1d6ac, 1, 5905 }, { 0x1d6ad, 1, 5906 }, { 0x1d6ae, 1, 5907 }, { 0x1d6af, 1, 676 },
    { 0x1d6b0, 1, 5908 }, { 0x1d6b1, 1, 5909 }, { 0x1d6b2, 1, 5910 }, { 0x1d6b3, 1, 5911 }, { 0x1d6b4, 1, 5912 }, { 0x1d6b5, 1, 5913 }, { 0x1d6b6, 1, 5914 }, { 0x1d6b7, 1, 2331 }, { 0x1d6b8, 1, 5915 }, { 0x1d6b9, 1, 676 },
    { 0x1d6ba, 1, 678 },  { 0x1d6bb, 1, 5916 }, { 0x1d6bc, 1, 670 },  { 0x1d6bd, 1, 5917 }, { 0x1d6be, 1, 5918 }, { 0x1d6bf, 1, 5919 }, { 0x1d6c0, 1, 2321 }, { 0x1d6c1, 1, 5920 }, { 0x1d6c2, 1, 5921 }, { 0x1d6c3, 1, 668 },
    { 0x1d6c4, 1, 1017 }, { 0x1d6c5, 1, 1018 }, { 0x1d6c6, 1, 677 },  { 0x1d6c7, 1, 5922 }, { 0x1d6c8, 1, 5923 }, { 0x1d6c9, 1, 669 },  { 0x1d6ca, 1, 2133 }, { 0x1d6cb, 1, 673 },  { 0x1d6cc, 1, 5924 }, { 0x1d6cd, 1, 10 },
    { 0x1d6ce, 1, 5925 }, { 0x1d6cf, 1, 5926 }, { 0x1d6d0, 1, 5927 }, { 0x1d6d1, 1, 672 },  { 0x1d6d2, 1, 674 },  { 0x1d6d3, 1, 675 },  { 0x1d6d4, 1, 5928 }, { 0x1d6d5, 1, 5929 }, { 0x1d6d6, 1, 5930 }, { 0x1d6d7, 1, 671 },
    { 0x1d6d8, 1, 1019 }, { 0x1d6d9, 1, 5931 }, { 0x1d6da, 1, 5932 }, { 0x1d6db, 1, 5933 }, { 0x1d6dc, 1, 677 },  { 0x1d6dd, 1, 669 },  { 0x1d6de, 1, 673 },  { 0x1d6df, 1, 671 },  { 0x1d6e0, 1, 674 },  { 0x1d6e1, 1, 672 },
    { 0x1d6e2, 1, 5902 }, { 0x1d6e3, 1, 5903 }, { 0x1d6e4, 1, 2330 }, { 0x1d6e5, 1, 5904 }, { 0x1d6e6, 1, 5905 }, { 0x1d6e7, 1, 5906 }, { 0x1d6e8, 1, 5907 }, { 0x1d6e9, 1, 676 },  { 0x1d6ea, 1, 5908 }, { 0x1d6eb, 1, 5909 },
    { 0x1d6ec, 1, 5910 }, { 0x1d6ed, 1, 5911 }, { 0x1d6ee, 1, 5912 }, { 0x1d6ef, 1, 5913 }, { 0x1d6f0, 1, 5914 }, { 0x1d6f1, 1, 2331 }, { 0x1d6f2, 1, 5915 }, { 0x1d6f3, 1, 676 },  { 0x1d6f4, 1, 678 },  { 0x1d6f5, 1, 5916 },
    { 0x1d6f6, 1, 670 },  { 0x1d6f7, 1, 5917 }, { 0x1d6f8, 1, 5918 }, { 0x1d6f9, 1, 5919 }, { 0x1d6fa, 1, 2321 }, { 0x1d6fb, 1, 5920 }, { 0x1d6fc, 1, 5921 }, { 0x1d6fd, 1, 668 },  { 0x1d6fe, 1, 1017 }, { 0x1d6ff, 1, 1018 },
    { 0x1d700, 1, 677 },  { 0x1d701, 1, 5922 }, { 0x1d702, 1, 5923 }, { 0x1d703, 1, 669 },  { 0x1d704, 1, 2133 }, { 0x1d705, 1, 673 },  { 0x1d706, 1, 5924 }, { 0x1d707, 1, 10 },   { 0x1d708, 1, 5925 }, { 0x1d709, 1, 5926 },
    { 0x1d70a, 1, 5927 }, { 0x1d70b, 1, 672 },  { 0x1d70c, 1, 674 },  { 0x1d70d, 1, 675 },  { 0x1d70e, 1, 5928 }, { 0x1d70f, 1, 5929 }, { 0x1d710, 1, 5930 }, { 0x1d711, 1, 671 },  { 0x1d712, 1, 1019 }, { 0x1d713, 1, 5931 },
    { 0x1d714, 1, 5932 }, { 0x1d715, 1, 5933 }, { 0x1d716, 1, 677 },  { 0x1d717, 1, 669 },  { 0x1d718, 1, 673 },  { 0x1d719, 1, 671 },  { 0x1d71a, 1, 674 },  { 0x1d71b, 1, 672 },  { 0x1d71c, 1, 5902 }, { 0x1d71d, 1, 5903 },
    { 0x1d71e, 1, 2330 }, { 0x1d71f, 1, 5904 }, { 0x1d720, 1, 5905 }, { 0x1d721, 1, 5906 }, { 0x1d722, 1, 5907 }, { 0x1d723, 1, 676 },  { 0x1d724, 1, 5908 }, { 0x1d725, 1, 5909 }, { 0x1d726, 1, 5910 }, { 0x1d727, 1, 5911 },
    { 0x1d728, 1, 5912 }, { 0x1d729, 1, 5913 }, { 0x1d72a, 1, 5914 }, { 0x1d72b, 1, 2331 }, { 0x1d72c, 1, 5915 }, { 0x1d72d, 1, 676 },  { 0x1d72e, 1, 678 },  { 0x1d72f, 1, 5916 }, { 0x1d730, 1, 670 },  { 0x1d731, 1, 5917 },
    { 0x1d732, 1, 5918 }, { 0x1d733, 1, 5919 }, { 0x1d734, 1, 2321 }, { 0x1d735, 1, 5920 }, { 0x1d736, 1, 5921 }, { 0x1d737, 1, 668 },  { 0x1d738, 1, 1017 }, { 0x1d739, 1, 1018 }, { 0x1d73a, 1, 677 },  { 0x1d73b, 1, 5922 },
    { 0x1d73c, 1, 5923 }, { 0x1d73d, 1, 669 },  { 0x1d73e, 1, 2133 }, { 0x1d73f, 1, 673 },  { 0x1d740, 1, 5924 }, { 0x1d741, 1, 10 },   { 0x1d742, 1, 5925 }, { 0x1d743, 1, 5926 }, { 0x1d744, 1, 5927 }, { 0x1d745, 1, 672 },
    { 0x1d746, 1, 674 },  { 0x1d747, 1, 675 },  { 0x1d748, 1, 5928 }, { 0x1d749, 1, 5929 }, { 0x1d74a, 1, 5930 }, { 0x1d74b, 1, 671 },  { 0x1d74c, 1, 1019 }, { 0x1d74d, 1, 5931 }, { 0x1d74e, 1, 5932 }, { 0x1d74f, 1, 5933 },
    { 0x1d750, 1, 677 },  { 0x1d751, 1, 669 },  { 0x1d752, 1, 673 },  { 0x1d753, 1, 671 },  { 0x1d754, 1, 674 },  { 0x1d755, 1, 672 },  { 0x1d756, 1, 5902 }, { 0x1d757, 1, 5903 }, { 0x1d758, 1, 2330 }, { 0x1d759, 1, 5904 },
    { 0x1d75a, 1, 5905 }, { 0x1d75b, 1, 5906 }, { 0x1d75c, 1, 5907 }, { 0x1d75d, 1, 676 },  { 0x1d75e, 1, 5908 }, { 0x1d75f, 1, 5909 }, { 0x1d760, 1, 5910 }, { 0x1d761, 1, 5911 }, { 0x1d762, 1, 5912 }, { 0x1d763, 1, 5913 },
    { 0x1d764, 1, 5914 }, { 0x1d765, 1, 2331 }, { 0x1d766, 1, 5915 }, { 0x1d767, 1, 676 },  { 0x1d768, 1, 678 },  { 0x1d769, 1, 5916 }, { 0x1d76a, 1, 670 },  { 0x1d76b, 1, 5917 }, { 0x1d76c, 1, 5918 }, { 0x1d76d, 1, 5919 },
    { 0x1d76e, 1, 2321 }, { 0x1d76f, 1, 5920 }, { 0x1d770, 1, 5921 }, { 0x1d771, 1, 668 },  { 0x1d772, 1, 1017 }, { 0x1d773, 1, 1018 }, { 0x1d774, 1, 677 },  { 0x1d775, 1, 5922 }, { 0x1d776, 1, 5923 }, { 0x1d777, 1, 669 },
    { 0x1d778, 1, 2133 }, { 0x1d779, 1, 673 },  { 0x1d77a, 1, 5924 }, { 0x1d77b, 1, 10 },   { 0x1d77c, 1, 5925 }, { 0x1d77d, 1, 5926 }, { 0x1d77e, 1, 5927 }, { 0x1d77f, 1, 672 },  { 0x1d780, 1, 674 },  { 0x1d781, 1, 675 },
    { 0x1d782, 1, 5928 }, { 0x1d783, 1, 5929 }, { 0x1d784, 1, 5930 }, { 0x1d785, 1, 671 },  { 0x1d786, 1, 1019 }, { 0x1d787, 1, 5931 }, { 0x1d788, 1, 5932 }, { 0x1d789, 1, 5933 }, { 0x1d78a, 1, 677 },  { 0x1d78b, 1, 669 },
    { 0x1d78c, 1, 673 },  { 0x1d78d, 1, 671 },  { 0x1d78e, 1, 674 },  { 0x1d78f, 1, 672 },  { 0x1d790, 1, 5902 }, { 0x1d791, 1, 5903 }, { 0x1d792, 1, 2330 }, { 0x1d793, 1, 5904 }, { 0x1d794, 1, 5905 }, { 0x1d795, 1, 5906 },
    { 0x1d796, 1, 5907 }, { 0x1d797, 1, 676 },  { 0x1d798, 1, 5908 }, { 0x1d799, 1, 5909 }, { 0x1d79a, 1, 5910 }, { 0x1d79b, 1, 5911 }, { 0x1d79c, 1, 5912 }, { 0x1d79d, 1, 5913 }, { 0x1d79e, 1, 5914 }, { 0x1d79f, 1, 2331 },
    { 0x1d7a0, 1, 5915 }, { 0x1d7a1, 1, 676 },  { 0x1d7a2, 1, 678 },  { 0x1d7a3, 1, 5916 }, { 0x1d7a4, 1, 670 },  { 0x1d7a5, 1, 5917 }, { 0x1d7a6, 1, 5918 }, { 0x1d7a7, 1, 5919 }, { 0x1d7a8, 1, 2321 }, { 0x1d7a9, 1, 5920 },
    { 0x1d7aa, 1, 5921 }, { 0x1d7ab, 1, 668 },  { 0x1d7ac, 1, 1017 }, { 0x1d7ad, 1, 1018 }, { 0x1d7ae, 1, 677 },  { 0x1d7af, 1, 5922 }, { 0x1d7b0, 1, 5923 }, { 0x1d7b1, 1, 669 },  { 0x1d7b2, 1, 2133 }, { 0x1d7b3, 1, 673 },
    { 0x1d7b4, 1, 5924 }, { 0x1d7b5, 1, 10 },   { 0x1d7b6, 1, 5925 }, { 0x1d7b7, 1, 5926 }, { 0x1d7b8, 1, 5927 }, { 0x1d7b9, 1, 672 },  { 0x1d7ba, 1, 674 },  { 0x1d7bb, 1, 675 },  { 0x1d7bc, 1, 5928 }, { 0x1d7bd, 1, 5929 },
    { 0x1d7be, 1, 5930 }, { 0x1d7bf, 1, 671 },  { 0x1d7c0, 1, 1019 }, { 0x1d7c1, 1, 5931 }, { 0x1d7c2, 1, 5932 }, { 0x1d7c3, 1, 5933 }, { 0x1d7c4, 1, 677 },  { 0x1d7c5, 1, 669 },  { 0x1d7c6, 1, 673 },  { 0x1d7c7, 1, 671 },
    { 0x1d7c8, 1, 674 },  { 0x1d7c9, 1, 672 },  { 0x1d7ca, 1, 5934 }, { 0x1d7cb, 1, 5935 }, { 0x1d7ce, 1, 2276 }, { 0x1d7cf, 1, 13 },   { 0x1d7d0, 1, 6 },    { 0x1d7d1, 1, 7 },    { 0x1d7d2, 1, 2277 }, { 0x1d7d3, 1, 2278 },
    { 0x1d7d4, 1, 2279 }, { 0x1d7d5, 1, 2280 }, { 0x1d7d6, 1, 2281 }, { 0x1d7d7, 1, 2282 }, { 0x1d7d8, 1, 2276 }, { 0x1d7d9, 1, 13 },   { 0x1d7da, 1, 6 },    { 0x1d7db, 1, 7 },    { 0x1d7dc, 1, 2277 }, { 0x1d7dd, 1, 2278 },
    { 0x1d7de, 1, 2279 }, { 0x1d7df, 1, 2280 }, { 0x1d7e0, 1, 2281 }, { 0x1d7e1, 1, 2282 }, { 0x1d7e2, 1, 2276 }, { 0x1d7e3, 1, 13 },   { 0x1d7e4, 1, 6 },    { 0x1d7e5, 1, 7 },    { 0x1d7e6, 1, 2277 }, { 0x1d7e7, 1, 2278 },
    { 0x1d7e8, 1, 2279 }, { 0x1d7e9, 1, 2280 }, { 0x1d7ea, 1, 2281 }, { 0x1d7eb, 1, 2282 }, { 0x1d7ec, 1, 2276 }, { 0x1d7ed, 1, 13 },   { 0x1d7ee, 1, 6 },    { 0x1d7ef, 1, 7 },    { 0x1d7f0, 1, 2277 }, { 0x1d7f1, 1, 2278 },
    { 0x1d7f2, 1, 2279 }, { 0x1d7f3, 1, 2280 }, { 0x1d7f4, 1, 2281 }, { 0x1d7f5, 1, 2282 }, { 0x1d7f6, 1, 2276 }, { 0x1d7f7, 1, 13 },   { 0x1d7f8, 1, 6 },    { 0x1d7f9, 1, 7 },    { 0x1d7fa, 1, 2277 }, { 0x1d7fb, 1, 2278 },
    { 0x1d7fc, 1, 2279 }, { 0x1d7fd, 1, 2280 }, { 0x1d7fe, 1, 2281 }, { 0x1d7ff, 1, 2282 }, { 0x1ee00, 1, 5766 }, { 0x1ee01, 1, 5767 }, { 0x1ee02, 1, 5771 }, { 0x1ee03, 1, 5774 }, { 0x1ee05, 1, 5793 }, { 0x1ee06, 1, 5777 },
    { 0x1ee07, 1, 5772 }, { 0x1ee08, 1, 5782 }, { 0x1ee09, 1, 5794 }, { 0x1ee0a, 1, 5788 }, { 0x1ee0b, 1, 5789 }, { 0x1ee0c, 1, 5790 }, { 0x1ee0d, 1, 5791 }, { 0x1ee0e, 1, 5778 }, { 0x1ee0f, 1, 5784 }, { 0x1ee10, 1, 5786 },
    { 0x1ee11, 1, 5780 }, { 0x1ee12, 1, 5787 }, { 0x1ee13, 1, 5776 }, { 0x1ee14, 1, 5779 }, { 0x1ee15, 1, 5769 }, { 0x1ee16, 1, 5770 }, { 0x1ee17, 1, 5773 }, { 0x1ee18, 1, 5775 }, { 0x1ee19, 1, 5781 }, { 0x1ee1a, 1, 5783 },
    { 0x1ee1b, 1, 5785 }, { 0x1ee1c, 1, 5936 }, { 0x1ee1d, 1, 4972 }, { 0x1ee1e, 1, 5937 }, { 0x1ee1f, 1, 5938 }, { 0x1ee21, 1, 5767 }, { 0x1ee22, 1, 5771 }, { 0x1ee24, 1, 5792 }, { 0x1ee27, 1, 5772 }, { 0x1ee29, 1, 5794 },
    { 0x1ee2a, 1, 5788 }, { 0x1ee2b, 1, 5789 }, { 0x1ee2c, 1, 5790 }, { 0x1ee2d, 1, 5791 }, { 0x1ee2e, 1, 5778 }, { 0x1ee2f, 1, 5784 }, { 0x1ee30, 1, 5786 }, { 0x1ee31, 1, 5780 }, { 0x1ee32, 1, 5787 }, { 0x1ee34, 1, 5779 },
    { 0x1ee35, 1, 5769 }, { 0x1ee36, 1, 5770 }, { 0x1ee37, 1, 5773 }, { 0x1ee39, 1, 5781 }, { 0x1ee3b, 1, 5785 }, { 0x1ee42, 1, 5771 }, { 0x1ee47, 1, 5772 }, { 0x1ee49, 1, 5794 }, { 0x1ee4b, 1, 5789 }, { 0x1ee4d, 1, 5791 },
    { 0x1ee4e, 1, 5778 }, { 0x1ee4f, 1, 5784 }, { 0x1ee51, 1, 5780 }, { 0x1ee52, 1, 5787 }, { 0x1ee54, 1, 5779 }, { 0x1ee57, 1, 5773 }, { 0x1ee59, 1, 5781 }, { 0x1ee5b, 1, 5785 }, { 0x1ee5d, 1, 4972 }, { 0x1ee5f, 1, 5938 },
    { 0x1ee61, 1, 5767 }, { 0x1ee62, 1, 5771 }, { 0x1ee64, 1, 5792 }, { 0x1ee67, 1, 5772 }, { 0x1ee68, 1, 5782 }, { 0x1ee69, 1, 5794 }, { 0x1ee6a, 1, 5788 }, { 0x1ee6c, 1, 5790 }, { 0x1ee6d, 1, 5791 }, { 0x1ee6e, 1, 5778 },
    { 0x1ee6f, 1, 5784 }, { 0x1ee70, 1, 5786 }, { 0x1ee71, 1, 5780 }, { 0x1ee72, 1, 5787 }, { 0x1ee74, 1, 5779 }, { 0x1ee75, 1, 5769 }, { 0x1ee76, 1, 5770 }, { 0x1ee77, 1, 5773 }, { 0x1ee79, 1, 5781 }, { 0x1ee7a, 1, 5783 },
    { 0x1ee7b, 1, 5785 }, { 0x1ee7c, 1, 5936 }, { 0x1ee7e, 1, 5937 }, { 0x1ee80, 1, 5766 }, { 0x1ee81, 1, 5767 }, { 0x1ee82, 1, 5771 }, { 0x1ee83, 1, 5774 }, { 0x1ee84, 1, 5792 }, { 0x1ee85, 1, 5793 }, { 0x1ee86, 1, 5777 },
    { 0x1ee87, 1, 5772 }, { 0x1ee88, 1, 5782 }, { 0x1ee89, 1, 5794 }, { 0x1ee8b, 1, 5789 }, { 0x1ee8c, 1, 5790 }, { 0x1ee8d, 1, 5791 }, { 0x1ee8e, 1, 5778 }, { 0x1ee8f, 1, 5784 }, { 0x1ee90, 1, 5786 }, { 0x1ee91, 1, 5780 },
    { 0x1ee92, 1, 5787 }, { 0x1ee93, 1, 5776 }, { 0x1ee94, 1, 5779 }, { 0x1ee95, 1, 5769 }, { 0x1ee96, 1, 5770 }, { 0x1ee97, 1, 5773 }, { 0x1ee98, 1, 5775 }, { 0x1ee99, 1, 5781 }, { 0x1ee9a, 1, 5783 }, { 0x1ee9b, 1, 5785 },
    { 0x1eea1, 1, 5767 }, { 0x1eea2, 1, 5771 }, { 0x1eea3, 1, 5774 }, { 0x1eea5, 1, 5793 }, { 0x1eea6, 1, 5777 }, { 0x1eea7, 1, 5772 }, { 0x1eea8, 1, 5782 }, { 0x1eea9, 1, 5794 }, { 0x1eeab, 1, 5789 }, { 0x1eeac, 1, 5790 },
    { 0x1eead, 1, 5791 }, { 0x1eeae, 1, 5778 }, { 0x1eeaf, 1, 5784 }, { 0x1eeb0, 1, 5786 }, { 0x1eeb1, 1, 5780 }, { 0x1eeb2, 1, 5787 }, { 0x1eeb3, 1, 5776 }, { 0x1eeb4, 1, 5779 }, { 0x1eeb5, 1, 5769 }, { 0x1eeb6, 1, 5770 },
    { 0x1eeb7, 1, 5773 }, { 0x1eeb8, 1, 5775 }, { 0x1eeb9, 1, 5781 }, { 0x1eeba, 1, 5783 }, { 0x1eebb, 1, 5785 }, { 0x1f100, 2, 5939 }, { 0x1f101, 2, 5941 }, { 0x1f102, 2, 5943 }, { 0x1f103, 2, 5945 }, { 0x1f104, 2, 5947 },
    { 0x1f105, 2, 5949 }, { 0x1f106, 2, 5951 }, { 0x1f107, 2, 5953 }, { 0x1f108, 2, 5955 }, { 0x1f109, 2, 5957 }, { 0x1f10a, 2, 5959 }, { 0x1f110, 3, 5961 }, { 0x1f111, 3, 5964 }, { 0x1f112, 3, 5967 }, { 0x1f113, 3, 5970 },
    { 0x1f114, 3, 5973 }, { 0x1f115, 3, 5976 }, { 0x1f116, 3, 5979 }, { 0x1f117, 3, 5982 }, { 0x1f118, 3, 5985 }, { 0x1f119, 3, 5988 }, { 0x1f11a, 3, 5991 }, { 0x1f11b, 3, 5994 }, { 0x1f11c, 3, 5997 }, { 0x1f11d, 3, 6000 },
    { 0x1f11e, 3, 6003 }, { 0x1f11f, 3, 6006 }, { 0x1f120, 3, 6009 }, { 0x1f121, 3, 6012 }, { 0x1f122, 3, 6015 }, { 0x1f123, 3, 6018 }, { 0x1f124, 3, 6021 }, { 0x1f125, 3, 6024 }, { 0x1f126, 3, 6027 }, { 0x1f127, 3, 6030 },
    { 0x1f128, 3, 6033 }, { 0x1f129, 3, 6036 }, { 0x1f12a, 3, 6039 }, { 0x1f12b, 1, 2297 }, { 0x1f12c, 1, 990 },  { 0x1f12d, 2, 6042 }, { 0x1f12e, 2, 6044 }, { 0x1f130, 1, 973 },  { 0x1f131, 1, 975 },  { 0x1f132, 1, 2297 },
    { 0x1f133, 1, 976 },  { 0x1f134, 1, 977 },  { 0x1f135, 1, 2322 }, { 0x1f136, 1, 979 },  { 0x1f137, 1, 980 },  { 0x1f138, 1, 981 },  { 0x1f139, 1, 982 },  { 0x1f13a, 1, 983 },  { 0x1f13b, 1, 984 },  { 0x1f13c, 1, 985 },
    { 0x1f13d, 1, 986 },  { 0x1f13e, 1, 987 },  { 0x1f13f, 1, 989 },  { 0x1f140, 1, 2312 }, { 0x1f141, 1, 990 },  { 0x1f142, 1, 2754 }, { 0x1f143, 1, 991 },  { 0x1f144, 1, 992 },  { 0x1f145, 1, 2388 }, { 0x1f146, 1, 993 },
    { 0x1f147, 1, 2400 }, { 0x1f148, 1, 2755 }, { 0x1f149, 1, 2320 }, { 0x1f14a, 2, 6046 }, { 0x1f14b, 2, 4253 }, { 0x1f14c, 2, 6048 }, { 0x1f14d, 2, 6050 }, { 0x1f14e, 3, 6052 }, { 0x1f14f, 2, 6055 }, { 0x1f16a, 2, 6057 },
    { 0x1f16b, 2, 6059 }, { 0x1f190, 2, 6061 }, { 0x1f200, 2, 6063 }, { 0x1f201, 2, 6065 }, { 0x1f202, 1, 3626 }, { 0x1f210, 1, 2837 }, { 0x1f211, 1, 6067 }, { 0x1f212, 1, 6068 }, { 0x1f213, 2, 3077 }, { 0x1f214, 1, 2780 },
    { 0x1f215, 1, 6069 }, { 0x1f216, 1, 6070 }, { 0x1f217, 1, 3217 }, { 0x1f218, 1, 6071 }, { 0x1f219, 1, 6072 }, { 0x1f21a, 1, 6073 }, { 0x1f21b, 1, 4618 }, { 0x1f21c, 1, 6074 }, { 0x1f21d, 1, 6075 }, { 0x1f21e, 1, 6076 },
    { 0x1f21f, 1, 6077 }, { 0x1f220, 1, 6078 }, { 0x1f221, 1, 6079 }, { 0x1f222, 1, 2873 }, { 0x1f223, 1, 6080 }, { 0x1f224, 1, 6081 }, { 0x1f225, 1, 6082 }, { 0x1f226, 1, 6083 }, { 0x1f227, 1, 6084 }, { 0x1f228, 1, 6085 },
    { 0x1f229, 1, 2774 }, { 0x1f22a, 1, 3209 }, { 0x1f22b, 1, 6086 }, { 0x1f22c, 1, 3539 }, { 0x1f22d, 1, 3212 }, { 0x1f22e, 1, 3540 }, { 0x1f22f, 1, 6087 }, { 0x1f230, 1, 2929 }, { 0x1f231, 1, 6088 }, { 0x1f232, 1, 6089 },
    { 0x1f233, 1, 6090 }, { 0x1f234, 1, 6091 }, { 0x1f235, 1, 6092 }, { 0x1f236, 1, 3522 }, { 0x1f237, 1, 2847 }, { 0x1f238, 1, 6093 }, { 0x1f239, 1, 6094 }, { 0x1f23a, 1, 6095 }, { 0x1f23b, 1, 6096 }, { 0x1f240, 3, 6097 },
    { 0x1f241, 3, 6100 }, { 0x1f242, 3, 6103 }, { 0x1f243, 3, 6106 }, { 0x1f244, 3, 6109 }, { 0x1f245, 3, 6112 }, { 0x1f246, 3, 6115 }, { 0x1f247, 3, 6118 }, { 0x1f248, 3, 6121 }, { 0x1f250, 1, 6124 }, { 0x1f251, 1, 6125 },
    { 0x2f800, 1, 6126 }, { 0x2f801, 1, 6127 }, { 0x2f802, 1, 6128 }, { 0x2f803, 1, 6129 }, { 0x2f804, 1, 6130 }, { 0x2f805, 1, 4711 }, { 0x2f806, 1, 6131 }, { 0x2f807, 1, 6132 }, { 0x2f808, 1, 6133 }, { 0x2f809, 1, 6134 },
    { 0x2f80a, 1, 4712 }, { 0x2f80b, 1, 6135 }, { 0x2f80c, 1, 6136 }, { 0x2f80d, 1, 6137 }, { 0x2f80e, 1, 4713 }, { 0x2f80f, 1, 6138 }, { 0x2f810, 1, 6139 }, { 0x2f811, 1, 6140 }, { 0x2f812, 1, 6141 }, { 0x2f813, 1, 6142 },
    { 0x2f814, 1, 6143 }, { 0x2f815, 1, 6076 }, { 0x2f816, 1, 6144 }, { 0x2f817, 1, 6145 }, { 0x2f818, 1, 6146 }, { 0x2f819, 1, 6147 }, { 0x2f81a, 1, 6148 }, { 0x2f81b, 1, 4768 }, { 0x2f81c, 1, 6149 }, { 0x2f81d, 1, 2790 },
    { 0x2f81e, 1, 6150 }, { 0x2f81f, 1, 6151 }, { 0x2f820, 1, 6152 }, { 0x2f821, 1, 6153 }, { 0x2f822, 1, 6094 }, { 0x2f823, 1, 6154 }, { 0x2f824, 1, 6155 }, { 0x2f825, 1, 4773 }, { 0x2f826, 1, 4714 }, { 0x2f827, 1, 4715 },
    { 0x2f828, 1, 4774 }, { 0x2f829, 1, 6156 }, { 0x2f82a, 1, 6157 }, { 0x2f82b, 1, 4532 }, { 0x2f82c, 1, 6158 }, { 0x2f82d, 1, 4716 }, { 0x2f82e, 1, 6159 }, { 0x2f82f, 1, 6160 }, { 0x2f830, 1, 6161 }, { 0x2f831, 1, 6162 },
    { 0x2f832, 1, 6162 }, { 0x2f833, 1, 6162 }, { 0x2f834, 1, 6163 }, { 0x2f835, 1, 6164 }, { 0x2f836, 1, 6165 }, { 0x2f837, 1, 6166 }, { 0x2f838, 1, 6167 }, { 0x2f839, 1, 6168 }, { 0x2f83a, 1, 6169 }, { 0x2f83b, 1, 6170 },
    { 0x2f83c, 1, 6171 }, { 0x2f83d, 1, 6172 }, { 0x2f83e, 1, 6173 }, { 0x2f83f, 1, 6174 }, { 0x2f840, 1, 6175 }, { 0x2f841, 1, 6176 }, { 0x2f842, 1, 6177 }, { 0x2f843, 1, 6178 }, { 0x2f844, 1, 6179 }, { 0x2f845, 1, 6180 },
    { 0x2f846, 1, 6180 }, { 0x2f847, 1, 4776 }, { 0x2f848, 1, 6181 }, { 0x2f849, 1, 6182 }, { 0x2f84a, 1, 6183 }, { 0x2f84b, 1, 6184 }, { 0x2f84c, 1, 4718 }, { 0x2f84d, 1, 6185 }, { 0x2f84e, 1, 6186 }, { 0x2f84f, 1, 6187 },
    { 0x2f850, 1, 4678 }, { 0x2f851, 1, 6188 }, { 0x2f852, 1, 6189 }, { 0x2f853, 1, 6190 }, { 0x2f854, 1, 6191 }, { 0x2f855, 1, 6192 }, { 0x2f856, 1, 6193 }, { 0x2f857, 1, 6194 }, { 0x2f858, 1, 6195 }, { 0x2f859, 1, 6196 },
    { 0x2f85a, 1, 6197 }, { 0x2f85b, 1, 6198 }, { 0x2f85c, 1, 6199 }, { 0x2f85d, 1, 6069 }, { 0x2f85e, 1, 6200 }, { 0x2f85f, 1, 6201 }, { 0x2f860, 1, 6202 }, { 0x2f861, 1, 6203 }, { 0x2f862, 1, 6204 }, { 0x2f863, 1, 6205 },
    { 0x2f864, 1, 6206 }, { 0x2f865, 1, 6207 }, { 0x2f866, 1, 6208 }, { 0x2f867, 1, 6209 }, { 0x2f868, 1, 6210 }, { 0x2f869, 1, 6211 }, { 0x2f86a, 1, 6212 }, { 0x2f86b, 1, 6212 }, { 0x2f86c, 1, 6213 }, { 0x2f86d, 1, 6214 },
    { 0x2f86e, 1, 6215 }, { 0x2f86f, 1, 4528 }, { 0x2f870, 1, 6216 }, { 0x2f871, 1, 6217 }, { 0x2f872, 1, 6218 }, { 0x2f873, 1, 6219 }, { 0x2f874, 1, 6220 }, { 0x2f875, 1, 2816 }, { 0x2f876, 1, 6221 }, { 0x2f877, 1, 6222 },
    { 0x2f878, 1, 2818 }, { 0x2f879, 1, 6223 }, { 0x2f87a, 1, 6224 }, { 0x2f87b, 1, 6225 }, { 0x2f87c, 1, 6226 }, { 0x2f87d, 1, 6227 }, { 0x2f87e, 1, 6228 }, { 0x2f87f, 1, 6229 }, { 0x2f880, 1, 6230 }, { 0x2f881, 1, 6231 },
    { 0x2f882, 1, 6232 }, { 0x2f883, 1, 6233 }, { 0x2f884, 1, 6234 }, { 0x2f885, 1, 6235 }, { 0x2f886, 1, 6236 }, { 0x2f887, 1, 6237 }, { 0x2f888, 1, 6238 }, { 0x2f889, 1, 6239 }, { 0x2f88a, 1, 6240 }, { 0x2f88b, 1, 6241 },
    { 0x2f88c, 1, 6242 }, { 0x2f88d, 1, 6243 }, { 0x2f88e, 1, 4476 }, { 0x2f88f, 1, 6244 }, { 0x2f890, 1, 2828 }, { 0x2f891, 1, 6245 }, { 0x2f892, 1, 6245 }, { 0x2f893, 1, 6246 }, { 0x2f894, 1, 6247 }, { 0x2f895, 1, 6247 },
    { 0x2f896, 1, 6248 }, { 0x2f897, 1, 6249 }, { 0x2f898, 1, 6250 }, { 0x2f899, 1, 6251 }, { 0x2f89a, 1, 6252 }, { 0x2f89b, 1, 6253 }, { 0x2f89c, 1, 6254 }, { 0x2f89d, 1, 6255 }, { 0x2f89e, 1, 6256 }, { 0x2f89f, 1, 6257 },
    { 0x2f8a0, 1, 6258 }, { 0x2f8a1, 1, 6259 }, { 0x2f8a2, 1, 6260 }, { 0x2f8a3, 1, 4723 }, { 0x2f8a4, 1, 6261 }, { 0x2f8a5, 1, 6262 }, { 0x2f8a6, 1, 6263 }, { 0x2f8a7, 1, 6264 }, { 0x2f8a8, 1, 4788 }, { 0x2f8a9, 1, 6264 },
    { 0x2f8aa, 1, 6265 }, { 0x2f8ab, 1, 4725 }, { 0x2f8ac, 1, 6266 }, { 0x2f8ad, 1, 6267 }, { 0x2f8ae, 1, 6268 }, { 0x2f8af, 1, 6269 }, { 0x2f8b0, 1, 4726 }, { 0x2f8b1, 1, 4449 }, { 0x2f8b2, 1, 6270 }, { 0x2f8b3, 1, 6271 },
    { 0x2f8b4, 1, 6272 }, { 0x2f8b5, 1, 6273 }, { 0x2f8b6, 1, 6274 }, { 0x2f8b7, 1, 6275 }, { 0x2f8b8, 1, 6276 }, { 0x2f8b9, 1, 6277 }, { 0x2f8ba, 1, 6278 }, { 0x2f8bb, 1, 6279 }, { 0x2f8bc, 1, 6280 }, { 0x2f8bd, 1, 6281 },
    { 0x2f8be, 1, 6282 }, { 0x2f8bf, 1, 6283 }, { 0x2f8c0, 1, 6284 }, { 0x2f8c1, 1, 6285 }, { 0x2f8c2, 1, 6286 }, { 0x2f8c3, 1, 6287 }, { 0x2f8c4, 1, 6288 }, { 0x2f8c5, 1, 6289 }, { 0x2f8c6, 1, 6290 }, { 0x2f8c7, 1, 6291 },
    { 0x2f8c8, 1, 4727 }, { 0x2f8c9, 1, 6292 }, { 0x2f8ca, 1, 6293 }, { 0x2f8cb, 1, 6294 }, { 0x2f8cc, 1, 6295 }, { 0x2f8cd, 1, 6296 }, { 0x2f8ce, 1, 6297 }, { 0x2f8cf, 1, 4729 }, { 0x2f8d0, 1, 6298 }, { 0x2f8d1, 1, 6299 },
    { 0x2f8d2, 1, 6300 }, { 0x2f8d3, 1, 6301 }, { 0x2f8d4, 1, 6302 }, { 0x2f8d5, 1, 6303 }, { 0x2f8d6, 1, 6304 }, { 0x2f8d7, 1, 6305 }, { 0x2f8d8, 1, 4477 }, { 0x2f8d9, 1, 4796 }, { 0x2f8da, 1, 6306 }, { 0x2f8db, 1, 6307 },
    { 0x2f8dc, 1, 6308 }, { 0x2f8dd, 1, 6309 }, { 0x2f8de, 1, 6310 }, { 0x2f8df, 1, 6311 }, { 0x2f8e0, 1, 6312 }, { 0x2f8e1, 1, 6313 }, { 0x2f8e2, 1, 4730 }, { 0x2f8e3, 1, 6314 }, { 0x2f8e4, 1, 6315 }, { 0x2f8e5, 1, 6316 },
    { 0x2f8e6, 1, 6317 }, { 0x2f8e7, 1, 4838 }, { 0x2f8e8, 1, 6318 }, { 0x2f8e9, 1, 6319 }, { 0x2f8ea, 1, 6320 }, { 0x2f8eb, 1, 6321 }, { 0x2f8ec, 1, 6322 }, { 0x2f8ed, 1, 6323 }, { 0x2f8ee, 1, 6324 }, { 0x2f8ef, 1, 6325 },
    { 0x2f8f0, 1, 6326 }, { 0x2f8f1, 1, 6327 }, { 0x2f8f2, 1, 6328 }, { 0x2f8f3, 1, 6329 }, { 0x2f8f4, 1, 6330 }, { 0x2f8f5, 1, 4545 }, { 0x2f8f6, 1, 6331 }, { 0x2f8f7, 1, 6332 }, { 0x2f8f8, 1, 6333 }, { 0x2f8f9, 1, 6334 },
    { 0x2f8fa, 1, 6335 }, { 0x2f8fb, 1, 6336 }, { 0x2f8fc, 1, 6337 }, { 0x2f8fd, 1, 6338 }, { 0x2f8fe, 1, 6339 }, { 0x2f8ff, 1, 6340 }, { 0x2f900, 1, 6341 }, { 0x2f901, 1, 4731 }, { 0x2f902, 1, 4628 }, { 0x2f903, 1, 6342 },
    { 0x2f904, 1, 6343 }, { 0x2f905, 1, 6344 }, { 0x2f906, 1, 6345 }, { 0x2f907, 1, 6346 }, { 0x2f908, 1, 6347 }, { 0x2f909, 1, 6348 }, { 0x2f90a, 1, 6349 }, { 0x2f90b, 1, 4799 }, { 0x2f90c, 1, 6350 }, { 0x2f90d, 1, 6351 },
    { 0x2f90e, 1, 6352 }, { 0x2f90f, 1, 6353 }, { 0x2f910, 1, 6354 }, { 0x2f911, 1, 6355 }, { 0x2f912, 1, 6356 }, { 0x2f913, 1, 6357 }, { 0x2f914, 1, 4800 }, { 0x2f915, 1, 6358 }, { 0x2f916, 1, 6359 }, { 0x2f917, 1, 6360 },
    { 0x2f918, 1, 6361 }, { 0x2f919, 1, 6362 }, { 0x2f91a, 1, 6363 }, { 0x2f91b, 1, 6364 }, { 0x2f91c, 1, 6365 }, { 0x2f91d, 1, 6366 }, { 0x2f91e, 1, 6367 }, { 0x2f91f, 1, 6368 }, { 0x2f920, 1, 6369 }, { 0x2f921, 1, 4802 },
    { 0x2f922, 1, 6370 }, { 0x2f923, 1, 6371 }, { 0x2f924, 1, 6372 }, { 0x2f925, 1, 6373 }, { 0x2f926, 1, 6374 }, { 0x2f927, 1, 6375 }, { 0x2f928, 1, 6376 }, { 0x2f929, 1, 6377 }, { 0x2f92a, 1, 6378 }, { 0x2f92b, 1, 6379 },
    { 0x2f92c, 1, 6380 }, { 0x2f92d, 1, 6380 }, { 0x2f92e, 1, 6381 }, { 0x2f92f, 1, 6382 }, { 0x2f930, 1, 4804 }, { 0x2f931, 1, 6383 }, { 0x2f932, 1, 6384 }, { 0x2f933, 1, 6385 }, { 0x2f934, 1, 6386 }, { 0x2f935, 1, 6387 },
    { 0x2f936, 1, 6388 }, { 0x2f937, 1, 6389 }, { 0x2f938, 1, 4531 }, { 0x2f939, 1, 6390 }, { 0x2f93a, 1, 6391 }, { 0x2f93b, 1, 6392 }, { 0x2f93c, 1, 6393 }, { 0x2f93d, 1, 6394 }, { 0x2f93e, 1, 6395 }, { 0x2f93f, 1, 6396 },
    { 0x2f940, 1, 4810 }, { 0x2f941, 1, 6397 }, { 0x2f942, 1, 6398 }, { 0x2f943, 1, 6399 }, { 0x2f944, 1, 6400 }, { 0x2f945, 1, 6401 }, { 0x2f946, 1, 6402 }, { 0x2f947, 1, 6402 }, { 0x2f948, 1, 4811 }, { 0x2f949, 1, 4840 },
    { 0x2f94a, 1, 6403 }, { 0x2f94b, 1, 6404 }, { 0x2f94c, 1, 6405 }, { 0x2f94d, 1, 6406 }, { 0x2f94e, 1, 6407 }, { 0x2f94f, 1, 4494 }, { 0x2f950, 1, 4813 }, { 0x2f951, 1, 6408 }, { 0x2f952, 1, 6409 }, { 0x2f953, 1, 4741 },
    { 0x2f954, 1, 6410 }, { 0x2f955, 1, 6411 }, { 0x2f956, 1, 4698 }, { 0x2f957, 1, 6412 }, { 0x2f958, 1, 6413 }, { 0x2f959, 1, 4744 }, { 0x2f95a, 1, 6414 }, { 0x2f95b, 1, 6415 }, { 0x2f95c, 1, 6416 }, { 0x2f95d, 1, 6417 },
    { 0x2f95e, 1, 6417 }, { 0x2f95f, 1, 6418 }, { 0x2f960, 1, 6419 }, { 0x2f961, 1, 6420 }, { 0x2f962, 1, 6421 }, { 0x2f963, 1, 6422 }, { 0x2f964, 1, 6423 }, { 0x2f965, 1, 6424 }, { 0x2f966, 1, 6425 }, { 0x2f967, 1, 6426 },
    { 0x2f968, 1, 6427 }, { 0x2f969, 1, 6428 }, { 0x2f96a, 1, 6429 }, { 0x2f96b, 1, 6430 }, { 0x2f96c, 1, 6431 }, { 0x2f96d, 1, 6432 }, { 0x2f96e, 1, 6433 }, { 0x2f96f, 1, 6434 }, { 0x2f970, 1, 6435 }, { 0x2f971, 1, 6436 },
    { 0x2f972, 1, 6437 }, { 0x2f973, 1, 6438 }, { 0x2f974, 1, 6439 }, { 0x2f975, 1, 6440 }, { 0x2f976, 1, 6441 }, { 0x2f977, 1, 6442 }, { 0x2f978, 1, 6443 }, { 0x2f979, 1, 6444 }, { 0x2f97a, 1, 4750 }, { 0x2f97b, 1, 6445 },
    { 0x2f97c, 1, 6446 }, { 0x2f97d, 1, 6447 }, { 0x2f97e, 1, 6448 }, { 0x2f97f, 1, 6449 }, { 0x2f980, 1, 6450 }, { 0x2f981, 1, 6451 }, { 0x2f982, 1, 6452 }, { 0x2f983, 1, 6453 }, { 0x2f984, 1, 6454 }, { 0x2f985, 1, 6455 },
    { 0x2f986, 1, 6456 }, { 0x2f987, 1, 6457 }, { 0x2f988, 1, 6458 }, { 0x2f989, 1, 6459 }, { 0x2f98a, 1, 6460 }, { 0x2f98b, 1, 6246 }, { 0x2f98c, 1, 6461 }, { 0x2f98d, 1, 6462 }, { 0x2f98e, 1, 6463 }, { 0x2f98f, 1, 6464 },
    { 0x2f990, 1, 6465 }, { 0x2f991, 1, 6466 }, { 0x2f992, 1, 6467 }, { 0x2f993, 1, 6468 }, { 0x2f994, 1, 6469 }, { 0x2f995, 1, 6470 }, { 0x2f996, 1, 6471 }, { 0x2f997, 1, 6472 }, { 0x2f998, 1, 4548 }, { 0x2f999, 1, 6473 },
    { 0x2f99a, 1, 6474 }, { 0x2f99b, 1, 6475 }, { 0x2f99c, 1, 6476 }, { 0x2f99d, 1, 6477 }, { 0x2f99e, 1, 6478 }, { 0x2f99f, 1, 4753 }, { 0x2f9a0, 1, 6479 }, { 0x2f9a1, 1, 6480 }, { 0x2f9a2, 1, 6481 }, { 0x2f9a3, 1, 6482 },
    { 0x2f9a4, 1, 6483 }, { 0x2f9a5, 1, 6484 }, { 0x2f9a6, 1, 6485 }, { 0x2f9a7, 1, 6486 }, { 0x2f9a8, 1, 6487 }, { 0x2f9a9, 1, 6488 }, { 0x2f9aa, 1, 6489 }, { 0x2f9ab, 1, 6490 }, { 0x2f9ac, 1, 6491 }, { 0x2f9ad, 1, 6492 },
    { 0x2f9ae, 1, 6493 }, { 0x2f9af, 1, 6494 }, { 0x2f9b0, 1, 6495 }, { 0x2f9b1, 1, 6496 }, { 0x2f9b2, 1, 6497 }, { 0x2f9b3, 1, 6498 }, { 0x2f9b4, 1, 4489 }, { 0x2f9b5, 1, 6499 }, { 0x2f9b6, 1, 6500 }, { 0x2f9b7, 1, 6501 },
    { 0x2f9b8, 1, 6502 }, { 0x2f9b9, 1, 6503 }, { 0x2f9ba, 1, 6504 }, { 0x2f9bb, 1, 4820 }, { 0x2f9bc, 1, 6505 }, { 0x2f9bd, 1, 6506 }, { 0x2f9be, 1, 6507 }, { 0x2f9bf, 1, 6508 }, { 0x2f9c0, 1, 6509 }, { 0x2f9c1, 1, 6510 },
    { 0x2f9c2, 1, 6511 }, { 0x2f9c3, 1, 6512 }, { 0x2f9c4, 1, 2918 }, { 0x2f9c5, 1, 6513 }, { 0x2f9c6, 1, 6514 }, { 0x2f9c7, 1, 6515 }, { 0x2f9c8, 1, 6516 }, { 0x2f9c9, 1, 6517 }, { 0x2f9ca, 1, 6518 }, { 0x2f9cb, 1, 6519 },
    { 0x2f9cc, 1, 6520 }, { 0x2f9cd, 1, 6521 }, { 0x2f9ce, 1, 6522 }, { 0x2f9cf, 1, 6523 }, { 0x2f9d0, 1, 4825 }, { 0x2f9d1, 1, 4826 }, { 0x2f9d2, 1, 2925 }, { 0x2f9d3, 1, 6524 }, { 0x2f9d4, 1, 6525 }, { 0x2f9d5, 1, 6526 },
    { 0x2f9d6, 1, 6527 }, { 0x2f9d7, 1, 6528 }, { 0x2f9d8, 1, 6529 }, { 0x2f9d9, 1, 6530 }, { 0x2f9da, 1, 6531 }, { 0x2f9db, 1, 6532 }, { 0x2f9dc, 1, 6533 }, { 0x2f9dd, 1, 6534 }, { 0x2f9de, 1, 6535 }, { 0x2f9df, 1, 4827 },
    { 0x2f9e0, 1, 6536 }, { 0x2f9e1, 1, 6537 }, { 0x2f9e2, 1, 6538 }, { 0x2f9e3, 1, 6539 }, { 0x2f9e4, 1, 6540 }, { 0x2f9e5, 1, 6541 }, { 0x2f9e6, 1, 6542 }, { 0x2f9e7, 1, 6543 }, { 0x2f9e8, 1, 6544 }, { 0x2f9e9, 1, 6545 },
    { 0x2f9ea, 1, 6546 }, { 0x2f9eb, 1, 6547 }, { 0x2f9ec, 1, 6548 }, { 0x2f9ed, 1, 6549 }, { 0x2f9ee, 1, 6550 }, { 0x2f9ef, 1, 6551 }, { 0x2f9f0, 1, 6552 }, { 0x2f9f1, 1, 6553 }, { 0x2f9f2, 1, 6554 }, { 0x2f9f3, 1, 6555 },
    { 0x2f9f4, 1, 6556 }, { 0x2f9f5, 1, 6557 }, { 0x2f9f6, 1, 6558 }, { 0x2f9f7, 1, 6559 }, { 0x2f9f8, 1, 6560 }, { 0x2f9f9, 1, 6561 }, { 0x2f9fa, 1, 6562 }, { 0x2f9fb, 1, 6563 }, { 0x2f9fc, 1, 6564 }, { 0x2f9fd, 1, 6565 },
    { 0x2f9fe, 1, 4833 }, { 0x2f9ff, 1, 4833 }, { 0x2fa00, 1, 6566 }, { 0x2fa01, 1, 6567 }, { 0x2fa02, 1, 6568 }, { 0x2fa03, 1, 6569 }, { 0x2fa04, 1, 6570 }, { 0x2fa05, 1, 6571 }, { 0x2fa06, 1, 6572 }, { 0x2fa07, 1, 6573 },
    { 0x2fa08, 1, 6574 }, { 0x2fa09, 1, 6575 }, { 0x2fa0a, 1, 4834 }, { 0x2fa0b, 1, 6576 }, { 0x2fa0c, 1, 6577 }, { 0x2fa0d, 1, 6578 }, { 0x2fa0e, 1, 6579 }, { 0x2fa0f, 1, 6580 }, { 0x2fa10, 1, 6581 }, { 0x2fa11, 1, 6582 },
    { 0x2fa12, 1, 6583 }, { 0x2fa13, 1, 6584 }, { 0x2fa14, 1, 6585 }, { 0x2fa15, 1, 2973 }, { 0x2fa16, 1, 6586 }, { 0x2fa17, 1, 2977 }, { 0x2fa18, 1, 6587 }, { 0x2fa19, 1, 6588 }, { 0x2fa1a, 1, 6589 }, { 0x2fa1b, 1, 6590 },
    { 0x2fa1c, 1, 2982 }, { 0x2fa1d, 1, 6591 }
};

static const Unicode decomp_expansion[] = {
    0x20 /* offset 0 */,
    0x20,
    0x308 /* offset 1 */,
    0x61 /* offset 3 */,
    0x20,
    0x304 /* offset 4 */,
    0x32 /* offset 6 */,
    0x33 /* offset 7 */,
    0x20,
    0x301 /* offset 8 */,
    0x3bc /* offset 10 */,
    0x20,
    0x327 /* offset 11 */,
    0x31 /* offset 13 */,
    0x6f /* offset 14 */,
    0x31,
    0x2044,
    0x34 /* offset 15 */,
    0x31,
    0x2044,
    0x32 /* offset 18 */,
    0x33,
    0x2044,
    0x34 /* offset 21 */,
    0x41,
    0x300 /* offset 24 */,
    0x41,
    0x301 /* offset 26 */,
    0x41,
    0x302 /* offset 28 */,
    0x41,
    0x303 /* offset 30 */,
    0x41,
    0x308 /* offset 32 */,
    0x41,
    0x30a /* offset 34 */,
    0x43,
    0x327 /* offset 36 */,
    0x45,
    0x300 /* offset 38 */,
    0x45,
    0x301 /* offset 40 */,
    0x45,
    0x302 /* offset 42 */,
    0x45,
    0x308 /* offset 44 */,
    0x49,
    0x300 /* offset 46 */,
    0x49,
    0x301 /* offset 48 */,
    0x49,
    0x302 /* offset 50 */,
    0x49,
    0x308 /* offset 52 */,
    0x4e,
    0x303 /* offset 54 */,
    0x4f,
    0x300 /* offset 56 */,
    0x4f,
    0x301 /* offset 58 */,
    0x4f,
    0x302 /* offset 60 */,
    0x4f,
    0x303 /* offset 62 */,
    0x4f,
    0x308 /* offset 64 */,
    0x55,
    0x300 /* offset 66 */,
    0x55,
    0x301 /* offset 68 */,
    0x55,
    0x302 /* offset 70 */,
    0x55,
    0x308 /* offset 72 */,
    0x59,
    0x301 /* offset 74 */,
    0x61,
    0x300 /* offset 76 */,
    0x61,
    0x301 /* offset 78 */,
    0x61,
    0x302 /* offset 80 */,
    0x61,
    0x303 /* offset 82 */,
    0x61,
    0x308 /* offset 84 */,
    0x61,
    0x30a /* offset 86 */,
    0x63,
    0x327 /* offset 88 */,
    0x65,
    0x300 /* offset 90 */,
    0x65,
    0x301 /* offset 92 */,
    0x65,
    0x302 /* offset 94 */,
    0x65,
    0x308 /* offset 96 */,
    0x69,
    0x300 /* offset 98 */,
    0x69,
    0x301 /* offset 100 */,
    0x69,
    0x302 /* offset 102 */,
    0x69,
    0x308 /* offset 104 */,
    0x6e,
    0x303 /* offset 106 */,
    0x6f,
    0x300 /* offset 108 */,
    0x6f,
    0x301 /* offset 110 */,
    0x6f,
    0x302 /* offset 112 */,
    0x6f,
    0x303 /* offset 114 */,
    0x6f,
    0x308 /* offset 116 */,
    0x75,
    0x300 /* offset 118 */,
    0x75,
    0x301 /* offset 120 */,
    0x75,
    0x302 /* offset 122 */,
    0x75,
    0x308 /* offset 124 */,
    0x79,
    0x301 /* offset 126 */,
    0x79,
    0x308 /* offset 128 */,
    0x41,
    0x304 /* offset 130 */,
    0x61,
    0x304 /* offset 132 */,
    0x41,
    0x306 /* offset 134 */,
    0x61,
    0x306 /* offset 136 */,
    0x41,
    0x328 /* offset 138 */,
    0x61,
    0x328 /* offset 140 */,
    0x43,
    0x301 /* offset 142 */,
    0x63,
    0x301 /* offset 144 */,
    0x43,
    0x302 /* offset 146 */,
    0x63,
    0x302 /* offset 148 */,
    0x43,
    0x307 /* offset 150 */,
    0x63,
    0x307 /* offset 152 */,
    0x43,
    0x30c /* offset 154 */,
    0x63,
    0x30c /* offset 156 */,
    0x44,
    0x30c /* offset 158 */,
    0x64,
    0x30c /* offset 160 */,
    0x45,
    0x304 /* offset 162 */,
    0x65,
    0x304 /* offset 164 */,
    0x45,
    0x306 /* offset 166 */,
    0x65,
    0x306 /* offset 168 */,
    0x45,
    0x307 /* offset 170 */,
    0x65,
    0x307 /* offset 172 */,
    0x45,
    0x328 /* offset 174 */,
    0x65,
    0x328 /* offset 176 */,
    0x45,
    0x30c /* offset 178 */,
    0x65,
    0x30c /* offset 180 */,
    0x47,
    0x302 /* offset 182 */,
    0x67,
    0x302 /* offset 184 */,
    0x47,
    0x306 /* offset 186 */,
    0x67,
    0x306 /* offset 188 */,
    0x47,
    0x307 /* offset 190 */,
    0x67,
    0x307 /* offset 192 */,
    0x47,
    0x327 /* offset 194 */,
    0x67,
    0x327 /* offset 196 */,
    0x48,
    0x302 /* offset 198 */,
    0x68,
    0x302 /* offset 200 */,
    0x49,
    0x303 /* offset 202 */,
    0x69,
    0x303 /* offset 204 */,
    0x49,
    0x304 /* offset 206 */,
    0x69,
    0x304 /* offset 208 */,
    0x49,
    0x306 /* offset 210 */,
    0x69,
    0x306 /* offset 212 */,
    0x49,
    0x328 /* offset 214 */,
    0x69,
    0x328 /* offset 216 */,
    0x49,
    0x307 /* offset 218 */,
    0x49,
    0x4a /* offset 220 */,
    0x69,
    0x6a /* offset 222 */,
    0x4a,
    0x302 /* offset 224 */,
    0x6a,
    0x302 /* offset 226 */,
    0x4b,
    0x327 /* offset 228 */,
    0x6b,
    0x327 /* offset 230 */,
    0x4c,
    0x301 /* offset 232 */,
    0x6c,
    0x301 /* offset 234 */,
    0x4c,
    0x327 /* offset 236 */,
    0x6c,
    0x327 /* offset 238 */,
    0x4c,
    0x30c /* offset 240 */,
    0x6c,
    0x30c /* offset 242 */,
    0x4c,
    0xb7 /* offset 244 */,
    0x6c,
    0xb7 /* offset 246 */,
    0x4e,
    0x301 /* offset 248 */,
    0x6e,
    0x301 /* offset 250 */,
    0x4e,
    0x327 /* offset 252 */,
    0x6e,
    0x327 /* offset 254 */,
    0x4e,
    0x30c /* offset 256 */,
    0x6e,
    0x30c /* offset 258 */,
    0x2bc,
    0x6e /* offset 260 */,
    0x4f,
    0x304 /* offset 262 */,
    0x6f,
    0x304 /* offset 264 */,
    0x4f,
    0x306 /* offset 266 */,
    0x6f,
    0x306 /* offset 268 */,
    0x4f,
    0x30b /* offset 270 */,
    0x6f,
    0x30b /* offset 272 */,
    0x52,
    0x301 /* offset 274 */,
    0x72,
    0x301 /* offset 276 */,
    0x52,
    0x327 /* offset 278 */,
    0x72,
    0x327 /* offset 280 */,
    0x52,
    0x30c /* offset 282 */,
    0x72,
    0x30c /* offset 284 */,
    0x53,
    0x301 /* offset 286 */,
    0x73,
    0x301 /* offset 288 */,
    0x53,
    0x302 /* offset 290 */,
    0x73,
    0x302 /* offset 292 */,
    0x53,
    0x327 /* offset 294 */,
    0x73,
    0x327 /* offset 296 */,
    0x53,
    0x30c /* offset 298 */,
    0x73,
    0x30c /* offset 300 */,
    0x54,
    0x327 /* offset 302 */,
    0x74,
    0x327 /* offset 304 */,
    0x54,
    0x30c /* offset 306 */,
    0x74,
    0x30c /* offset 308 */,
    0x55,
    0x303 /* offset 310 */,
    0x75,
    0x303 /* offset 312 */,
    0x55,
    0x304 /* offset 314 */,
    0x75,
    0x304 /* offset 316 */,
    0x55,
    0x306 /* offset 318 */,
    0x75,
    0x306 /* offset 320 */,
    0x55,
    0x30a /* offset 322 */,
    0x75,
    0x30a /* offset 324 */,
    0x55,
    0x30b /* offset 326 */,
    0x75,
    0x30b /* offset 328 */,
    0x55,
    0x328 /* offset 330 */,
    0x75,
    0x328 /* offset 332 */,
    0x57,
    0x302 /* offset 334 */,
    0x77,
    0x302 /* offset 336 */,
    0x59,
    0x302 /* offset 338 */,
    0x79,
    0x302 /* offset 340 */,
    0x59,
    0x308 /* offset 342 */,
    0x5a,
    0x301 /* offset 344 */,
    0x7a,
    0x301 /* offset 346 */,
    0x5a,
    0x307 /* offset 348 */,
    0x7a,
    0x307 /* offset 350 */,
    0x5a,
    0x30c /* offset 352 */,
    0x7a,
    0x30c /* offset 354 */,
    0x73 /* offset 356 */,
    0x4f,
    0x31b /* offset 357 */,
    0x6f,
    0x31b /* offset 359 */,
    0x55,
    0x31b /* offset 361 */,
    0x75,
    0x31b /* offset 363 */,
    0x44,
    0x5a,
    0x30c /* offset 365 */,
    0x44,
    0x7a,
    0x30c /* offset 368 */,
    0x64,
    0x7a,
    0x30c /* offset 371 */,
    0x4c,
    0x4a /* offset 374 */,
    0x4c,
    0x6a /* offset 376 */,
    0x6c,
    0x6a /* offset 378 */,
    0x4e,
    0x4a /* offset 380 */,
    0x4e,
    0x6a /* offset 382 */,
    0x6e,
    0x6a /* offset 384 */,
    0x41,
    0x30c /* offset 386 */,
    0x61,
    0x30c /* offset 388 */,
    0x49,
    0x30c /* offset 390 */,
    0x69,
    0x30c /* offset 392 */,
    0x4f,
    0x30c /* offset 394 */,
    0x6f,
    0x30c /* offset 396 */,
    0x55,
    0x30c /* offset 398 */,
    0x75,
    0x30c /* offset 400 */,
    0x55,
    0x308,
    0x304 /* offset 402 */,
    0x75,
    0x308,
    0x304 /* offset 405 */,
    0x55,
    0x308,
    0x301 /* offset 408 */,
    0x75,
    0x308,
    0x301 /* offset 411 */,
    0x55,
    0x308,
    0x30c /* offset 414 */,
    0x75,
    0x308,
    0x30c /* offset 417 */,
    0x55,
    0x308,
    0x300 /* offset 420 */,
    0x75,
    0x308,
    0x300 /* offset 423 */,
    0x41,
    0x308,
    0x304 /* offset 426 */,
    0x61,
    0x308,
    0x304 /* offset 429 */,
    0x41,
    0x307,
    0x304 /* offset 432 */,
    0x61,
    0x307,
    0x304 /* offset 435 */,
    0xc6,
    0x304 /* offset 438 */,
    0xe6,
    0x304 /* offset 440 */,
    0x47,
    0x30c /* offset 442 */,
    0x67,
    0x30c /* offset 444 */,
    0x4b,
    0x30c /* offset 446 */,
    0x6b,
    0x30c /* offset 448 */,
    0x4f,
    0x328 /* offset 450 */,
    0x6f,
    0x328 /* offset 452 */,
    0x4f,
    0x328,
    0x304 /* offset 454 */,
    0x6f,
    0x328,
    0x304 /* offset 457 */,
    0x1b7,
    0x30c /* offset 460 */,
    0x292,
    0x30c /* offset 462 */,
    0x6a,
    0x30c /* offset 464 */,
    0x44,
    0x5a /* offset 466 */,
    0x44,
    0x7a /* offset 468 */,
    0x64,
    0x7a /* offset 470 */,
    0x47,
    0x301 /* offset 472 */,
    0x67,
    0x301 /* offset 474 */,
    0x4e,
    0x300 /* offset 476 */,
    0x6e,
    0x300 /* offset 478 */,
    0x41,
    0x30a,
    0x301 /* offset 480 */,
    0x61,
    0x30a,
    0x301 /* offset 483 */,
    0xc6,
    0x301 /* offset 486 */,
    0xe6,
    0x301 /* offset 488 */,
    0xd8,
    0x301 /* offset 490 */,
    0xf8,
    0x301 /* offset 492 */,
    0x41,
    0x30f /* offset 494 */,
    0x61,
    0x30f /* offset 496 */,
    0x41,
    0x311 /* offset 498 */,
    0x61,
    0x311 /* offset 500 */,
    0x45,
    0x30f /* offset 502 */,
    0x65,
    0x30f /* offset 504 */,
    0x45,
    0x311 /* offset 506 */,
    0x65,
    0x311 /* offset 508 */,
    0x49,
    0x30f /* offset 510 */,
    0x69,
    0x30f /* offset 512 */,
    0x49,
    0x311 /* offset 514 */,
    0x69,
    0x311 /* offset 516 */,
    0x4f,
    0x30f /* offset 518 */,
    0x6f,
    0x30f /* offset 520 */,
    0x4f,
    0x311 /* offset 522 */,
    0x6f,
    0x311 /* offset 524 */,
    0x52,
    0x30f /* offset 526 */,
    0x72,
    0x30f /* offset 528 */,
    0x52,
    0x311 /* offset 530 */,
    0x72,
    0x311 /* offset 532 */,
    0x55,
    0x30f /* offset 534 */,
    0x75,
    0x30f /* offset 536 */,
    0x55,
    0x311 /* offset 538 */,
    0x75,
    0x311 /* offset 540 */,
    0x53,
    0x326 /* offset 542 */,
    0x73,
    0x326 /* offset 544 */,
    0x54,
    0x326 /* offset 546 */,
    0x74,
    0x326 /* offset 548 */,
    0x48,
    0x30c /* offset 550 */,
    0x68,
    0x30c /* offset 552 */,
    0x41,
    0x307 /* offset 554 */,
    0x61,
    0x307 /* offset 556 */,
    0x45,
    0x327 /* offset 558 */,
    0x65,
    0x327 /* offset 560 */,
    0x4f,
    0x308,
    0x304 /* offset 562 */,
    0x6f,
    0x308,
    0x304 /* offset 565 */,
    0x4f,
    0x303,
    0x304 /* offset 568 */,
    0x6f,
    0x303,
    0x304 /* offset 571 */,
    0x4f,
    0x307 /* offset 574 */,
    0x6f,
    0x307 /* offset 576 */,
    0x4f,
    0x307,
    0x304 /* offset 578 */,
    0x6f,
    0x307,
    0x304 /* offset 581 */,
    0x59,
    0x304 /* offset 584 */,
    0x79,
    0x304 /* offset 586 */,
    0x68 /* offset 588 */,
    0x266 /* offset 589 */,
    0x6a /* offset 590 */,
    0x72 /* offset 591 */,
    0x279 /* offset 592 */,
    0x27b /* offset 593 */,
    0x281 /* offset 594 */,
    0x77 /* offset 595 */,
    0x79 /* offset 596 */,
    0x20,
    0x306 /* offset 597 */,
    0x20,
    0x307 /* offset 599 */,
    0x20,
    0x30a /* offset 601 */,
    0x20,
    0x328 /* offset 603 */,
    0x20,
    0x303 /* offset 605 */,
    0x20,
    0x30b /* offset 607 */,
    0x263 /* offset 609 */,
    0x6c /* offset 610 */,
    0x78 /* offset 611 */,
    0x295 /* offset 612 */,
    0x300 /* offset 613 */,
    0x301 /* offset 614 */,
    0x313 /* offset 615 */,
    0x308,
    0x301 /* offset 616 */,
    0x2b9 /* offset 618 */,
    0x20,
    0x345 /* offset 619 */,
    0x3b /* offset 621 */,
    0x20,
    0x308,
    0x301 /* offset 622 */,
    0x391,
    0x301 /* offset 625 */,
    0xb7 /* offset 627 */,
    0x395,
    0x301 /* offset 628 */,
    0x397,
    0x301 /* offset 630 */,
    0x399,
    0x301 /* offset 632 */,
    0x39f,
    0x301 /* offset 634 */,
    0x3a5,
    0x301 /* offset 636 */,
    0x3a9,
    0x301 /* offset 638 */,
    0x3b9,
    0x308,
    0x301 /* offset 640 */,
    0x399,
    0x308 /* offset 643 */,
    0x3a5,
    0x308 /* offset 645 */,
    0x3b1,
    0x301 /* offset 647 */,
    0x3b5,
    0x301 /* offset 649 */,
    0x3b7,
    0x301 /* offset 651 */,
    0x3b9,
    0x301 /* offset 653 */,
    0x3c5,
    0x308,
    0x301 /* offset 655 */,
    0x3b9,
    0x308 /* offset 658 */,
    0x3c5,
    0x308 /* offset 660 */,
    0x3bf,
    0x301 /* offset 662 */,
    0x3c5,
    0x301 /* offset 664 */,
    0x3c9,
    0x301 /* offset 666 */,
    0x3b2 /* offset 668 */,
    0x3b8 /* offset 669 */,
    0x3a5 /* offset 670 */,
    0x3c6 /* offset 671 */,
    0x3c0 /* offset 672 */,
    0x3ba /* offset 673 */,
    0x3c1 /* offset 674 */,
    0x3c2 /* offset 675 */,
    0x398 /* offset 676 */,
    0x3b5 /* offset 677 */,
    0x3a3 /* offset 678 */,
    0x415,
    0x300 /* offset 679 */,
    0x415,
    0x308 /* offset 681 */,
    0x413,
    0x301 /* offset 683 */,
    0x406,
    0x308 /* offset 685 */,
    0x41a,
    0x301 /* offset 687 */,
    0x418,
    0x300 /* offset 689 */,
    0x423,
    0x306 /* offset 691 */,
    0x418,
    0x306 /* offset 693 */,
    0x438,
    0x306 /* offset 695 */,
    0x435,
    0x300 /* offset 697 */,
    0x435,
    0x308 /* offset 699 */,
    0x433,
    0x301 /* offset 701 */,
    0x456,
    0x308 /* offset 703 */,
    0x43a,
    0x301 /* offset 705 */,
    0x438,
    0x300 /* offset 707 */,
    0x443,
    0x306 /* offset 709 */,
    0x474,
    0x30f /* offset 711 */,
    0x475,
    0x30f /* offset 713 */,
    0x416,
    0x306 /* offset 715 */,
    0x436,
    0x306 /* offset 717 */,
    0x410,
    0x306 /* offset 719 */,
    0x430,
    0x306 /* offset 721 */,
    0x410,
    0x308 /* offset 723 */,
    0x430,
    0x308 /* offset 725 */,
    0x415,
    0x306 /* offset 727 */,
    0x435,
    0x306 /* offset 729 */,
    0x4d8,
    0x308 /* offset 731 */,
    0x4d9,
    0x308 /* offset 733 */,
    0x416,
    0x308 /* offset 735 */,
    0x436,
    0x308 /* offset 737 */,
    0x417,
    0x308 /* offset 739 */,
    0x437,
    0x308 /* offset 741 */,
    0x418,
    0x304 /* offset 743 */,
    0x438,
    0x304 /* offset 745 */,
    0x418,
    0x308 /* offset 747 */,
    0x438,
    0x308 /* offset 749 */,
    0x41e,
    0x308 /* offset 751 */,
    0x43e,
    0x308 /* offset 753 */,
    0x4e8,
    0x308 /* offset 755 */,
    0x4e9,
    0x308 /* offset 757 */,
    0x42d,
    0x308 /* offset 759 */,
    0x44d,
    0x308 /* offset 761 */,
    0x423,
    0x304 /* offset 763 */,
    0x443,
    0x304 /* offset 765 */,
    0x423,
    0x308 /* offset 767 */,
    0x443,
    0x308 /* offset 769 */,
    0x423,
    0x30b /* offset 771 */,
    0x443,
    0x30b /* offset 773 */,
    0x427,
    0x308 /* offset 775 */,
    0x447,
    0x308 /* offset 777 */,
    0x42b,
    0x308 /* offset 779 */,
    0x44b,
    0x308 /* offset 781 */,
    0x565,
    0x582 /* offset 783 */,
    0x627,
    0x653 /* offset 785 */,
    0x627,
    0x654 /* offset 787 */,
    0x648,
    0x654 /* offset 789 */,
    0x627,
    0x655 /* offset 791 */,
    0x64a,
    0x654 /* offset 793 */,
    0x627,
    0x674 /* offset 795 */,
    0x648,
    0x674 /* offset 797 */,
    0x6c7,
    0x674 /* offset 799 */,
    0x64a,
    0x674 /* offset 801 */,
    0x6d5,
    0x654 /* offset 803 */,
    0x6c1,
    0x654 /* offset 805 */,
    0x6d2,
    0x654 /* offset 807 */,
    0x928,
    0x93c /* offset 809 */,
    0x930,
    0x93c /* offset 811 */,
    0x933,
    0x93c /* offset 813 */,
    0x915,
    0x93c /* offset 815 */,
    0x916,
    0x93c /* offset 817 */,
    0x917,
    0x93c /* offset 819 */,
    0x91c,
    0x93c /* offset 821 */,
    0x921,
    0x93c /* offset 823 */,
    0x922,
    0x93c /* offset 825 */,
    0x92b,
    0x93c /* offset 827 */,
    0x92f,
    0x93c /* offset 829 */,
    0x9c7,
    0x9be /* offset 831 */,
    0x9c7,
    0x9d7 /* offset 833 */,
    0x9a1,
    0x9bc /* offset 835 */,
    0x9a2,
    0x9bc /* offset 837 */,
    0x9af,
    0x9bc /* offset 839 */,
    0xa32,
    0xa3c /* offset 841 */,
    0xa38,
    0xa3c /* offset 843 */,
    0xa16,
    0xa3c /* offset 845 */,
    0xa17,
    0xa3c /* offset 847 */,
    0xa1c,
    0xa3c /* offset 849 */,
    0xa2b,
    0xa3c /* offset 851 */,
    0xb47,
    0xb56 /* offset 853 */,
    0xb47,
    0xb3e /* offset 855 */,
    0xb47,
    0xb57 /* offset 857 */,
    0xb21,
    0xb3c /* offset 859 */,
    0xb22,
    0xb3c /* offset 861 */,
    0xb92,
    0xbd7 /* offset 863 */,
    0xbc6,
    0xbbe /* offset 865 */,
    0xbc7,
    0xbbe /* offset 867 */,
    0xbc6,
    0xbd7 /* offset 869 */,
    0xc46,
    0xc56 /* offset 871 */,
    0xcbf,
    0xcd5 /* offset 873 */,
    0xcc6,
    0xcd5 /* offset 875 */,
    0xcc6,
    0xcd6 /* offset 877 */,
    0xcc6,
    0xcc2 /* offset 879 */,
    0xcc6,
    0xcc2,
    0xcd5 /* offset 881 */,
    0xd46,
    0xd3e /* offset 884 */,
    0xd47,
    0xd3e /* offset 886 */,
    0xd46,
    0xd57 /* offset 888 */,
    0xdd9,
    0xdca /* offset 890 */,
    0xdd9,
    0xdcf /* offset 892 */,
    0xdd9,
    0xdcf,
    0xdca /* offset 894 */,
    0xdd9,
    0xddf /* offset 897 */,
    0xe4d,
    0xe32 /* offset 899 */,
    0xecd,
    0xeb2 /* offset 901 */,
    0xeab,
    0xe99 /* offset 903 */,
    0xeab,
    0xea1 /* offset 905 */,
    0xf0b /* offset 907 */,
    0xf42,
    0xfb7 /* offset 908 */,
    0xf4c,
    0xfb7 /* offset 910 */,
    0xf51,
    0xfb7 /* offset 912 */,
    0xf56,
    0xfb7 /* offset 914 */,
    0xf5b,
    0xfb7 /* offset 916 */,
    0xf40,
    0xfb5 /* offset 918 */,
    0xf71,
    0xf72 /* offset 920 */,
    0xf71,
    0xf74 /* offset 922 */,
    0xfb2,
    0xf80 /* offset 924 */,
    0xfb2,
    0xf71,
    0xf80 /* offset 926 */,
    0xfb3,
    0xf80 /* offset 929 */,
    0xfb3,
    0xf71,
    0xf80 /* offset 931 */,
    0xf71,
    0xf80 /* offset 934 */,
    0xf92,
    0xfb7 /* offset 936 */,
    0xf9c,
    0xfb7 /* offset 938 */,
    0xfa1,
    0xfb7 /* offset 940 */,
    0xfa6,
    0xfb7 /* offset 942 */,
    0xfab,
    0xfb7 /* offset 944 */,
    0xf90,
    0xfb5 /* offset 946 */,
    0x1025,
    0x102e /* offset 948 */,
    0x10dc /* offset 950 */,
    0x1b05,
    0x1b35 /* offset 951 */,
    0x1b07,
    0x1b35 /* offset 953 */,
    0x1b09,
    0x1b35 /* offset 955 */,
    0x1b0b,
    0x1b35 /* offset 957 */,
    0x1b0d,
    0x1b35 /* offset 959 */,
    0x1b11,
    0x1b35 /* offset 961 */,
    0x1b3a,
    0x1b35 /* offset 963 */,
    0x1b3c,
    0x1b35 /* offset 965 */,
    0x1b3e,
    0x1b35 /* offset 967 */,
    0x1b3f,
    0x1b35 /* offset 969 */,
    0x1b42,
    0x1b35 /* offset 971 */,
    0x41 /* offset 973 */,
    0xc6 /* offset 974 */,
    0x42 /* offset 975 */,
    0x44 /* offset 976 */,
    0x45 /* offset 977 */,
    0x18e /* offset 978 */,
    0x47 /* offset 979 */,
    0x48 /* offset 980 */,
    0x49 /* offset 981 */,
    0x4a /* offset 982 */,
    0x4b /* offset 983 */,
    0x4c /* offset 984 */,
    0x4d /* offset 985 */,
    0x4e /* offset 986 */,
    0x4f /* offset 987 */,
    0x222 /* offset 988 */,
    0x50 /* offset 989 */,
    0x52 /* offset 990 */,
    0x54 /* offset 991 */,
    0x55 /* offset 992 */,
    0x57 /* offset 993 */,
    0x250 /* offset 994 */,
    0x251 /* offset 995 */,
    0x1d02 /* offset 996 */,
    0x62 /* offset 997 */,
    0x64 /* offset 998 */,
    0x65 /* offset 999 */,
    0x259 /* offset 1000 */,
    0x25b /* offset 1001 */,
    0x25c /* offset 1002 */,
    0x67 /* offset 1003 */,
    0x6b /* offset 1004 */,
    0x6d /* offset 1005 */,
    0x14b /* offset 1006 */,
    0x254 /* offset 1007 */,
    0x1d16 /* offset 1008 */,
    0x1d17 /* offset 1009 */,
    0x70 /* offset 1010 */,
    0x74 /* offset 1011 */,
    0x75 /* offset 1012 */,
    0x1d1d /* offset 1013 */,
    0x26f /* offset 1014 */,
    0x76 /* offset 1015 */,
    0x1d25 /* offset 1016 */,
    0x3b3 /* offset 1017 */,
    0x3b4 /* offset 1018 */,
    0x3c7 /* offset 1019 */,
    0x69 /* offset 1020 */,
    0x43d /* offset 1021 */,
    0x252 /* offset 1022 */,
    0x63 /* offset 1023 */,
    0x255 /* offset 1024 */,
    0xf0 /* offset 1025 */,
    0x66 /* offset 1026 */,
    0x25f /* offset 1027 */,
    0x261 /* offset 1028 */,
    0x265 /* offset 1029 */,
    0x268 /* offset 1030 */,
    0x269 /* offset 1031 */,
    0x26a /* offset 1032 */,
    0x1d7b /* offset 1033 */,
    0x29d /* offset 1034 */,
    0x26d /* offset 1035 */,
    0x1d85 /* offset 1036 */,
    0x29f /* offset 1037 */,
    0x271 /* offset 1038 */,
    0x270 /* offset 1039 */,
    0x272 /* offset 1040 */,
    0x273 /* offset 1041 */,
    0x274 /* offset 1042 */,
    0x275 /* offset 1043 */,
    0x278 /* offset 1044 */,
    0x282 /* offset 1045 */,
    0x283 /* offset 1046 */,
    0x1ab /* offset 1047 */,
    0x289 /* offset 1048 */,
    0x28a /* offset 1049 */,
    0x1d1c /* offset 1050 */,
    0x28b /* offset 1051 */,
    0x28c /* offset 1052 */,
    0x7a /* offset 1053 */,
    0x290 /* offset 1054 */,
    0x291 /* offset 1055 */,
    0x292 /* offset 1056 */,
    0x41,
    0x325 /* offset 1057 */,
    0x61,
    0x325 /* offset 1059 */,
    0x42,
    0x307 /* offset 1061 */,
    0x62,
    0x307 /* offset 1063 */,
    0x42,
    0x323 /* offset 1065 */,
    0x62,
    0x323 /* offset 1067 */,
    0x42,
    0x331 /* offset 1069 */,
    0x62,
    0x331 /* offset 1071 */,
    0x43,
    0x327,
    0x301 /* offset 1073 */,
    0x63,
    0x327,
    0x301 /* offset 1076 */,
    0x44,
    0x307 /* offset 1079 */,
    0x64,
    0x307 /* offset 1081 */,
    0x44,
    0x323 /* offset 1083 */,
    0x64,
    0x323 /* offset 1085 */,
    0x44,
    0x331 /* offset 1087 */,
    0x64,
    0x331 /* offset 1089 */,
    0x44,
    0x327 /* offset 1091 */,
    0x64,
    0x327 /* offset 1093 */,
    0x44,
    0x32d /* offset 1095 */,
    0x64,
    0x32d /* offset 1097 */,
    0x45,
    0x304,
    0x300 /* offset 1099 */,
    0x65,
    0x304,
    0x300 /* offset 1102 */,
    0x45,
    0x304,
    0x301 /* offset 1105 */,
    0x65,
    0x304,
    0x301 /* offset 1108 */,
    0x45,
    0x32d /* offset 1111 */,
    0x65,
    0x32d /* offset 1113 */,
    0x45,
    0x330 /* offset 1115 */,
    0x65,
    0x330 /* offset 1117 */,
    0x45,
    0x327,
    0x306 /* offset 1119 */,
    0x65,
    0x327,
    0x306 /* offset 1122 */,
    0x46,
    0x307 /* offset 1125 */,
    0x66,
    0x307 /* offset 1127 */,
    0x47,
    0x304 /* offset 1129 */,
    0x67,
    0x304 /* offset 1131 */,
    0x48,
    0x307 /* offset 1133 */,
    0x68,
    0x307 /* offset 1135 */,
    0x48,
    0x323 /* offset 1137 */,
    0x68,
    0x323 /* offset 1139 */,
    0x48,
    0x308 /* offset 1141 */,
    0x68,
    0x308 /* offset 1143 */,
    0x48,
    0x327 /* offset 1145 */,
    0x68,
    0x327 /* offset 1147 */,
    0x48,
    0x32e /* offset 1149 */,
    0x68,
    0x32e /* offset 1151 */,
    0x49,
    0x330 /* offset 1153 */,
    0x69,
    0x330 /* offset 1155 */,
    0x49,
    0x308,
    0x301 /* offset 1157 */,
    0x69,
    0x308,
    0x301 /* offset 1160 */,
    0x4b,
    0x301 /* offset 1163 */,
    0x6b,
    0x301 /* offset 1165 */,
    0x4b,
    0x323 /* offset 1167 */,
    0x6b,
    0x323 /* offset 1169 */,
    0x4b,
    0x331 /* offset 1171 */,
    0x6b,
    0x331 /* offset 1173 */,
    0x4c,
    0x323 /* offset 1175 */,
    0x6c,
    0x323 /* offset 1177 */,
    0x4c,
    0x323,
    0x304 /* offset 1179 */,
    0x6c,
    0x323,
    0x304 /* offset 1182 */,
    0x4c,
    0x331 /* offset 1185 */,
    0x6c,
    0x331 /* offset 1187 */,
    0x4c,
    0x32d /* offset 1189 */,
    0x6c,
    0x32d /* offset 1191 */,
    0x4d,
    0x301 /* offset 1193 */,
    0x6d,
    0x301 /* offset 1195 */,
    0x4d,
    0x307 /* offset 1197 */,
    0x6d,
    0x307 /* offset 1199 */,
    0x4d,
    0x323 /* offset 1201 */,
    0x6d,
    0x323 /* offset 1203 */,
    0x4e,
    0x307 /* offset 1205 */,
    0x6e,
    0x307 /* offset 1207 */,
    0x4e,
    0x323 /* offset 1209 */,
    0x6e,
    0x323 /* offset 1211 */,
    0x4e,
    0x331 /* offset 1213 */,
    0x6e,
    0x331 /* offset 1215 */,
    0x4e,
    0x32d /* offset 1217 */,
    0x6e,
    0x32d /* offset 1219 */,
    0x4f,
    0x303,
    0x301 /* offset 1221 */,
    0x6f,
    0x303,
    0x301 /* offset 1224 */,
    0x4f,
    0x303,
    0x308 /* offset 1227 */,
    0x6f,
    0x303,
    0x308 /* offset 1230 */,
    0x4f,
    0x304,
    0x300 /* offset 1233 */,
    0x6f,
    0x304,
    0x300 /* offset 1236 */,
    0x4f,
    0x304,
    0x301 /* offset 1239 */,
    0x6f,
    0x304,
    0x301 /* offset 1242 */,
    0x50,
    0x301 /* offset 1245 */,
    0x70,
    0x301 /* offset 1247 */,
    0x50,
    0x307 /* offset 1249 */,
    0x70,
    0x307 /* offset 1251 */,
    0x52,
    0x307 /* offset 1253 */,
    0x72,
    0x307 /* offset 1255 */,
    0x52,
    0x323 /* offset 1257 */,
    0x72,
    0x323 /* offset 1259 */,
    0x52,
    0x323,
    0x304 /* offset 1261 */,
    0x72,
    0x323,
    0x304 /* offset 1264 */,
    0x52,
    0x331 /* offset 1267 */,
    0x72,
    0x331 /* offset 1269 */,
    0x53,
    0x307 /* offset 1271 */,
    0x73,
    0x307 /* offset 1273 */,
    0x53,
    0x323 /* offset 1275 */,
    0x73,
    0x323 /* offset 1277 */,
    0x53,
    0x301,
    0x307 /* offset 1279 */,
    0x73,
    0x301,
    0x307 /* offset 1282 */,
    0x53,
    0x30c,
    0x307 /* offset 1285 */,
    0x73,
    0x30c,
    0x307 /* offset 1288 */,
    0x53,
    0x323,
    0x307 /* offset 1291 */,
    0x73,
    0x323,
    0x307 /* offset 1294 */,
    0x54,
    0x307 /* offset 1297 */,
    0x74,
    0x307 /* offset 1299 */,
    0x54,
    0x323 /* offset 1301 */,
    0x74,
    0x323 /* offset 1303 */,
    0x54,
    0x331 /* offset 1305 */,
    0x74,
    0x331 /* offset 1307 */,
    0x54,
    0x32d /* offset 1309 */,
    0x74,
    0x32d /* offset 1311 */,
    0x55,
    0x324 /* offset 1313 */,
    0x75,
    0x324 /* offset 1315 */,
    0x55,
    0x330 /* offset 1317 */,
    0x75,
    0x330 /* offset 1319 */,
    0x55,
    0x32d /* offset 1321 */,
    0x75,
    0x32d /* offset 1323 */,
    0x55,
    0x303,
    0x301 /* offset 1325 */,
    0x75,
    0x303,
    0x301 /* offset 1328 */,
    0x55,
    0x304,
    0x308 /* offset 1331 */,
    0x75,
    0x304,
    0x308 /* offset 1334 */,
    0x56,
    0x303 /* offset 1337 */,
    0x76,
    0x303 /* offset 1339 */,
    0x56,
    0x323 /* offset 1341 */,
    0x76,
    0x323 /* offset 1343 */,
    0x57,
    0x300 /* offset 1345 */,
    0x77,
    0x300 /* offset 1347 */,
    0x57,
    0x301 /* offset 1349 */,
    0x77,
    0x301 /* offset 1351 */,
    0x57,
    0x308 /* offset 1353 */,
    0x77,
    0x308 /* offset 1355 */,
    0x57,
    0x307 /* offset 1357 */,
    0x77,
    0x307 /* offset 1359 */,
    0x57,
    0x323 /* offset 1361 */,
    0x77,
    0x323 /* offset 1363 */,
    0x58,
    0x307 /* offset 1365 */,
    0x78,
    0x307 /* offset 1367 */,
    0x58,
    0x308 /* offset 1369 */,
    0x78,
    0x308 /* offset 1371 */,
    0x59,
    0x307 /* offset 1373 */,
    0x79,
    0x307 /* offset 1375 */,
    0x5a,
    0x302 /* offset 1377 */,
    0x7a,
    0x302 /* offset 1379 */,
    0x5a,
    0x323 /* offset 1381 */,
    0x7a,
    0x323 /* offset 1383 */,
    0x5a,
    0x331 /* offset 1385 */,
    0x7a,
    0x331 /* offset 1387 */,
    0x68,
    0x331 /* offset 1389 */,
    0x74,
    0x308 /* offset 1391 */,
    0x77,
    0x30a /* offset 1393 */,
    0x79,
    0x30a /* offset 1395 */,
    0x61,
    0x2be /* offset 1397 */,
    0x41,
    0x323 /* offset 1399 */,
    0x61,
    0x323 /* offset 1401 */,
    0x41,
    0x309 /* offset 1403 */,
    0x61,
    0x309 /* offset 1405 */,
    0x41,
    0x302,
    0x301 /* offset 1407 */,
    0x61,
    0x302,
    0x301 /* offset 1410 */,
    0x41,
    0x302,
    0x300 /* offset 1413 */,
    0x61,
    0x302,
    0x300 /* offset 1416 */,
    0x41,
    0x302,
    0x309 /* offset 1419 */,
    0x61,
    0x302,
    0x309 /* offset 1422 */,
    0x41,
    0x302,
    0x303 /* offset 1425 */,
    0x61,
    0x302,
    0x303 /* offset 1428 */,
    0x41,
    0x323,
    0x302 /* offset 1431 */,
    0x61,
    0x323,
    0x302 /* offset 1434 */,
    0x41,
    0x306,
    0x301 /* offset 1437 */,
    0x61,
    0x306,
    0x301 /* offset 1440 */,
    0x41,
    0x306,
    0x300 /* offset 1443 */,
    0x61,
    0x306,
    0x300 /* offset 1446 */,
    0x41,
    0x306,
    0x309 /* offset 1449 */,
    0x61,
    0x306,
    0x309 /* offset 1452 */,
    0x41,
    0x306,
    0x303 /* offset 1455 */,
    0x61,
    0x306,
    0x303 /* offset 1458 */,
    0x41,
    0x323,
    0x306 /* offset 1461 */,
    0x61,
    0x323,
    0x306 /* offset 1464 */,
    0x45,
    0x323 /* offset 1467 */,
    0x65,
    0x323 /* offset 1469 */,
    0x45,
    0x309 /* offset 1471 */,
    0x65,
    0x309 /* offset 1473 */,
    0x45,
    0x303 /* offset 1475 */,
    0x65,
    0x303 /* offset 1477 */,
    0x45,
    0x302,
    0x301 /* offset 1479 */,
    0x65,
    0x302,
    0x301 /* offset 1482 */,
    0x45,
    0x302,
    0x300 /* offset 1485 */,
    0x65,
    0x302,
    0x300 /* offset 1488 */,
    0x45,
    0x302,
    0x309 /* offset 1491 */,
    0x65,
    0x302,
    0x309 /* offset 1494 */,
    0x45,
    0x302,
    0x303 /* offset 1497 */,
    0x65,
    0x302,
    0x303 /* offset 1500 */,
    0x45,
    0x323,
    0x302 /* offset 1503 */,
    0x65,
    0x323,
    0x302 /* offset 1506 */,
    0x49,
    0x309 /* offset 1509 */,
    0x69,
    0x309 /* offset 1511 */,
    0x49,
    0x323 /* offset 1513 */,
    0x69,
    0x323 /* offset 1515 */,
    0x4f,
    0x323 /* offset 1517 */,
    0x6f,
    0x323 /* offset 1519 */,
    0x4f,
    0x309 /* offset 1521 */,
    0x6f,
    0x309 /* offset 1523 */,
    0x4f,
    0x302,
    0x301 /* offset 1525 */,
    0x6f,
    0x302,
    0x301 /* offset 1528 */,
    0x4f,
    0x302,
    0x300 /* offset 1531 */,
    0x6f,
    0x302,
    0x300 /* offset 1534 */,
    0x4f,
    0x302,
    0x309 /* offset 1537 */,
    0x6f,
    0x302,
    0x309 /* offset 1540 */,
    0x4f,
    0x302,
    0x303 /* offset 1543 */,
    0x6f,
    0x302,
    0x303 /* offset 1546 */,
    0x4f,
    0x323,
    0x302 /* offset 1549 */,
    0x6f,
    0x323,
    0x302 /* offset 1552 */,
    0x4f,
    0x31b,
    0x301 /* offset 1555 */,
    0x6f,
    0x31b,
    0x301 /* offset 1558 */,
    0x4f,
    0x31b,
    0x300 /* offset 1561 */,
    0x6f,
    0x31b,
    0x300 /* offset 1564 */,
    0x4f,
    0x31b,
    0x309 /* offset 1567 */,
    0x6f,
    0x31b,
    0x309 /* offset 1570 */,
    0x4f,
    0x31b,
    0x303 /* offset 1573 */,
    0x6f,
    0x31b,
    0x303 /* offset 1576 */,
    0x4f,
    0x31b,
    0x323 /* offset 1579 */,
    0x6f,
    0x31b,
    0x323 /* offset 1582 */,
    0x55,
    0x323 /* offset 1585 */,
    0x75,
    0x323 /* offset 1587 */,
    0x55,
    0x309 /* offset 1589 */,
    0x75,
    0x309 /* offset 1591 */,
    0x55,
    0x31b,
    0x301 /* offset 1593 */,
    0x75,
    0x31b,
    0x301 /* offset 1596 */,
    0x55,
    0x31b,
    0x300 /* offset 1599 */,
    0x75,
    0x31b,
    0x300 /* offset 1602 */,
    0x55,
    0x31b,
    0x309 /* offset 1605 */,
    0x75,
    0x31b,
    0x309 /* offset 1608 */,
    0x55,
    0x31b,
    0x303 /* offset 1611 */,
    0x75,
    0x31b,
    0x303 /* offset 1614 */,
    0x55,
    0x31b,
    0x323 /* offset 1617 */,
    0x75,
    0x31b,
    0x323 /* offset 1620 */,
    0x59,
    0x300 /* offset 1623 */,
    0x79,
    0x300 /* offset 1625 */,
    0x59,
    0x323 /* offset 1627 */,
    0x79,
    0x323 /* offset 1629 */,
    0x59,
    0x309 /* offset 1631 */,
    0x79,
    0x309 /* offset 1633 */,
    0x59,
    0x303 /* offset 1635 */,
    0x79,
    0x303 /* offset 1637 */,
    0x3b1,
    0x313 /* offset 1639 */,
    0x3b1,
    0x314 /* offset 1641 */,
    0x3b1,
    0x313,
    0x300 /* offset 1643 */,
    0x3b1,
    0x314,
    0x300 /* offset 1646 */,
    0x3b1,
    0x313,
    0x301 /* offset 1649 */,
    0x3b1,
    0x314,
    0x301 /* offset 1652 */,
    0x3b1,
    0x313,
    0x342 /* offset 1655 */,
    0x3b1,
    0x314,
    0x342 /* offset 1658 */,
    0x391,
    0x313 /* offset 1661 */,
    0x391,
    0x314 /* offset 1663 */,
    0x391,
    0x313,
    0x300 /* offset 1665 */,
    0x391,
    0x314,
    0x300 /* offset 1668 */,
    0x391,
    0x313,
    0x301 /* offset 1671 */,
    0x391,
    0x314,
    0x301 /* offset 1674 */,
    0x391,
    0x313,
    0x342 /* offset 1677 */,
    0x391,
    0x314,
    0x342 /* offset 1680 */,
    0x3b5,
    0x313 /* offset 1683 */,
    0x3b5,
    0x314 /* offset 1685 */,
    0x3b5,
    0x313,
    0x300 /* offset 1687 */,
    0x3b5,
    0x314,
    0x300 /* offset 1690 */,
    0x3b5,
    0x313,
    0x301 /* offset 1693 */,
    0x3b5,
    0x314,
    0x301 /* offset 1696 */,
    0x395,
    0x313 /* offset 1699 */,
    0x395,
    0x314 /* offset 1701 */,
    0x395,
    0x313,
    0x300 /* offset 1703 */,
    0x395,
    0x314,
    0x300 /* offset 1706 */,
    0x395,
    0x313,
    0x301 /* offset 1709 */,
    0x395,
    0x314,
    0x301 /* offset 1712 */,
    0x3b7,
    0x313 /* offset 1715 */,
    0x3b7,
    0x314 /* offset 1717 */,
    0x3b7,
    0x313,
    0x300 /* offset 1719 */,
    0x3b7,
    0x314,
    0x300 /* offset 1722 */,
    0x3b7,
    0x313,
    0x301 /* offset 1725 */,
    0x3b7,
    0x314,
    0x301 /* offset 1728 */,
    0x3b7,
    0x313,
    0x342 /* offset 1731 */,
    0x3b7,
    0x314,
    0x342 /* offset 1734 */,
    0x397,
    0x313 /* offset 1737 */,
    0x397,
    0x314 /* offset 1739 */,
    0x397,
    0x313,
    0x300 /* offset 1741 */,
    0x397,
    0x314,
    0x300 /* offset 1744 */,
    0x397,
    0x313,
    0x301 /* offset 1747 */,
    0x397,
    0x314,
    0x301 /* offset 1750 */,
    0x397,
    0x313,
    0x342 /* offset 1753 */,
    0x397,
    0x314,
    0x342 /* offset 1756 */,
    0x3b9,
    0x313 /* offset 1759 */,
    0x3b9,
    0x314 /* offset 1761 */,
    0x3b9,
    0x313,
    0x300 /* offset 1763 */,
    0x3b9,
    0x314,
    0x300 /* offset 1766 */,
    0x3b9,
    0x313,
    0x301 /* offset 1769 */,
    0x3b9,
    0x314,
    0x301 /* offset 1772 */,
    0x3b9,
    0x313,
    0x342 /* offset 1775 */,
    0x3b9,
    0x314,
    0x342 /* offset 1778 */,
    0x399,
    0x313 /* offset 1781 */,
    0x399,
    0x314 /* offset 1783 */,
    0x399,
    0x313,
    0x300 /* offset 1785 */,
    0x399,
    0x314,
    0x300 /* offset 1788 */,
    0x399,
    0x313,
    0x301 /* offset 1791 */,
    0x399,
    0x314,
    0x301 /* offset 1794 */,
    0x399,
    0x313,
    0x342 /* offset 1797 */,
    0x399,
    0x314,
    0x342 /* offset 1800 */,
    0x3bf,
    0x313 /* offset 1803 */,
    0x3bf,
    0x314 /* offset 1805 */,
    0x3bf,
    0x313,
    0x300 /* offset 1807 */,
    0x3bf,
    0x314,
    0x300 /* offset 1810 */,
    0x3bf,
    0x313,
    0x301 /* offset 1813 */,
    0x3bf,
    0x314,
    0x301 /* offset 1816 */,
    0x39f,
    0x313 /* offset 1819 */,
    0x39f,
    0x314 /* offset 1821 */,
    0x39f,
    0x313,
    0x300 /* offset 1823 */,
    0x39f,
    0x314,
    0x300 /* offset 1826 */,
    0x39f,
    0x313,
    0x301 /* offset 1829 */,
    0x39f,
    0x314,
    0x301 /* offset 1832 */,
    0x3c5,
    0x313 /* offset 1835 */,
    0x3c5,
    0x314 /* offset 1837 */,
    0x3c5,
    0x313,
    0x300 /* offset 1839 */,
    0x3c5,
    0x314,
    0x300 /* offset 1842 */,
    0x3c5,
    0x313,
    0x301 /* offset 1845 */,
    0x3c5,
    0x314,
    0x301 /* offset 1848 */,
    0x3c5,
    0x313,
    0x342 /* offset 1851 */,
    0x3c5,
    0x314,
    0x342 /* offset 1854 */,
    0x3a5,
    0x314 /* offset 1857 */,
    0x3a5,
    0x314,
    0x300 /* offset 1859 */,
    0x3a5,
    0x314,
    0x301 /* offset 1862 */,
    0x3a5,
    0x314,
    0x342 /* offset 1865 */,
    0x3c9,
    0x313 /* offset 1868 */,
    0x3c9,
    0x314 /* offset 1870 */,
    0x3c9,
    0x313,
    0x300 /* offset 1872 */,
    0x3c9,
    0x314,
    0x300 /* offset 1875 */,
    0x3c9,
    0x313,
    0x301 /* offset 1878 */,
    0x3c9,
    0x314,
    0x301 /* offset 1881 */,
    0x3c9,
    0x313,
    0x342 /* offset 1884 */,
    0x3c9,
    0x314,
    0x342 /* offset 1887 */,
    0x3a9,
    0x313 /* offset 1890 */,
    0x3a9,
    0x314 /* offset 1892 */,
    0x3a9,
    0x313,
    0x300 /* offset 1894 */,
    0x3a9,
    0x314,
    0x300 /* offset 1897 */,
    0x3a9,
    0x313,
    0x301 /* offset 1900 */,
    0x3a9,
    0x314,
    0x301 /* offset 1903 */,
    0x3a9,
    0x313,
    0x342 /* offset 1906 */,
    0x3a9,
    0x314,
    0x342 /* offset 1909 */,
    0x3b1,
    0x300 /* offset 1912 */,
    0x3b5,
    0x300 /* offset 1914 */,
    0x3b7,
    0x300 /* offset 1916 */,
    0x3b9,
    0x300 /* offset 1918 */,
    0x3bf,
    0x300 /* offset 1920 */,
    0x3c5,
    0x300 /* offset 1922 */,
    0x3c9,
    0x300 /* offset 1924 */,
    0x3b1,
    0x313,
    0x345 /* offset 1926 */,
    0x3b1,
    0x314,
    0x345 /* offset 1929 */,
    0x3b1,
    0x313,
    0x300,
    0x345 /* offset 1932 */,
    0x3b1,
    0x314,
    0x300,
    0x345 /* offset 1936 */,
    0x3b1,
    0x313,
    0x301,
    0x345 /* offset 1940 */,
    0x3b1,
    0x314,
    0x301,
    0x345 /* offset 1944 */,
    0x3b1,
    0x313,
    0x342,
    0x345 /* offset 1948 */,
    0x3b1,
    0x314,
    0x342,
    0x345 /* offset 1952 */,
    0x391,
    0x313,
    0x345 /* offset 1956 */,
    0x391,
    0x314,
    0x345 /* offset 1959 */,
    0x391,
    0x313,
    0x300,
    0x345 /* offset 1962 */,
    0x391,
    0x314,
    0x300,
    0x345 /* offset 1966 */,
    0x391,
    0x313,
    0x301,
    0x345 /* offset 1970 */,
    0x391,
    0x314,
    0x301,
    0x345 /* offset 1974 */,
    0x391,
    0x313,
    0x342,
    0x345 /* offset 1978 */,
    0x391,
    0x314,
    0x342,
    0x345 /* offset 1982 */,
    0x3b7,
    0x313,
    0x345 /* offset 1986 */,
    0x3b7,
    0x314,
    0x345 /* offset 1989 */,
    0x3b7,
    0x313,
    0x300,
    0x345 /* offset 1992 */,
    0x3b7,
    0x314,
    0x300,
    0x345 /* offset 1996 */,
    0x3b7,
    0x313,
    0x301,
    0x345 /* offset 2000 */,
    0x3b7,
    0x314,
    0x301,
    0x345 /* offset 2004 */,
    0x3b7,
    0x313,
    0x342,
    0x345 /* offset 2008 */,
    0x3b7,
    0x314,
    0x342,
    0x345 /* offset 2012 */,
    0x397,
    0x313,
    0x345 /* offset 2016 */,
    0x397,
    0x314,
    0x345 /* offset 2019 */,
    0x397,
    0x313,
    0x300,
    0x345 /* offset 2022 */,
    0x397,
    0x314,
    0x300,
    0x345 /* offset 2026 */,
    0x397,
    0x313,
    0x301,
    0x345 /* offset 2030 */,
    0x397,
    0x314,
    0x301,
    0x345 /* offset 2034 */,
    0x397,
    0x313,
    0x342,
    0x345 /* offset 2038 */,
    0x397,
    0x314,
    0x342,
    0x345 /* offset 2042 */,
    0x3c9,
    0x313,
    0x345 /* offset 2046 */,
    0x3c9,
    0x314,
    0x345 /* offset 2049 */,
    0x3c9,
    0x313,
    0x300,
    0x345 /* offset 2052 */,
    0x3c9,
    0x314,
    0x300,
    0x345 /* offset 2056 */,
    0x3c9,
    0x313,
    0x301,
    0x345 /* offset 2060 */,
    0x3c9,
    0x314,
    0x301,
    0x345 /* offset 2064 */,
    0x3c9,
    0x313,
    0x342,
    0x345 /* offset 2068 */,
    0x3c9,
    0x314,
    0x342,
    0x345 /* offset 2072 */,
    0x3a9,
    0x313,
    0x345 /* offset 2076 */,
    0x3a9,
    0x314,
    0x345 /* offset 2079 */,
    0x3a9,
    0x313,
    0x300,
    0x345 /* offset 2082 */,
    0x3a9,
    0x314,
    0x300,
    0x345 /* offset 2086 */,
    0x3a9,
    0x313,
    0x301,
    0x345 /* offset 2090 */,
    0x3a9,
    0x314,
    0x301,
    0x345 /* offset 2094 */,
    0x3a9,
    0x313,
    0x342,
    0x345 /* offset 2098 */,
    0x3a9,
    0x314,
    0x342,
    0x345 /* offset 2102 */,
    0x3b1,
    0x306 /* offset 2106 */,
    0x3b1,
    0x304 /* offset 2108 */,
    0x3b1,
    0x300,
    0x345 /* offset 2110 */,
    0x3b1,
    0x345 /* offset 2113 */,
    0x3b1,
    0x301,
    0x345 /* offset 2115 */,
    0x3b1,
    0x342 /* offset 2118 */,
    0x3b1,
    0x342,
    0x345 /* offset 2120 */,
    0x391,
    0x306 /* offset 2123 */,
    0x391,
    0x304 /* offset 2125 */,
    0x391,
    0x300 /* offset 2127 */,
    0x391,
    0x345 /* offset 2129 */,
    0x20,
    0x313 /* offset 2131 */,
    0x3b9 /* offset 2133 */,
    0x20,
    0x342 /* offset 2134 */,
    0x20,
    0x308,
    0x342 /* offset 2136 */,
    0x3b7,
    0x300,
    0x345 /* offset 2139 */,
    0x3b7,
    0x345 /* offset 2142 */,
    0x3b7,
    0x301,
    0x345 /* offset 2144 */,
    0x3b7,
    0x342 /* offset 2147 */,
    0x3b7,
    0x342,
    0x345 /* offset 2149 */,
    0x395,
    0x300 /* offset 2152 */,
    0x397,
    0x300 /* offset 2154 */,
    0x397,
    0x345 /* offset 2156 */,
    0x20,
    0x313,
    0x300 /* offset 2158 */,
    0x20,
    0x313,
    0x301 /* offset 2161 */,
    0x20,
    0x313,
    0x342 /* offset 2164 */,
    0x3b9,
    0x306 /* offset 2167 */,
    0x3b9,
    0x304 /* offset 2169 */,
    0x3b9,
    0x308,
    0x300 /* offset 2171 */,
    0x3b9,
    0x342 /* offset 2174 */,
    0x3b9,
    0x308,
    0x342 /* offset 2176 */,
    0x399,
    0x306 /* offset 2179 */,
    0x399,
    0x304 /* offset 2181 */,
    0x399,
    0x300 /* offset 2183 */,
    0x20,
    0x314,
    0x300 /* offset 2185 */,
    0x20,
    0x314,
    0x301 /* offset 2188 */,
    0x20,
    0x314,
    0x342 /* offset 2191 */,
    0x3c5,
    0x306 /* offset 2194 */,
    0x3c5,
    0x304 /* offset 2196 */,
    0x3c5,
    0x308,
    0x300 /* offset 2198 */,
    0x3c1,
    0x313 /* offset 2201 */,
    0x3c1,
    0x314 /* offset 2203 */,
    0x3c5,
    0x342 /* offset 2205 */,
    0x3c5,
    0x308,
    0x342 /* offset 2207 */,
    0x3a5,
    0x306 /* offset 2210 */,
    0x3a5,
    0x304 /* offset 2212 */,
    0x3a5,
    0x300 /* offset 2214 */,
    0x3a1,
    0x314 /* offset 2216 */,
    0x20,
    0x308,
    0x300 /* offset 2218 */,
    0x60 /* offset 2221 */,
    0x3c9,
    0x300,
    0x345 /* offset 2222 */,
    0x3c9,
    0x345 /* offset 2225 */,
    0x3c9,
    0x301,
    0x345 /* offset 2227 */,
    0x3c9,
    0x342 /* offset 2230 */,
    0x3c9,
    0x342,
    0x345 /* offset 2232 */,
    0x39f,
    0x300 /* offset 2235 */,
    0x3a9,
    0x300 /* offset 2237 */,
    0x3a9,
    0x345 /* offset 2239 */,
    0x20,
    0x314 /* offset 2241 */,
    0x2010 /* offset 2243 */,
    0x20,
    0x333 /* offset 2244 */,
    0x2e /* offset 2246 */,
    0x2e,
    0x2e /* offset 2247 */,
    0x2e,
    0x2e,
    0x2e /* offset 2249 */,
    0x2032,
    0x2032 /* offset 2252 */,
    0x2032,
    0x2032,
    0x2032 /* offset 2254 */,
    0x2035,
    0x2035 /* offset 2257 */,
    0x2035,
    0x2035,
    0x2035 /* offset 2259 */,
    0x21,
    0x21 /* offset 2262 */,
    0x20,
    0x305 /* offset 2264 */,
    0x3f,
    0x3f /* offset 2266 */,
    0x3f,
    0x21 /* offset 2268 */,
    0x21,
    0x3f /* offset 2270 */,
    0x2032,
    0x2032,
    0x2032,
    0x2032 /* offset 2272 */,
    0x30 /* offset 2276 */,
    0x34 /* offset 2277 */,
    0x35 /* offset 2278 */,
    0x36 /* offset 2279 */,
    0x37 /* offset 2280 */,
    0x38 /* offset 2281 */,
    0x39 /* offset 2282 */,
    0x2b /* offset 2283 */,
    0x2212 /* offset 2284 */,
    0x3d /* offset 2285 */,
    0x28 /* offset 2286 */,
    0x29 /* offset 2287 */,
    0x6e /* offset 2288 */,
    0x52,
    0x73 /* offset 2289 */,
    0x61,
    0x2f,
    0x63 /* offset 2291 */,
    0x61,
    0x2f,
    0x73 /* offset 2294 */,
    0x43 /* offset 2297 */,
    0xb0,
    0x43 /* offset 2298 */,
    0x63,
    0x2f,
    0x6f /* offset 2300 */,
    0x63,
    0x2f,
    0x75 /* offset 2303 */,
    0x190 /* offset 2306 */,
    0xb0,
    0x46 /* offset 2307 */,
    0x127 /* offset 2309 */,
    0x4e,
    0x6f /* offset 2310 */,
    0x51 /* offset 2312 */,
    0x53,
    0x4d /* offset 2313 */,
    0x54,
    0x45,
    0x4c /* offset 2315 */,
    0x54,
    0x4d /* offset 2318 */,
    0x5a /* offset 2320 */,
    0x3a9 /* offset 2321 */,
    0x46 /* offset 2322 */,
    0x5d0 /* offset 2323 */,
    0x5d1 /* offset 2324 */,
    0x5d2 /* offset 2325 */,
    0x5d3 /* offset 2326 */,
    0x46,
    0x41,
    0x58 /* offset 2327 */,
    0x393 /* offset 2330 */,
    0x3a0 /* offset 2331 */,
    0x2211 /* offset 2332 */,
    0x31,
    0x2044,
    0x37 /* offset 2333 */,
    0x31,
    0x2044,
    0x39 /* offset 2336 */,
    0x31,
    0x2044,
    0x31,
    0x30 /* offset 2339 */,
    0x31,
    0x2044,
    0x33 /* offset 2343 */,
    0x32,
    0x2044,
    0x33 /* offset 2346 */,
    0x31,
    0x2044,
    0x35 /* offset 2349 */,
    0x32,
    0x2044,
    0x35 /* offset 2352 */,
    0x33,
    0x2044,
    0x35 /* offset 2355 */,
    0x34,
    0x2044,
    0x35 /* offset 2358 */,
    0x31,
    0x2044,
    0x36 /* offset 2361 */,
    0x35,
    0x2044,
    0x36 /* offset 2364 */,
    0x31,
    0x2044,
    0x38 /* offset 2367 */,
    0x33,
    0x2044,
    0x38 /* offset 2370 */,
    0x35,
    0x2044,
    0x38 /* offset 2373 */,
    0x37,
    0x2044,
    0x38 /* offset 2376 */,
    0x31,
    0x2044 /* offset 2379 */,
    0x49,
    0x49 /* offset 2381 */,
    0x49,
    0x49,
    0x49 /* offset 2383 */,
    0x49,
    0x56 /* offset 2386 */,
    0x56 /* offset 2388 */,
    0x56,
    0x49 /* offset 2389 */,
    0x56,
    0x49,
    0x49 /* offset 2391 */,
    0x56,
    0x49,
    0x49,
    0x49 /* offset 2394 */,
    0x49,
    0x58 /* offset 2398 */,
    0x58 /* offset 2400 */,
    0x58,
    0x49 /* offset 2401 */,
    0x58,
    0x49,
    0x49 /* offset 2403 */,
    0x69,
    0x69 /* offset 2406 */,
    0x69,
    0x69,
    0x69 /* offset 2408 */,
    0x69,
    0x76 /* offset 2411 */,
    0x76,
    0x69 /* offset 2413 */,
    0x76,
    0x69,
    0x69 /* offset 2415 */,
    0x76,
    0x69,
    0x69,
    0x69 /* offset 2418 */,
    0x69,
    0x78 /* offset 2422 */,
    0x78,
    0x69 /* offset 2424 */,
    0x78,
    0x69,
    0x69 /* offset 2426 */,
    0x30,
    0x2044,
    0x33 /* offset 2429 */,
    0x2190,
    0x338 /* offset 2432 */,
    0x2192,
    0x338 /* offset 2434 */,
    0x2194,
    0x338 /* offset 2436 */,
    0x21d0,
    0x338 /* offset 2438 */,
    0x21d4,
    0x338 /* offset 2440 */,
    0x21d2,
    0x338 /* offset 2442 */,
    0x2203,
    0x338 /* offset 2444 */,
    0x2208,
    0x338 /* offset 2446 */,
    0x220b,
    0x338 /* offset 2448 */,
    0x2223,
    0x338 /* offset 2450 */,
    0x2225,
    0x338 /* offset 2452 */,
    0x222b,
    0x222b /* offset 2454 */,
    0x222b,
    0x222b,
    0x222b /* offset 2456 */,
    0x222e,
    0x222e /* offset 2459 */,
    0x222e,
    0x222e,
    0x222e /* offset 2461 */,
    0x223c,
    0x338 /* offset 2464 */,
    0x2243,
    0x338 /* offset 2466 */,
    0x2245,
    0x338 /* offset 2468 */,
    0x2248,
    0x338 /* offset 2470 */,
    0x3d,
    0x338 /* offset 2472 */,
    0x2261,
    0x338 /* offset 2474 */,
    0x224d,
    0x338 /* offset 2476 */,
    0x3c,
    0x338 /* offset 2478 */,
    0x3e,
    0x338 /* offset 2480 */,
    0x2264,
    0x338 /* offset 2482 */,
    0x2265,
    0x338 /* offset 2484 */,
    0x2272,
    0x338 /* offset 2486 */,
    0x2273,
    0x338 /* offset 2488 */,
    0x2276,
    0x338 /* offset 2490 */,
    0x2277,
    0x338 /* offset 2492 */,
    0x227a,
    0x338 /* offset 2494 */,
    0x227b,
    0x338 /* offset 2496 */,
    0x2282,
    0x338 /* offset 2498 */,
    0x2283,
    0x338 /* offset 2500 */,
    0x2286,
    0x338 /* offset 2502 */,
    0x2287,
    0x338 /* offset 2504 */,
    0x22a2,
    0x338 /* offset 2506 */,
    0x22a8,
    0x338 /* offset 2508 */,
    0x22a9,
    0x338 /* offset 2510 */,
    0x22ab,
    0x338 /* offset 2512 */,
    0x227c,
    0x338 /* offset 2514 */,
    0x227d,
    0x338 /* offset 2516 */,
    0x2291,
    0x338 /* offset 2518 */,
    0x2292,
    0x338 /* offset 2520 */,
    0x22b2,
    0x338 /* offset 2522 */,
    0x22b3,
    0x338 /* offset 2524 */,
    0x22b4,
    0x338 /* offset 2526 */,
    0x22b5,
    0x338 /* offset 2528 */,
    0x3008 /* offset 2530 */,
    0x3009 /* offset 2531 */,
    0x31,
    0x30 /* offset 2532 */,
    0x31,
    0x31 /* offset 2534 */,
    0x31,
    0x32 /* offset 2536 */,
    0x31,
    0x33 /* offset 2538 */,
    0x31,
    0x34 /* offset 2540 */,
    0x31,
    0x35 /* offset 2542 */,
    0x31,
    0x36 /* offset 2544 */,
    0x31,
    0x37 /* offset 2546 */,
    0x31,
    0x38 /* offset 2548 */,
    0x31,
    0x39 /* offset 2550 */,
    0x32,
    0x30 /* offset 2552 */,
    0x28,
    0x31,
    0x29 /* offset 2554 */,
    0x28,
    0x32,
    0x29 /* offset 2557 */,
    0x28,
    0x33,
    0x29 /* offset 2560 */,
    0x28,
    0x34,
    0x29 /* offset 2563 */,
    0x28,
    0x35,
    0x29 /* offset 2566 */,
    0x28,
    0x36,
    0x29 /* offset 2569 */,
    0x28,
    0x37,
    0x29 /* offset 2572 */,
    0x28,
    0x38,
    0x29 /* offset 2575 */,
    0x28,
    0x39,
    0x29 /* offset 2578 */,
    0x28,
    0x31,
    0x30,
    0x29 /* offset 2581 */,
    0x28,
    0x31,
    0x31,
    0x29 /* offset 2585 */,
    0x28,
    0x31,
    0x32,
    0x29 /* offset 2589 */,
    0x28,
    0x31,
    0x33,
    0x29 /* offset 2593 */,
    0x28,
    0x31,
    0x34,
    0x29 /* offset 2597 */,
    0x28,
    0x31,
    0x35,
    0x29 /* offset 2601 */,
    0x28,
    0x31,
    0x36,
    0x29 /* offset 2605 */,
    0x28,
    0x31,
    0x37,
    0x29 /* offset 2609 */,
    0x28,
    0x31,
    0x38,
    0x29 /* offset 2613 */,
    0x28,
    0x31,
    0x39,
    0x29 /* offset 2617 */,
    0x28,
    0x32,
    0x30,
    0x29 /* offset 2621 */,
    0x31,
    0x2e /* offset 2625 */,
    0x32,
    0x2e /* offset 2627 */,
    0x33,
    0x2e /* offset 2629 */,
    0x34,
    0x2e /* offset 2631 */,
    0x35,
    0x2e /* offset 2633 */,
    0x36,
    0x2e /* offset 2635 */,
    0x37,
    0x2e /* offset 2637 */,
    0x38,
    0x2e /* offset 2639 */,
    0x39,
    0x2e /* offset 2641 */,
    0x31,
    0x30,
    0x2e /* offset 2643 */,
    0x31,
    0x31,
    0x2e /* offset 2646 */,
    0x31,
    0x32,
    0x2e /* offset 2649 */,
    0x31,
    0x33,
    0x2e /* offset 2652 */,
    0x31,
    0x34,
    0x2e /* offset 2655 */,
    0x31,
    0x35,
    0x2e /* offset 2658 */,
    0x31,
    0x36,
    0x2e /* offset 2661 */,
    0x31,
    0x37,
    0x2e /* offset 2664 */,
    0x31,
    0x38,
    0x2e /* offset 2667 */,
    0x31,
    0x39,
    0x2e /* offset 2670 */,
    0x32,
    0x30,
    0x2e /* offset 2673 */,
    0x28,
    0x61,
    0x29 /* offset 2676 */,
    0x28,
    0x62,
    0x29 /* offset 2679 */,
    0x28,
    0x63,
    0x29 /* offset 2682 */,
    0x28,
    0x64,
    0x29 /* offset 2685 */,
    0x28,
    0x65,
    0x29 /* offset 2688 */,
    0x28,
    0x66,
    0x29 /* offset 2691 */,
    0x28,
    0x67,
    0x29 /* offset 2694 */,
    0x28,
    0x68,
    0x29 /* offset 2697 */,
    0x28,
    0x69,
    0x29 /* offset 2700 */,
    0x28,
    0x6a,
    0x29 /* offset 2703 */,
    0x28,
    0x6b,
    0x29 /* offset 2706 */,
    0x28,
    0x6c,
    0x29 /* offset 2709 */,
    0x28,
    0x6d,
    0x29 /* offset 2712 */,
    0x28,
    0x6e,
    0x29 /* offset 2715 */,
    0x28,
    0x6f,
    0x29 /* offset 2718 */,
    0x28,
    0x70,
    0x29 /* offset 2721 */,
    0x28,
    0x71,
    0x29 /* offset 2724 */,
    0x28,
    0x72,
    0x29 /* offset 2727 */,
    0x28,
    0x73,
    0x29 /* offset 2730 */,
    0x28,
    0x74,
    0x29 /* offset 2733 */,
    0x28,
    0x75,
    0x29 /* offset 2736 */,
    0x28,
    0x76,
    0x29 /* offset 2739 */,
    0x28,
    0x77,
    0x29 /* offset 2742 */,
    0x28,
    0x78,
    0x29 /* offset 2745 */,
    0x28,
    0x79,
    0x29 /* offset 2748 */,
    0x28,
    0x7a,
    0x29 /* offset 2751 */,
    0x53 /* offset 2754 */,
    0x59 /* offset 2755 */,
    0x71 /* offset 2756 */,
    0x222b,
    0x222b,
    0x222b,
    0x222b /* offset 2757 */,
    0x3a,
    0x3a,
    0x3d /* offset 2761 */,
    0x3d,
    0x3d /* offset 2764 */,
    0x3d,
    0x3d,
    0x3d /* offset 2766 */,
    0x2add,
    0x338 /* offset 2769 */,
    0x2d61 /* offset 2771 */,
    0x6bcd /* offset 2772 */,
    0x9f9f /* offset 2773 */,
    0x4e00 /* offset 2774 */,
    0x4e28 /* offset 2775 */,
    0x4e36 /* offset 2776 */,
    0x4e3f /* offset 2777 */,
    0x4e59 /* offset 2778 */,
    0x4e85 /* offset 2779 */,
    0x4e8c /* offset 2780 */,
    0x4ea0 /* offset 2781 */,
    0x4eba /* offset 2782 */,
    0x513f /* offset 2783 */,
    0x5165 /* offset 2784 */,
    0x516b /* offset 2785 */,
    0x5182 /* offset 2786 */,
    0x5196 /* offset 2787 */,
    0x51ab /* offset 2788 */,
    0x51e0 /* offset 2789 */,
    0x51f5 /* offset 2790 */,
    0x5200 /* offset 2791 */,
    0x529b /* offset 2792 */,
    0x52f9 /* offset 2793 */,
    0x5315 /* offset 2794 */,
    0x531a /* offset 2795 */,
    0x5338 /* offset 2796 */,
    0x5341 /* offset 2797 */,
    0x535c /* offset 2798 */,
    0x5369 /* offset 2799 */,
    0x5382 /* offset 2800 */,
    0x53b6 /* offset 2801 */,
    0x53c8 /* offset 2802 */,
    0x53e3 /* offset 2803 */,
    0x56d7 /* offset 2804 */,
    0x571f /* offset 2805 */,
    0x58eb /* offset 2806 */,
    0x5902 /* offset 2807 */,
    0x590a /* offset 2808 */,
    0x5915 /* offset 2809 */,
    0x5927 /* offset 2810 */,
    0x5973 /* offset 2811 */,
    0x5b50 /* offset 2812 */,
    0x5b80 /* offset 2813 */,
    0x5bf8 /* offset 2814 */,
    0x5c0f /* offset 2815 */,
    0x5c22 /* offset 2816 */,
    0x5c38 /* offset 2817 */,
    0x5c6e /* offset 2818 */,
    0x5c71 /* offset 2819 */,
    0x5ddb /* offset 2820 */,
    0x5de5 /* offset 2821 */,
    0x5df1 /* offset 2822 */,
    0x5dfe /* offset 2823 */,
    0x5e72 /* offset 2824 */,
    0x5e7a /* offset 2825 */,
    0x5e7f /* offset 2826 */,
    0x5ef4 /* offset 2827 */,
    0x5efe /* offset 2828 */,
    0x5f0b /* offset 2829 */,
    0x5f13 /* offset 2830 */,
    0x5f50 /* offset 2831 */,
    0x5f61 /* offset 2832 */,
    0x5f73 /* offset 2833 */,
    0x5fc3 /* offset 2834 */,
    0x6208 /* offset 2835 */,
    0x6236 /* offset 2836 */,
    0x624b /* offset 2837 */,
    0x652f /* offset 2838 */,
    0x6534 /* offset 2839 */,
    0x6587 /* offset 2840 */,
    0x6597 /* offset 2841 */,
    0x65a4 /* offset 2842 */,
    0x65b9 /* offset 2843 */,
    0x65e0 /* offset 2844 */,
    0x65e5 /* offset 2845 */,
    0x66f0 /* offset 2846 */,
    0x6708 /* offset 2847 */,
    0x6728 /* offset 2848 */,
    0x6b20 /* offset 2849 */,
    0x6b62 /* offset 2850 */,
    0x6b79 /* offset 2851 */,
    0x6bb3 /* offset 2852 */,
    0x6bcb /* offset 2853 */,
    0x6bd4 /* offset 2854 */,
    0x6bdb /* offset 2855 */,
    0x6c0f /* offset 2856 */,
    0x6c14 /* offset 2857 */,
    0x6c34 /* offset 2858 */,
    0x706b /* offset 2859 */,
    0x722a /* offset 2860 */,
    0x7236 /* offset 2861 */,
    0x723b /* offset 2862 */,
    0x723f /* offset 2863 */,
    0x7247 /* offset 2864 */,
    0x7259 /* offset 2865 */,
    0x725b /* offset 2866 */,
    0x72ac /* offset 2867 */,
    0x7384 /* offset 2868 */,
    0x7389 /* offset 2869 */,
    0x74dc /* offset 2870 */,
    0x74e6 /* offset 2871 */,
    0x7518 /* offset 2872 */,
    0x751f /* offset 2873 */,
    0x7528 /* offset 2874 */,
    0x7530 /* offset 2875 */,
    0x758b /* offset 2876 */,
    0x7592 /* offset 2877 */,
    0x7676 /* offset 2878 */,
    0x767d /* offset 2879 */,
    0x76ae /* offset 2880 */,
    0x76bf /* offset 2881 */,
    0x76ee /* offset 2882 */,
    0x77db /* offset 2883 */,
    0x77e2 /* offset 2884 */,
    0x77f3 /* offset 2885 */,
    0x793a /* offset 2886 */,
    0x79b8 /* offset 2887 */,
    0x79be /* offset 2888 */,
    0x7a74 /* offset 2889 */,
    0x7acb /* offset 2890 */,
    0x7af9 /* offset 2891 */,
    0x7c73 /* offset 2892 */,
    0x7cf8 /* offset 2893 */,
    0x7f36 /* offset 2894 */,
    0x7f51 /* offset 2895 */,
    0x7f8a /* offset 2896 */,
    0x7fbd /* offset 2897 */,
    0x8001 /* offset 2898 */,
    0x800c /* offset 2899 */,
    0x8012 /* offset 2900 */,
    0x8033 /* offset 2901 */,
    0x807f /* offset 2902 */,
    0x8089 /* offset 2903 */,
    0x81e3 /* offset 2904 */,
    0x81ea /* offset 2905 */,
    0x81f3 /* offset 2906 */,
    0x81fc /* offset 2907 */,
    0x820c /* offset 2908 */,
    0x821b /* offset 2909 */,
    0x821f /* offset 2910 */,
    0x826e /* offset 2911 */,
    0x8272 /* offset 2912 */,
    0x8278 /* offset 2913 */,
    0x864d /* offset 2914 */,
    0x866b /* offset 2915 */,
    0x8840 /* offset 2916 */,
    0x884c /* offset 2917 */,
    0x8863 /* offset 2918 */,
    0x897e /* offset 2919 */,
    0x898b /* offset 2920 */,
    0x89d2 /* offset 2921 */,
    0x8a00 /* offset 2922 */,
    0x8c37 /* offset 2923 */,
    0x8c46 /* offset 2924 */,
    0x8c55 /* offset 2925 */,
    0x8c78 /* offset 2926 */,
    0x8c9d /* offset 2927 */,
    0x8d64 /* offset 2928 */,
    0x8d70 /* offset 2929 */,
    0x8db3 /* offset 2930 */,
    0x8eab /* offset 2931 */,
    0x8eca /* offset 2932 */,
    0x8f9b /* offset 2933 */,
    0x8fb0 /* offset 2934 */,
    0x8fb5 /* offset 2935 */,
    0x9091 /* offset 2936 */,
    0x9149 /* offset 2937 */,
    0x91c6 /* offset 2938 */,
    0x91cc /* offset 2939 */,
    0x91d1 /* offset 2940 */,
    0x9577 /* offset 2941 */,
    0x9580 /* offset 2942 */,
    0x961c /* offset 2943 */,
    0x96b6 /* offset 2944 */,
    0x96b9 /* offset 2945 */,
    0x96e8 /* offset 2946 */,
    0x9751 /* offset 2947 */,
    0x975e /* offset 2948 */,
    0x9762 /* offset 2949 */,
    0x9769 /* offset 2950 */,
    0x97cb /* offset 2951 */,
    0x97ed /* offset 2952 */,
    0x97f3 /* offset 2953 */,
    0x9801 /* offset 2954 */,
    0x98a8 /* offset 2955 */,
    0x98db /* offset 2956 */,
    0x98df /* offset 2957 */,
    0x9996 /* offset 2958 */,
    0x9999 /* offset 2959 */,
    0x99ac /* offset 2960 */,
    0x9aa8 /* offset 2961 */,
    0x9ad8 /* offset 2962 */,
    0x9adf /* offset 2963 */,
    0x9b25 /* offset 2964 */,
    0x9b2f /* offset 2965 */,
    0x9b32 /* offset 2966 */,
    0x9b3c /* offset 2967 */,
    0x9b5a /* offset 2968 */,
    0x9ce5 /* offset 2969 */,
    0x9e75 /* offset 2970 */,
    0x9e7f /* offset 2971 */,
    0x9ea5 /* offset 2972 */,
    0x9ebb /* offset 2973 */,
    0x9ec3 /* offset 2974 */,
    0x9ecd /* offset 2975 */,
    0x9ed1 /* offset 2976 */,
    0x9ef9 /* offset 2977 */,
    0x9efd /* offset 2978 */,
    0x9f0e /* offset 2979 */,
    0x9f13 /* offset 2980 */,
    0x9f20 /* offset 2981 */,
    0x9f3b /* offset 2982 */,
    0x9f4a /* offset 2983 */,
    0x9f52 /* offset 2984 */,
    0x9f8d /* offset 2985 */,
    0x9f9c /* offset 2986 */,
    0x9fa0 /* offset 2987 */,
    0x3012 /* offset 2988 */,
    0x5344 /* offset 2989 */,
    0x5345 /* offset 2990 */,
    0x304b,
    0x3099 /* offset 2991 */,
    0x304d,
    0x3099 /* offset 2993 */,
    0x304f,
    0x3099 /* offset 2995 */,
    0x3051,
    0x3099 /* offset 2997 */,
    0x3053,
    0x3099 /* offset 2999 */,
    0x3055,
    0x3099 /* offset 3001 */,
    0x3057,
    0x3099 /* offset 3003 */,
    0x3059,
    0x3099 /* offset 3005 */,
    0x305b,
    0x3099 /* offset 3007 */,
    0x305d,
    0x3099 /* offset 3009 */,
    0x305f,
    0x3099 /* offset 3011 */,
    0x3061,
    0x3099 /* offset 3013 */,
    0x3064,
    0x3099 /* offset 3015 */,
    0x3066,
    0x3099 /* offset 3017 */,
    0x3068,
    0x3099 /* offset 3019 */,
    0x306f,
    0x3099 /* offset 3021 */,
    0x306f,
    0x309a /* offset 3023 */,
    0x3072,
    0x3099 /* offset 3025 */,
    0x3072,
    0x309a /* offset 3027 */,
    0x3075,
    0x3099 /* offset 3029 */,
    0x3075,
    0x309a /* offset 3031 */,
    0x3078,
    0x3099 /* offset 3033 */,
    0x3078,
    0x309a /* offset 3035 */,
    0x307b,
    0x3099 /* offset 3037 */,
    0x307b,
    0x309a /* offset 3039 */,
    0x3046,
    0x3099 /* offset 3041 */,
    0x20,
    0x3099 /* offset 3043 */,
    0x20,
    0x309a /* offset 3045 */,
    0x309d,
    0x3099 /* offset 3047 */,
    0x3088,
    0x308a /* offset 3049 */,
    0x30ab,
    0x3099 /* offset 3051 */,
    0x30ad,
    0x3099 /* offset 3053 */,
    0x30af,
    0x3099 /* offset 3055 */,
    0x30b1,
    0x3099 /* offset 3057 */,
    0x30b3,
    0x3099 /* offset 3059 */,
    0x30b5,
    0x3099 /* offset 3061 */,
    0x30b7,
    0x3099 /* offset 3063 */,
    0x30b9,
    0x3099 /* offset 3065 */,
    0x30bb,
    0x3099 /* offset 3067 */,
    0x30bd,
    0x3099 /* offset 3069 */,
    0x30bf,
    0x3099 /* offset 3071 */,
    0x30c1,
    0x3099 /* offset 3073 */,
    0x30c4,
    0x3099 /* offset 3075 */,
    0x30c6,
    0x3099 /* offset 3077 */,
    0x30c8,
    0x3099 /* offset 3079 */,
    0x30cf,
    0x3099 /* offset 3081 */,
    0x30cf,
    0x309a /* offset 3083 */,
    0x30d2,
    0x3099 /* offset 3085 */,
    0x30d2,
    0x309a /* offset 3087 */,
    0x30d5,
    0x3099 /* offset 3089 */,
    0x30d5,
    0x309a /* offset 3091 */,
    0x30d8,
    0x3099 /* offset 3093 */,
    0x30d8,
    0x309a /* offset 3095 */,
    0x30db,
    0x3099 /* offset 3097 */,
    0x30db,
    0x309a /* offset 3099 */,
    0x30a6,
    0x3099 /* offset 3101 */,
    0x30ef,
    0x3099 /* offset 3103 */,
    0x30f0,
    0x3099 /* offset 3105 */,
    0x30f1,
    0x3099 /* offset 3107 */,
    0x30f2,
    0x3099 /* offset 3109 */,
    0x30fd,
    0x3099 /* offset 3111 */,
    0x30b3,
    0x30c8 /* offset 3113 */,
    0x1100 /* offset 3115 */,
    0x1101 /* offset 3116 */,
    0x11aa /* offset 3117 */,
    0x1102 /* offset 3118 */,
    0x11ac /* offset 3119 */,
    0x11ad /* offset 3120 */,
    0x1103 /* offset 3121 */,
    0x1104 /* offset 3122 */,
    0x1105 /* offset 3123 */,
    0x11b0 /* offset 3124 */,
    0x11b1 /* offset 3125 */,
    0x11b2 /* offset 3126 */,
    0x11b3 /* offset 3127 */,
    0x11b4 /* offset 3128 */,
    0x11b5 /* offset 3129 */,
    0x111a /* offset 3130 */,
    0x1106 /* offset 3131 */,
    0x1107 /* offset 3132 */,
    0x1108 /* offset 3133 */,
    0x1121 /* offset 3134 */,
    0x1109 /* offset 3135 */,
    0x110a /* offset 3136 */,
    0x110b /* offset 3137 */,
    0x110c /* offset 3138 */,
    0x110d /* offset 3139 */,
    0x110e /* offset 3140 */,
    0x110f /* offset 3141 */,
    0x1110 /* offset 3142 */,
    0x1111 /* offset 3143 */,
    0x1112 /* offset 3144 */,
    0x1161 /* offset 3145 */,
    0x1162 /* offset 3146 */,
    0x1163 /* offset 3147 */,
    0x1164 /* offset 3148 */,
    0x1165 /* offset 3149 */,
    0x1166 /* offset 3150 */,
    0x1167 /* offset 3151 */,
    0x1168 /* offset 3152 */,
    0x1169 /* offset 3153 */,
    0x116a /* offset 3154 */,
    0x116b /* offset 3155 */,
    0x116c /* offset 3156 */,
    0x116d /* offset 3157 */,
    0x116e /* offset 3158 */,
    0x116f /* offset 3159 */,
    0x1170 /* offset 3160 */,
    0x1171 /* offset 3161 */,
    0x1172 /* offset 3162 */,
    0x1173 /* offset 3163 */,
    0x1174 /* offset 3164 */,
    0x1175 /* offset 3165 */,
    0x1160 /* offset 3166 */,
    0x1114 /* offset 3167 */,
    0x1115 /* offset 3168 */,
    0x11c7 /* offset 3169 */,
    0x11c8 /* offset 3170 */,
    0x11cc /* offset 3171 */,
    0x11ce /* offset 3172 */,
    0x11d3 /* offset 3173 */,
    0x11d7 /* offset 3174 */,
    0x11d9 /* offset 3175 */,
    0x111c /* offset 3176 */,
    0x11dd /* offset 3177 */,
    0x11df /* offset 3178 */,
    0x111d /* offset 3179 */,
    0x111e /* offset 3180 */,
    0x1120 /* offset 3181 */,
    0x1122 /* offset 3182 */,
    0x1123 /* offset 3183 */,
    0x1127 /* offset 3184 */,
    0x1129 /* offset 3185 */,
    0x112b /* offset 3186 */,
    0x112c /* offset 3187 */,
    0x112d /* offset 3188 */,
    0x112e /* offset 3189 */,
    0x112f /* offset 3190 */,
    0x1132 /* offset 3191 */,
    0x1136 /* offset 3192 */,
    0x1140 /* offset 3193 */,
    0x1147 /* offset 3194 */,
    0x114c /* offset 3195 */,
    0x11f1 /* offset 3196 */,
    0x11f2 /* offset 3197 */,
    0x1157 /* offset 3198 */,
    0x1158 /* offset 3199 */,
    0x1159 /* offset 3200 */,
    0x1184 /* offset 3201 */,
    0x1185 /* offset 3202 */,
    0x1188 /* offset 3203 */,
    0x1191 /* offset 3204 */,
    0x1192 /* offset 3205 */,
    0x1194 /* offset 3206 */,
    0x119e /* offset 3207 */,
    0x11a1 /* offset 3208 */,
    0x4e09 /* offset 3209 */,
    0x56db /* offset 3210 */,
    0x4e0a /* offset 3211 */,
    0x4e2d /* offset 3212 */,
    0x4e0b /* offset 3213 */,
    0x7532 /* offset 3214 */,
    0x4e19 /* offset 3215 */,
    0x4e01 /* offset 3216 */,
    0x5929 /* offset 3217 */,
    0x5730 /* offset 3218 */,
    0x28,
    0x1100,
    0x29 /* offset 3219 */,
    0x28,
    0x1102,
    0x29 /* offset 3222 */,
    0x28,
    0x1103,
    0x29 /* offset 3225 */,
    0x28,
    0x1105,
    0x29 /* offset 3228 */,
    0x28,
    0x1106,
    0x29 /* offset 3231 */,
    0x28,
    0x1107,
    0x29 /* offset 3234 */,
    0x28,
    0x1109,
    0x29 /* offset 3237 */,
    0x28,
    0x110b,
    0x29 /* offset 3240 */,
    0x28,
    0x110c,
    0x29 /* offset 3243 */,
    0x28,
    0x110e,
    0x29 /* offset 3246 */,
    0x28,
    0x110f,
    0x29 /* offset 3249 */,
    0x28,
    0x1110,
    0x29 /* offset 3252 */,
    0x28,
    0x1111,
    0x29 /* offset 3255 */,
    0x28,
    0x1112,
    0x29 /* offset 3258 */,
    0x28,
    0x1100,
    0x1161,
    0x29 /* offset 3261 */,
    0x28,
    0x1102,
    0x1161,
    0x29 /* offset 3265 */,
    0x28,
    0x1103,
    0x1161,
    0x29 /* offset 3269 */,
    0x28,
    0x1105,
    0x1161,
    0x29 /* offset 3273 */,
    0x28,
    0x1106,
    0x1161,
    0x29 /* offset 3277 */,
    0x28,
    0x1107,
    0x1161,
    0x29 /* offset 3281 */,
    0x28,
    0x1109,
    0x1161,
    0x29 /* offset 3285 */,
    0x28,
    0x110b,
    0x1161,
    0x29 /* offset 3289 */,
    0x28,
    0x110c,
    0x1161,
    0x29 /* offset 3293 */,
    0x28,
    0x110e,
    0x1161,
    0x29 /* offset 3297 */,
    0x28,
    0x110f,
    0x1161,
    0x29 /* offset 3301 */,
    0x28,
    0x1110,
    0x1161,
    0x29 /* offset 3305 */,
    0x28,
    0x1111,
    0x1161,
    0x29 /* offset 3309 */,
    0x28,
    0x1112,
    0x1161,
    0x29 /* offset 3313 */,
    0x28,
    0x110c,
    0x116e,
    0x29 /* offset 3317 */,
    0x28,
    0x110b,
    0x1169,
    0x110c,
    0x1165,
    0x11ab,
    0x29 /* offset 3321 */,
    0x28,
    0x110b,
    0x1169,
    0x1112,
    0x116e,
    0x29 /* offset 3328 */,
    0x28,
    0x4e00,
    0x29 /* offset 3334 */,
    0x28,
    0x4e8c,
    0x29 /* offset 3337 */,
    0x28,
    0x4e09,
    0x29 /* offset 3340 */,
    0x28,
    0x56db,
    0x29 /* offset 3343 */,
    0x28,
    0x4e94,
    0x29 /* offset 3346 */,
    0x28,
    0x516d,
    0x29 /* offset 3349 */,
    0x28,
    0x4e03,
    0x29 /* offset 3352 */,
    0x28,
    0x516b,
    0x29 /* offset 3355 */,
    0x28,
    0x4e5d,
    0x29 /* offset 3358 */,
    0x28,
    0x5341,
    0x29 /* offset 3361 */,
    0x28,
    0x6708,
    0x29 /* offset 3364 */,
    0x28,
    0x706b,
    0x29 /* offset 3367 */,
    0x28,
    0x6c34,
    0x29 /* offset 3370 */,
    0x28,
    0x6728,
    0x29 /* offset 3373 */,
    0x28,
    0x91d1,
    0x29 /* offset 3376 */,
    0x28,
    0x571f,
    0x29 /* offset 3379 */,
    0x28,
    0x65e5,
    0x29 /* offset 3382 */,
    0x28,
    0x682a,
    0x29 /* offset 3385 */,
    0x28,
    0x6709,
    0x29 /* offset 3388 */,
    0x28,
    0x793e,
    0x29 /* offset 3391 */,
    0x28,
    0x540d,
    0x29 /* offset 3394 */,
    0x28,
    0x7279,
    0x29 /* offset 3397 */,
    0x28,
    0x8ca1,
    0x29 /* offset 3400 */,
    0x28,
    0x795d,
    0x29 /* offset 3403 */,
    0x28,
    0x52b4,
    0x29 /* offset 3406 */,
    0x28,
    0x4ee3,
    0x29 /* offset 3409 */,
    0x28,
    0x547c,
    0x29 /* offset 3412 */,
    0x28,
    0x5b66,
    0x29 /* offset 3415 */,
    0x28,
    0x76e3,
    0x29 /* offset 3418 */,
    0x28,
    0x4f01,
    0x29 /* offset 3421 */,
    0x28,
    0x8cc7,
    0x29 /* offset 3424 */,
    0x28,
    0x5354,
    0x29 /* offset 3427 */,
    0x28,
    0x796d,
    0x29 /* offset 3430 */,
    0x28,
    0x4f11,
    0x29 /* offset 3433 */,
    0x28,
    0x81ea,
    0x29 /* offset 3436 */,
    0x28,
    0x81f3,
    0x29 /* offset 3439 */,
    0x554f /* offset 3442 */,
    0x5e7c /* offset 3443 */,
    0x7b8f /* offset 3444 */,
    0x50,
    0x54,
    0x45 /* offset 3445 */,
    0x32,
    0x31 /* offset 3448 */,
    0x32,
    0x32 /* offset 3450 */,
    0x32,
    0x33 /* offset 3452 */,
    0x32,
    0x34 /* offset 3454 */,
    0x32,
    0x35 /* offset 3456 */,
    0x32,
    0x36 /* offset 3458 */,
    0x32,
    0x37 /* offset 3460 */,
    0x32,
    0x38 /* offset 3462 */,
    0x32,
    0x39 /* offset 3464 */,
    0x33,
    0x30 /* offset 3466 */,
    0x33,
    0x31 /* offset 3468 */,
    0x33,
    0x32 /* offset 3470 */,
    0x33,
    0x33 /* offset 3472 */,
    0x33,
    0x34 /* offset 3474 */,
    0x33,
    0x35 /* offset 3476 */,
    0x1100,
    0x1161 /* offset 3478 */,
    0x1102,
    0x1161 /* offset 3480 */,
    0x1103,
    0x1161 /* offset 3482 */,
    0x1105,
    0x1161 /* offset 3484 */,
    0x1106,
    0x1161 /* offset 3486 */,
    0x1107,
    0x1161 /* offset 3488 */,
    0x1109,
    0x1161 /* offset 3490 */,
    0x110b,
    0x1161 /* offset 3492 */,
    0x110c,
    0x1161 /* offset 3494 */,
    0x110e,
    0x1161 /* offset 3496 */,
    0x110f,
    0x1161 /* offset 3498 */,
    0x1110,
    0x1161 /* offset 3500 */,
    0x1111,
    0x1161 /* offset 3502 */,
    0x1112,
    0x1161 /* offset 3504 */,
    0x110e,
    0x1161,
    0x11b7,
    0x1100,
    0x1169 /* offset 3506 */,
    0x110c,
    0x116e,
    0x110b,
    0x1174 /* offset 3511 */,
    0x110b,
    0x116e /* offset 3515 */,
    0x4e94 /* offset 3517 */,
    0x516d /* offset 3518 */,
    0x4e03 /* offset 3519 */,
    0x4e5d /* offset 3520 */,
    0x682a /* offset 3521 */,
    0x6709 /* offset 3522 */,
    0x793e /* offset 3523 */,
    0x540d /* offset 3524 */,
    0x7279 /* offset 3525 */,
    0x8ca1 /* offset 3526 */,
    0x795d /* offset 3527 */,
    0x52b4 /* offset 3528 */,
    0x79d8 /* offset 3529 */,
    0x7537 /* offset 3530 */,
    0x9069 /* offset 3531 */,
    0x512a /* offset 3532 */,
    0x5370 /* offset 3533 */,
    0x6ce8 /* offset 3534 */,
    0x9805 /* offset 3535 */,
    0x4f11 /* offset 3536 */,
    0x5199 /* offset 3537 */,
    0x6b63 /* offset 3538 */,
    0x5de6 /* offset 3539 */,
    0x53f3 /* offset 3540 */,
    0x533b /* offset 3541 */,
    0x5b97 /* offset 3542 */,
    0x5b66 /* offset 3543 */,
    0x76e3 /* offset 3544 */,
    0x4f01 /* offset 3545 */,
    0x8cc7 /* offset 3546 */,
    0x5354 /* offset 3547 */,
    0x591c /* offset 3548 */,
    0x33,
    0x36 /* offset 3549 */,
    0x33,
    0x37 /* offset 3551 */,
    0x33,
    0x38 /* offset 3553 */,
    0x33,
    0x39 /* offset 3555 */,
    0x34,
    0x30 /* offset 3557 */,
    0x34,
    0x31 /* offset 3559 */,
    0x34,
    0x32 /* offset 3561 */,
    0x34,
    0x33 /* offset 3563 */,
    0x34,
    0x34 /* offset 3565 */,
    0x34,
    0x35 /* offset 3567 */,
    0x34,
    0x36 /* offset 3569 */,
    0x34,
    0x37 /* offset 3571 */,
    0x34,
    0x38 /* offset 3573 */,
    0x34,
    0x39 /* offset 3575 */,
    0x35,
    0x30 /* offset 3577 */,
    0x31,
    0x6708 /* offset 3579 */,
    0x32,
    0x6708 /* offset 3581 */,
    0x33,
    0x6708 /* offset 3583 */,
    0x34,
    0x6708 /* offset 3585 */,
    0x35,
    0x6708 /* offset 3587 */,
    0x36,
    0x6708 /* offset 3589 */,
    0x37,
    0x6708 /* offset 3591 */,
    0x38,
    0x6708 /* offset 3593 */,
    0x39,
    0x6708 /* offset 3595 */,
    0x31,
    0x30,
    0x6708 /* offset 3597 */,
    0x31,
    0x31,
    0x6708 /* offset 3600 */,
    0x31,
    0x32,
    0x6708 /* offset 3603 */,
    0x48,
    0x67 /* offset 3606 */,
    0x65,
    0x72,
    0x67 /* offset 3608 */,
    0x65,
    0x56 /* offset 3611 */,
    0x4c,
    0x54,
    0x44 /* offset 3613 */,
    0x30a2 /* offset 3616 */,
    0x30a4 /* offset 3617 */,
    0x30a6 /* offset 3618 */,
    0x30a8 /* offset 3619 */,
    0x30aa /* offset 3620 */,
    0x30ab /* offset 3621 */,
    0x30ad /* offset 3622 */,
    0x30af /* offset 3623 */,
    0x30b1 /* offset 3624 */,
    0x30b3 /* offset 3625 */,
    0x30b5 /* offset 3626 */,
    0x30b7 /* offset 3627 */,
    0x30b9 /* offset 3628 */,
    0x30bb /* offset 3629 */,
    0x30bd /* offset 3630 */,
    0x30bf /* offset 3631 */,
    0x30c1 /* offset 3632 */,
    0x30c4 /* offset 3633 */,
    0x30c6 /* offset 3634 */,
    0x30c8 /* offset 3635 */,
    0x30ca /* offset 3636 */,
    0x30cb /* offset 3637 */,
    0x30cc /* offset 3638 */,
    0x30cd /* offset 3639 */,
    0x30ce /* offset 3640 */,
    0x30cf /* offset 3641 */,
    0x30d2 /* offset 3642 */,
    0x30d5 /* offset 3643 */,
    0x30d8 /* offset 3644 */,
    0x30db /* offset 3645 */,
    0x30de /* offset 3646 */,
    0x30df /* offset 3647 */,
    0x30e0 /* offset 3648 */,
    0x30e1 /* offset 3649 */,
    0x30e2 /* offset 3650 */,
    0x30e4 /* offset 3651 */,
    0x30e6 /* offset 3652 */,
    0x30e8 /* offset 3653 */,
    0x30e9 /* offset 3654 */,
    0x30ea /* offset 3655 */,
    0x30eb /* offset 3656 */,
    0x30ec /* offset 3657 */,
    0x30ed /* offset 3658 */,
    0x30ef /* offset 3659 */,
    0x30f0 /* offset 3660 */,
    0x30f1 /* offset 3661 */,
    0x30f2 /* offset 3662 */,
    0x30a2,
    0x30cf,
    0x309a,
    0x30fc,
    0x30c8 /* offset 3663 */,
    0x30a2,
    0x30eb,
    0x30d5,
    0x30a1 /* offset 3668 */,
    0x30a2,
    0x30f3,
    0x30d8,
    0x309a,
    0x30a2 /* offset 3672 */,
    0x30a2,
    0x30fc,
    0x30eb /* offset 3677 */,
    0x30a4,
    0x30cb,
    0x30f3,
    0x30af,
    0x3099 /* offset 3680 */,
    0x30a4,
    0x30f3,
    0x30c1 /* offset 3685 */,
    0x30a6,
    0x30a9,
    0x30f3 /* offset 3688 */,
    0x30a8,
    0x30b9,
    0x30af,
    0x30fc,
    0x30c8,
    0x3099 /* offset 3691 */,
    0x30a8,
    0x30fc,
    0x30ab,
    0x30fc /* offset 3697 */,
    0x30aa,
    0x30f3,
    0x30b9 /* offset 3701 */,
    0x30aa,
    0x30fc,
    0x30e0 /* offset 3704 */,
    0x30ab,
    0x30a4,
    0x30ea /* offset 3707 */,
    0x30ab,
    0x30e9,
    0x30c3,
    0x30c8 /* offset 3710 */,
    0x30ab,
    0x30ed,
    0x30ea,
    0x30fc /* offset 3714 */,
    0x30ab,
    0x3099,
    0x30ed,
    0x30f3 /* offset 3718 */,
    0x30ab,
    0x3099,
    0x30f3,
    0x30de /* offset 3722 */,
    0x30ad,
    0x3099,
    0x30ab,
    0x3099 /* offset 3726 */,
    0x30ad,
    0x3099,
    0x30cb,
    0x30fc /* offset 3730 */,
    0x30ad,
    0x30e5,
    0x30ea,
    0x30fc /* offset 3734 */,
    0x30ad,
    0x3099,
    0x30eb,
    0x30bf,
    0x3099,
    0x30fc /* offset 3738 */,
    0x30ad,
    0x30ed /* offset 3744 */,
    0x30ad,
    0x30ed,
    0x30af,
    0x3099,
    0x30e9,
    0x30e0 /* offset 3746 */,
    0x30ad,
    0x30ed,
    0x30e1,
    0x30fc,
    0x30c8,
    0x30eb /* offset 3752 */,
    0x30ad,
    0x30ed,
    0x30ef,
    0x30c3,
    0x30c8 /* offset 3758 */,
    0x30af,
    0x3099,
    0x30e9,
    0x30e0 /* offset 3763 */,
    0x30af,
    0x3099,
    0x30e9,
    0x30e0,
    0x30c8,
    0x30f3 /* offset 3767 */,
    0x30af,
    0x30eb,
    0x30bb,
    0x3099,
    0x30a4,
    0x30ed /* offset 3773 */,
    0x30af,
    0x30ed,
    0x30fc,
    0x30cd /* offset 3779 */,
    0x30b1,
    0x30fc,
    0x30b9 /* offset 3783 */,
    0x30b3,
    0x30eb,
    0x30ca /* offset 3786 */,
    0x30b3,
    0x30fc,
    0x30db,
    0x309a /* offset 3789 */,
    0x30b5,
    0x30a4,
    0x30af,
    0x30eb /* offset 3793 */,
    0x30b5,
    0x30f3,
    0x30c1,
    0x30fc,
    0x30e0 /* offset 3797 */,
    0x30b7,
    0x30ea,
    0x30f3,
    0x30af,
    0x3099 /* offset 3802 */,
    0x30bb,
    0x30f3,
    0x30c1 /* offset 3807 */,
    0x30bb,
    0x30f3,
    0x30c8 /* offset 3810 */,
    0x30bf,
    0x3099,
    0x30fc,
    0x30b9 /* offset 3813 */,
    0x30c6,
    0x3099,
    0x30b7 /* offset 3817 */,
    0x30c8,
    0x3099,
    0x30eb /* offset 3820 */,
    0x30c8,
    0x30f3 /* offset 3823 */,
    0x30ca,
    0x30ce /* offset 3825 */,
    0x30ce,
    0x30c3,
    0x30c8 /* offset 3827 */,
    0x30cf,
    0x30a4,
    0x30c4 /* offset 3830 */,
    0x30cf,
    0x309a,
    0x30fc,
    0x30bb,
    0x30f3,
    0x30c8 /* offset 3833 */,
    0x30cf,
    0x309a,
    0x30fc,
    0x30c4 /* offset 3839 */,
    0x30cf,
    0x3099,
    0x30fc,
    0x30ec,
    0x30eb /* offset 3843 */,
    0x30d2,
    0x309a,
    0x30a2,
    0x30b9,
    0x30c8,
    0x30eb /* offset 3848 */,
    0x30d2,
    0x309a,
    0x30af,
    0x30eb /* offset 3854 */,
    0x30d2,
    0x309a,
    0x30b3 /* offset 3858 */,
    0x30d2,
    0x3099,
    0x30eb /* offset 3861 */,
    0x30d5,
    0x30a1,
    0x30e9,
    0x30c3,
    0x30c8,
    0x3099 /* offset 3864 */,
    0x30d5,
    0x30a3,
    0x30fc,
    0x30c8 /* offset 3870 */,
    0x30d5,
    0x3099,
    0x30c3,
    0x30b7,
    0x30a7,
    0x30eb /* offset 3874 */,
    0x30d5,
    0x30e9,
    0x30f3 /* offset 3880 */,
    0x30d8,
    0x30af,
    0x30bf,
    0x30fc,
    0x30eb /* offset 3883 */,
    0x30d8,
    0x309a,
    0x30bd /* offset 3888 */,
    0x30d8,
    0x309a,
    0x30cb,
    0x30d2 /* offset 3891 */,
    0x30d8,
    0x30eb,
    0x30c4 /* offset 3895 */,
    0x30d8,
    0x309a,
    0x30f3,
    0x30b9 /* offset 3898 */,
    0x30d8,
    0x309a,
    0x30fc,
    0x30b7,
    0x3099 /* offset 3902 */,
    0x30d8,
    0x3099,
    0x30fc,
    0x30bf /* offset 3907 */,
    0x30db,
    0x309a,
    0x30a4,
    0x30f3,
    0x30c8 /* offset 3911 */,
    0x30db,
    0x3099,
    0x30eb,
    0x30c8 /* offset 3916 */,
    0x30db,
    0x30f3 /* offset 3920 */,
    0x30db,
    0x309a,
    0x30f3,
    0x30c8,
    0x3099 /* offset 3922 */,
    0x30db,
    0x30fc,
    0x30eb /* offset 3927 */,
    0x30db,
    0x30fc,
    0x30f3 /* offset 3930 */,
    0x30de,
    0x30a4,
    0x30af,
    0x30ed /* offset 3933 */,
    0x30de,
    0x30a4,
    0x30eb /* offset 3937 */,
    0x30de,
    0x30c3,
    0x30cf /* offset 3940 */,
    0x30de,
    0x30eb,
    0x30af /* offset 3943 */,
    0x30de,
    0x30f3,
    0x30b7,
    0x30e7,
    0x30f3 /* offset 3946 */,
    0x30df,
    0x30af,
    0x30ed,
    0x30f3 /* offset 3951 */,
    0x30df,
    0x30ea /* offset 3955 */,
    0x30df,
    0x30ea,
    0x30cf,
    0x3099,
    0x30fc,
    0x30eb /* offset 3957 */,
    0x30e1,
    0x30ab,
    0x3099 /* offset 3963 */,
    0x30e1,
    0x30ab,
    0x3099,
    0x30c8,
    0x30f3 /* offset 3966 */,
    0x30e1,
    0x30fc,
    0x30c8,
    0x30eb /* offset 3971 */,
    0x30e4,
    0x30fc,
    0x30c8,
    0x3099 /* offset 3975 */,
    0x30e4,
    0x30fc,
    0x30eb /* offset 3979 */,
    0x30e6,
    0x30a2,
    0x30f3 /* offset 3982 */,
    0x30ea,
    0x30c3,
    0x30c8,
    0x30eb /* offset 3985 */,
    0x30ea,
    0x30e9 /* offset 3989 */,
    0x30eb,
    0x30d2,
    0x309a,
    0x30fc /* offset 3991 */,
    0x30eb,
    0x30fc,
    0x30d5,
    0x3099,
    0x30eb /* offset 3995 */,
    0x30ec,
    0x30e0 /* offset 4000 */,
    0x30ec,
    0x30f3,
    0x30c8,
    0x30b1,
    0x3099,
    0x30f3 /* offset 4002 */,
    0x30ef,
    0x30c3,
    0x30c8 /* offset 4008 */,
    0x30,
    0x70b9 /* offset 4011 */,
    0x31,
    0x70b9 /* offset 4013 */,
    0x32,
    0x70b9 /* offset 4015 */,
    0x33,
    0x70b9 /* offset 4017 */,
    0x34,
    0x70b9 /* offset 4019 */,
    0x35,
    0x70b9 /* offset 4021 */,
    0x36,
    0x70b9 /* offset 4023 */,
    0x37,
    0x70b9 /* offset 4025 */,
    0x38,
    0x70b9 /* offset 4027 */,
    0x39,
    0x70b9 /* offset 4029 */,
    0x31,
    0x30,
    0x70b9 /* offset 4031 */,
    0x31,
    0x31,
    0x70b9 /* offset 4034 */,
    0x31,
    0x32,
    0x70b9 /* offset 4037 */,
    0x31,
    0x33,
    0x70b9 /* offset 4040 */,
    0x31,
    0x34,
    0x70b9 /* offset 4043 */,
    0x31,
    0x35,
    0x70b9 /* offset 4046 */,
    0x31,
    0x36,
    0x70b9 /* offset 4049 */,
    0x31,
    0x37,
    0x70b9 /* offset 4052 */,
    0x31,
    0x38,
    0x70b9 /* offset 4055 */,
    0x31,
    0x39,
    0x70b9 /* offset 4058 */,
    0x32,
    0x30,
    0x70b9 /* offset 4061 */,
    0x32,
    0x31,
    0x70b9 /* offset 4064 */,
    0x32,
    0x32,
    0x70b9 /* offset 4067 */,
    0x32,
    0x33,
    0x70b9 /* offset 4070 */,
    0x32,
    0x34,
    0x70b9 /* offset 4073 */,
    0x68,
    0x50,
    0x61 /* offset 4076 */,
    0x64,
    0x61 /* offset 4079 */,
    0x41,
    0x55 /* offset 4081 */,
    0x62,
    0x61,
    0x72 /* offset 4083 */,
    0x6f,
    0x56 /* offset 4086 */,
    0x70,
    0x63 /* offset 4088 */,
    0x64,
    0x6d /* offset 4090 */,
    0x64,
    0x6d,
    0x32 /* offset 4092 */,
    0x64,
    0x6d,
    0x33 /* offset 4095 */,
    0x49,
    0x55 /* offset 4098 */,
    0x5e73,
    0x6210 /* offset 4100 */,
    0x662d,
    0x548c /* offset 4102 */,
    0x5927,
    0x6b63 /* offset 4104 */,
    0x660e,
    0x6cbb /* offset 4106 */,
    0x682a,
    0x5f0f,
    0x4f1a,
    0x793e /* offset 4108 */,
    0x70,
    0x41 /* offset 4112 */,
    0x6e,
    0x41 /* offset 4114 */,
    0x3bc,
    0x41 /* offset 4116 */,
    0x6d,
    0x41 /* offset 4118 */,
    0x6b,
    0x41 /* offset 4120 */,
    0x4b,
    0x42 /* offset 4122 */,
    0x4d,
    0x42 /* offset 4124 */,
    0x47,
    0x42 /* offset 4126 */,
    0x63,
    0x61,
    0x6c /* offset 4128 */,
    0x6b,
    0x63,
    0x61,
    0x6c /* offset 4131 */,
    0x70,
    0x46 /* offset 4135 */,
    0x6e,
    0x46 /* offset 4137 */,
    0x3bc,
    0x46 /* offset 4139 */,
    0x3bc,
    0x67 /* offset 4141 */,
    0x6d,
    0x67 /* offset 4143 */,
    0x6b,
    0x67 /* offset 4145 */,
    0x48,
    0x7a /* offset 4147 */,
    0x6b,
    0x48,
    0x7a /* offset 4149 */,
    0x4d,
    0x48,
    0x7a /* offset 4152 */,
    0x47,
    0x48,
    0x7a /* offset 4155 */,
    0x54,
    0x48,
    0x7a /* offset 4158 */,
    0x3bc,
    0x6c /* offset 4161 */,
    0x6d,
    0x6c /* offset 4163 */,
    0x64,
    0x6c /* offset 4165 */,
    0x6b,
    0x6c /* offset 4167 */,
    0x66,
    0x6d /* offset 4169 */,
    0x6e,
    0x6d /* offset 4171 */,
    0x3bc,
    0x6d /* offset 4173 */,
    0x6d,
    0x6d /* offset 4175 */,
    0x63,
    0x6d /* offset 4177 */,
    0x6b,
    0x6d /* offset 4179 */,
    0x6d,
    0x6d,
    0x32 /* offset 4181 */,
    0x63,
    0x6d,
    0x32 /* offset 4184 */,
    0x6d,
    0x32 /* offset 4187 */,
    0x6b,
    0x6d,
    0x32 /* offset 4189 */,
    0x6d,
    0x6d,
    0x33 /* offset 4192 */,
    0x63,
    0x6d,
    0x33 /* offset 4195 */,
    0x6d,
    0x33 /* offset 4198 */,
    0x6b,
    0x6d,
    0x33 /* offset 4200 */,
    0x6d,
    0x2215,
    0x73 /* offset 4203 */,
    0x6d,
    0x2215,
    0x73,
    0x32 /* offset 4206 */,
    0x50,
    0x61 /* offset 4210 */,
    0x6b,
    0x50,
    0x61 /* offset 4212 */,
    0x4d,
    0x50,
    0x61 /* offset 4215 */,
    0x47,
    0x50,
    0x61 /* offset 4218 */,
    0x72,
    0x61,
    0x64 /* offset 4221 */,
    0x72,
    0x61,
    0x64,
    0x2215,
    0x73 /* offset 4224 */,
    0x72,
    0x61,
    0x64,
    0x2215,
    0x73,
    0x32 /* offset 4229 */,
    0x70,
    0x73 /* offset 4235 */,
    0x6e,
    0x73 /* offset 4237 */,
    0x3bc,
    0x73 /* offset 4239 */,
    0x6d,
    0x73 /* offset 4241 */,
    0x70,
    0x56 /* offset 4243 */,
    0x6e,
    0x56 /* offset 4245 */,
    0x3bc,
    0x56 /* offset 4247 */,
    0x6d,
    0x56 /* offset 4249 */,
    0x6b,
    0x56 /* offset 4251 */,
    0x4d,
    0x56 /* offset 4253 */,
    0x70,
    0x57 /* offset 4255 */,
    0x6e,
    0x57 /* offset 4257 */,
    0x3bc,
    0x57 /* offset 4259 */,
    0x6d,
    0x57 /* offset 4261 */,
    0x6b,
    0x57 /* offset 4263 */,
    0x4d,
    0x57 /* offset 4265 */,
    0x6b,
    0x3a9 /* offset 4267 */,
    0x4d,
    0x3a9 /* offset 4269 */,
    0x61,
    0x2e,
    0x6d,
    0x2e /* offset 4271 */,
    0x42,
    0x71 /* offset 4275 */,
    0x63,
    0x63 /* offset 4277 */,
    0x63,
    0x64 /* offset 4279 */,
    0x43,
    0x2215,
    0x6b,
    0x67 /* offset 4281 */,
    0x43,
    0x6f,
    0x2e /* offset 4285 */,
    0x64,
    0x42 /* offset 4288 */,
    0x47,
    0x79 /* offset 4290 */,
    0x68,
    0x61 /* offset 4292 */,
    0x48,
    0x50 /* offset 4294 */,
    0x69,
    0x6e /* offset 4296 */,
    0x4b,
    0x4b /* offset 4298 */,
    0x4b,
    0x4d /* offset 4300 */,
    0x6b,
    0x74 /* offset 4302 */,
    0x6c,
    0x6d /* offset 4304 */,
    0x6c,
    0x6e /* offset 4306 */,
    0x6c,
    0x6f,
    0x67 /* offset 4308 */,
    0x6c,
    0x78 /* offset 4311 */,
    0x6d,
    0x62 /* offset 4313 */,
    0x6d,
    0x69,
    0x6c /* offset 4315 */,
    0x6d,
    0x6f,
    0x6c /* offset 4318 */,
    0x50,
    0x48 /* offset 4321 */,
    0x70,
    0x2e,
    0x6d,
    0x2e /* offset 4323 */,
    0x50,
    0x50,
    0x4d /* offset 4327 */,
    0x50,
    0x52 /* offset 4330 */,
    0x73,
    0x72 /* offset 4332 */,
    0x53,
    0x76 /* offset 4334 */,
    0x57,
    0x62 /* offset 4336 */,
    0x56,
    0x2215,
    0x6d /* offset 4338 */,
    0x41,
    0x2215,
    0x6d /* offset 4341 */,
    0x31,
    0x65e5 /* offset 4344 */,
    0x32,
    0x65e5 /* offset 4346 */,
    0x33,
    0x65e5 /* offset 4348 */,
    0x34,
    0x65e5 /* offset 4350 */,
    0x35,
    0x65e5 /* offset 4352 */,
    0x36,
    0x65e5 /* offset 4354 */,
    0x37,
    0x65e5 /* offset 4356 */,
    0x38,
    0x65e5 /* offset 4358 */,
    0x39,
    0x65e5 /* offset 4360 */,
    0x31,
    0x30,
    0x65e5 /* offset 4362 */,
    0x31,
    0x31,
    0x65e5 /* offset 4365 */,
    0x31,
    0x32,
    0x65e5 /* offset 4368 */,
    0x31,
    0x33,
    0x65e5 /* offset 4371 */,
    0x31,
    0x34,
    0x65e5 /* offset 4374 */,
    0x31,
    0x35,
    0x65e5 /* offset 4377 */,
    0x31,
    0x36,
    0x65e5 /* offset 4380 */,
    0x31,
    0x37,
    0x65e5 /* offset 4383 */,
    0x31,
    0x38,
    0x65e5 /* offset 4386 */,
    0x31,
    0x39,
    0x65e5 /* offset 4389 */,
    0x32,
    0x30,
    0x65e5 /* offset 4392 */,
    0x32,
    0x31,
    0x65e5 /* offset 4395 */,
    0x32,
    0x32,
    0x65e5 /* offset 4398 */,
    0x32,
    0x33,
    0x65e5 /* offset 4401 */,
    0x32,
    0x34,
    0x65e5 /* offset 4404 */,
    0x32,
    0x35,
    0x65e5 /* offset 4407 */,
    0x32,
    0x36,
    0x65e5 /* offset 4410 */,
    0x32,
    0x37,
    0x65e5 /* offset 4413 */,
    0x32,
    0x38,
    0x65e5 /* offset 4416 */,
    0x32,
    0x39,
    0x65e5 /* offset 4419 */,
    0x33,
    0x30,
    0x65e5 /* offset 4422 */,
    0x33,
    0x31,
    0x65e5 /* offset 4425 */,
    0x67,
    0x61,
    0x6c /* offset 4428 */,
    0x44a /* offset 4431 */,
    0x44c /* offset 4432 */,
    0xa76f /* offset 4433 */,
    0x126 /* offset 4434 */,
    0x153 /* offset 4435 */,
    0xa727 /* offset 4436 */,
    0xab37 /* offset 4437 */,
    0x26b /* offset 4438 */,
    0xab52 /* offset 4439 */,
    0x8c48 /* offset 4440 */,
    0x66f4 /* offset 4441 */,
    0x8cc8 /* offset 4442 */,
    0x6ed1 /* offset 4443 */,
    0x4e32 /* offset 4444 */,
    0x53e5 /* offset 4445 */,
    0x5951 /* offset 4446 */,
    0x5587 /* offset 4447 */,
    0x5948 /* offset 4448 */,
    0x61f6 /* offset 4449 */,
    0x7669 /* offset 4450 */,
    0x7f85 /* offset 4451 */,
    0x863f /* offset 4452 */,
    0x87ba /* offset 4453 */,
    0x88f8 /* offset 4454 */,
    0x908f /* offset 4455 */,
    0x6a02 /* offset 4456 */,
    0x6d1b /* offset 4457 */,
    0x70d9 /* offset 4458 */,
    0x73de /* offset 4459 */,
    0x843d /* offset 4460 */,
    0x916a /* offset 4461 */,
    0x99f1 /* offset 4462 */,
    0x4e82 /* offset 4463 */,
    0x5375 /* offset 4464 */,
    0x6b04 /* offset 4465 */,
    0x721b /* offset 4466 */,
    0x862d /* offset 4467 */,
    0x9e1e /* offset 4468 */,
    0x5d50 /* offset 4469 */,
    0x6feb /* offset 4470 */,
    0x85cd /* offset 4471 */,
    0x8964 /* offset 4472 */,
    0x62c9 /* offset 4473 */,
    0x81d8 /* offset 4474 */,
    0x881f /* offset 4475 */,
    0x5eca /* offset 4476 */,
    0x6717 /* offset 4477 */,
    0x6d6a /* offset 4478 */,
    0x72fc /* offset 4479 */,
    0x90ce /* offset 4480 */,
    0x4f86 /* offset 4481 */,
    0x51b7 /* offset 4482 */,
    0x52de /* offset 4483 */,
    0x64c4 /* offset 4484 */,
    0x6ad3 /* offset 4485 */,
    0x7210 /* offset 4486 */,
    0x76e7 /* offset 4487 */,
    0x8606 /* offset 4488 */,
    0x865c /* offset 4489 */,
    0x8def /* offset 4490 */,
    0x9732 /* offset 4491 */,
    0x9b6f /* offset 4492 */,
    0x9dfa /* offset 4493 */,
    0x788c /* offset 4494 */,
    0x797f /* offset 4495 */,
    0x7da0 /* offset 4496 */,
    0x83c9 /* offset 4497 */,
    0x9304 /* offset 4498 */,
    0x8ad6 /* offset 4499 */,
    0x58df /* offset 4500 */,
    0x5f04 /* offset 4501 */,
    0x7c60 /* offset 4502 */,
    0x807e /* offset 4503 */,
    0x7262 /* offset 4504 */,
    0x78ca /* offset 4505 */,
    0x8cc2 /* offset 4506 */,
    0x96f7 /* offset 4507 */,
    0x58d8 /* offset 4508 */,
    0x5c62 /* offset 4509 */,
    0x6a13 /* offset 4510 */,
    0x6dda /* offset 4511 */,
    0x6f0f /* offset 4512 */,
    0x7d2f /* offset 4513 */,
    0x7e37 /* offset 4514 */,
    0x964b /* offset 4515 */,
    0x52d2 /* offset 4516 */,
    0x808b /* offset 4517 */,
    0x51dc /* offset 4518 */,
    0x51cc /* offset 4519 */,
    0x7a1c /* offset 4520 */,
    0x7dbe /* offset 4521 */,
    0x83f1 /* offset 4522 */,
    0x9675 /* offset 4523 */,
    0x8b80 /* offset 4524 */,
    0x62cf /* offset 4525 */,
    0x8afe /* offset 4526 */,
    0x4e39 /* offset 4527 */,
    0x5be7 /* offset 4528 */,
    0x6012 /* offset 4529 */,
    0x7387 /* offset 4530 */,
    0x7570 /* offset 4531 */,
    0x5317 /* offset 4532 */,
    0x78fb /* offset 4533 */,
    0x4fbf /* offset 4534 */,
    0x5fa9 /* offset 4535 */,
    0x4e0d /* offset 4536 */,
    0x6ccc /* offset 4537 */,
    0x6578 /* offset 4538 */,
    0x7d22 /* offset 4539 */,
    0x53c3 /* offset 4540 */,
    0x585e /* offset 4541 */,
    0x7701 /* offset 4542 */,
    0x8449 /* offset 4543 */,
    0x8aaa /* offset 4544 */,
    0x6bba /* offset 4545 */,
    0x6c88 /* offset 4546 */,
    0x62fe /* offset 4547 */,
    0x82e5 /* offset 4548 */,
    0x63a0 /* offset 4549 */,
    0x7565 /* offset 4550 */,
    0x4eae /* offset 4551 */,
    0x5169 /* offset 4552 */,
    0x51c9 /* offset 4553 */,
    0x6881 /* offset 4554 */,
    0x7ce7 /* offset 4555 */,
    0x826f /* offset 4556 */,
    0x8ad2 /* offset 4557 */,
    0x91cf /* offset 4558 */,
    0x52f5 /* offset 4559 */,
    0x5442 /* offset 4560 */,
    0x5eec /* offset 4561 */,
    0x65c5 /* offset 4562 */,
    0x6ffe /* offset 4563 */,
    0x792a /* offset 4564 */,
    0x95ad /* offset 4565 */,
    0x9a6a /* offset 4566 */,
    0x9e97 /* offset 4567 */,
    0x9ece /* offset 4568 */,
    0x66c6 /* offset 4569 */,
    0x6b77 /* offset 4570 */,
    0x8f62 /* offset 4571 */,
    0x5e74 /* offset 4572 */,
    0x6190 /* offset 4573 */,
    0x6200 /* offset 4574 */,
    0x649a /* offset 4575 */,
    0x6f23 /* offset 4576 */,
    0x7149 /* offset 4577 */,
    0x7489 /* offset 4578 */,
    0x79ca /* offset 4579 */,
    0x7df4 /* offset 4580 */,
    0x806f /* offset 4581 */,
    0x8f26 /* offset 4582 */,
    0x84ee /* offset 4583 */,
    0x9023 /* offset 4584 */,
    0x934a /* offset 4585 */,
    0x5217 /* offset 4586 */,
    0x52a3 /* offset 4587 */,
    0x54bd /* offset 4588 */,
    0x70c8 /* offset 4589 */,
    0x88c2 /* offset 4590 */,
    0x5ec9 /* offset 4591 */,
    0x5ff5 /* offset 4592 */,
    0x637b /* offset 4593 */,
    0x6bae /* offset 4594 */,
    0x7c3e /* offset 4595 */,
    0x7375 /* offset 4596 */,
    0x4ee4 /* offset 4597 */,
    0x56f9 /* offset 4598 */,
    0x5dba /* offset 4599 */,
    0x601c /* offset 4600 */,
    0x73b2 /* offset 4601 */,
    0x7469 /* offset 4602 */,
    0x7f9a /* offset 4603 */,
    0x8046 /* offset 4604 */,
    0x9234 /* offset 4605 */,
    0x96f6 /* offset 4606 */,
    0x9748 /* offset 4607 */,
    0x9818 /* offset 4608 */,
    0x4f8b /* offset 4609 */,
    0x79ae /* offset 4610 */,
    0x91b4 /* offset 4611 */,
    0x96b8 /* offset 4612 */,
    0x60e1 /* offset 4613 */,
    0x4e86 /* offset 4614 */,
    0x50da /* offset 4615 */,
    0x5bee /* offset 4616 */,
    0x5c3f /* offset 4617 */,
    0x6599 /* offset 4618 */,
    0x71ce /* offset 4619 */,
    0x7642 /* offset 4620 */,
    0x84fc /* offset 4621 */,
    0x907c /* offset 4622 */,
    0x6688 /* offset 4623 */,
    0x962e /* offset 4624 */,
    0x5289 /* offset 4625 */,
    0x677b /* offset 4626 */,
    0x67f3 /* offset 4627 */,
    0x6d41 /* offset 4628 */,
    0x6e9c /* offset 4629 */,
    0x7409 /* offset 4630 */,
    0x7559 /* offset 4631 */,
    0x786b /* offset 4632 */,
    0x7d10 /* offset 4633 */,
    0x985e /* offset 4634 */,
    0x622e /* offset 4635 */,
    0x9678 /* offset 4636 */,
    0x502b /* offset 4637 */,
    0x5d19 /* offset 4638 */,
    0x6dea /* offset 4639 */,
    0x8f2a /* offset 4640 */,
    0x5f8b /* offset 4641 */,
    0x6144 /* offset 4642 */,
    0x6817 /* offset 4643 */,
    0x9686 /* offset 4644 */,
    0x5229 /* offset 4645 */,
    0x540f /* offset 4646 */,
    0x5c65 /* offset 4647 */,
    0x6613 /* offset 4648 */,
    0x674e /* offset 4649 */,
    0x68a8 /* offset 4650 */,
    0x6ce5 /* offset 4651 */,
    0x7406 /* offset 4652 */,
    0x75e2 /* offset 4653 */,
    0x7f79 /* offset 4654 */,
    0x88cf /* offset 4655 */,
    0x88e1 /* offset 4656 */,
    0x96e2 /* offset 4657 */,
    0x533f /* offset 4658 */,
    0x6eba /* offset 4659 */,
    0x541d /* offset 4660 */,
    0x71d0 /* offset 4661 */,
    0x7498 /* offset 4662 */,
    0x85fa /* offset 4663 */,
    0x96a3 /* offset 4664 */,
    0x9c57 /* offset 4665 */,
    0x9e9f /* offset 4666 */,
    0x6797 /* offset 4667 */,
    0x6dcb /* offset 4668 */,
    0x81e8 /* offset 4669 */,
    0x7b20 /* offset 4670 */,
    0x7c92 /* offset 4671 */,
    0x72c0 /* offset 4672 */,
    0x7099 /* offset 4673 */,
    0x8b58 /* offset 4674 */,
    0x4ec0 /* offset 4675 */,
    0x8336 /* offset 4676 */,
    0x523a /* offset 4677 */,
    0x5207 /* offset 4678 */,
    0x5ea6 /* offset 4679 */,
    0x62d3 /* offset 4680 */,
    0x7cd6 /* offset 4681 */,
    0x5b85 /* offset 4682 */,
    0x6d1e /* offset 4683 */,
    0x66b4 /* offset 4684 */,
    0x8f3b /* offset 4685 */,
    0x964d /* offset 4686 */,
    0x5ed3 /* offset 4687 */,
    0x5140 /* offset 4688 */,
    0x55c0 /* offset 4689 */,
    0x585a /* offset 4690 */,
    0x6674 /* offset 4691 */,
    0x51de /* offset 4692 */,
    0x732a /* offset 4693 */,
    0x76ca /* offset 4694 */,
    0x793c /* offset 4695 */,
    0x795e /* offset 4696 */,
    0x7965 /* offset 4697 */,
    0x798f /* offset 4698 */,
    0x9756 /* offset 4699 */,
    0x7cbe /* offset 4700 */,
    0x8612 /* offset 4701 */,
    0x8af8 /* offset 4702 */,
    0x9038 /* offset 4703 */,
    0x90fd /* offset 4704 */,
    0x98ef /* offset 4705 */,
    0x98fc /* offset 4706 */,
    0x9928 /* offset 4707 */,
    0x9db4 /* offset 4708 */,
    0x90de /* offset 4709 */,
    0x96b7 /* offset 4710 */,
    0x4fae /* offset 4711 */,
    0x50e7 /* offset 4712 */,
    0x514d /* offset 4713 */,
    0x52c9 /* offset 4714 */,
    0x52e4 /* offset 4715 */,
    0x5351 /* offset 4716 */,
    0x559d /* offset 4717 */,
    0x5606 /* offset 4718 */,
    0x5668 /* offset 4719 */,
    0x5840 /* offset 4720 */,
    0x58a8 /* offset 4721 */,
    0x5c64 /* offset 4722 */,
    0x6094 /* offset 4723 */,
    0x6168 /* offset 4724 */,
    0x618e /* offset 4725 */,
    0x61f2 /* offset 4726 */,
    0x654f /* offset 4727 */,
    0x65e2 /* offset 4728 */,
    0x6691 /* offset 4729 */,
    0x6885 /* offset 4730 */,
    0x6d77 /* offset 4731 */,
    0x6e1a /* offset 4732 */,
    0x6f22 /* offset 4733 */,
    0x716e /* offset 4734 */,
    0x722b /* offset 4735 */,
    0x7422 /* offset 4736 */,
    0x7891 /* offset 4737 */,
    0x7949 /* offset 4738 */,
    0x7948 /* offset 4739 */,
    0x7950 /* offset 4740 */,
    0x7956 /* offset 4741 */,
    0x798d /* offset 4742 */,
    0x798e /* offset 4743 */,
    0x7a40 /* offset 4744 */,
    0x7a81 /* offset 4745 */,
    0x7bc0 /* offset 4746 */,
    0x7e09 /* offset 4747 */,
    0x7e41 /* offset 4748 */,
    0x7f72 /* offset 4749 */,
    0x8005 /* offset 4750 */,
    0x81ed /* offset 4751 */,
    0x8279 /* offset 4752 */,
    0x8457 /* offset 4753 */,
    0x8910 /* offset 4754 */,
    0x8996 /* offset 4755 */,
    0x8b01 /* offset 4756 */,
    0x8b39 /* offset 4757 */,
    0x8cd3 /* offset 4758 */,
    0x8d08 /* offset 4759 */,
    0x8fb6 /* offset 4760 */,
    0x96e3 /* offset 4761 */,
    0x97ff /* offset 4762 */,
    0x983b /* offset 4763 */,
    0x6075 /* offset 4764 */,
    0x242ee /* offset 4765 */,
    0x8218 /* offset 4766 */,
    0x4e26 /* offset 4767 */,
    0x51b5 /* offset 4768 */,
    0x5168 /* offset 4769 */,
    0x4f80 /* offset 4770 */,
    0x5145 /* offset 4771 */,
    0x5180 /* offset 4772 */,
    0x52c7 /* offset 4773 */,
    0x52fa /* offset 4774 */,
    0x5555 /* offset 4775 */,
    0x5599 /* offset 4776 */,
    0x55e2 /* offset 4777 */,
    0x58b3 /* offset 4778 */,
    0x5944 /* offset 4779 */,
    0x5954 /* offset 4780 */,
    0x5a62 /* offset 4781 */,
    0x5b28 /* offset 4782 */,
    0x5ed2 /* offset 4783 */,
    0x5ed9 /* offset 4784 */,
    0x5f69 /* offset 4785 */,
    0x5fad /* offset 4786 */,
    0x60d8 /* offset 4787 */,
    0x614e /* offset 4788 */,
    0x6108 /* offset 4789 */,
    0x6160 /* offset 4790 */,
    0x6234 /* offset 4791 */,
    0x63c4 /* offset 4792 */,
    0x641c /* offset 4793 */,
    0x6452 /* offset 4794 */,
    0x6556 /* offset 4795 */,
    0x671b /* offset 4796 */,
    0x6756 /* offset 4797 */,
    0x6edb /* offset 4798 */,
    0x6ecb /* offset 4799 */,
    0x701e /* offset 4800 */,
    0x77a7 /* offset 4801 */,
    0x7235 /* offset 4802 */,
    0x72af /* offset 4803 */,
    0x7471 /* offset 4804 */,
    0x7506 /* offset 4805 */,
    0x753b /* offset 4806 */,
    0x761d /* offset 4807 */,
    0x761f /* offset 4808 */,
    0x76db /* offset 4809 */,
    0x76f4 /* offset 4810 */,
    0x774a /* offset 4811 */,
    0x7740 /* offset 4812 */,
    0x78cc /* offset 4813 */,
    0x7ab1 /* offset 4814 */,
    0x7c7b /* offset 4815 */,
    0x7d5b /* offset 4816 */,
    0x7f3e /* offset 4817 */,
    0x8352 /* offset 4818 */,
    0x83ef /* offset 4819 */,
    0x8779 /* offset 4820 */,
    0x8941 /* offset 4821 */,
    0x8986 /* offset 4822 */,
    0x8abf /* offset 4823 */,
    0x8acb /* offset 4824 */,
    0x8aed /* offset 4825 */,
    0x8b8a /* offset 4826 */,
    0x8f38 /* offset 4827 */,
    0x9072 /* offset 4828 */,
    0x9199 /* offset 4829 */,
    0x9276 /* offset 4830 */,
    0x967c /* offset 4831 */,
    0x97db /* offset 4832 */,
    0x980b /* offset 4833 */,
    0x9b12 /* offset 4834 */,
    0x2284a /* offset 4835 */,
    0x22844 /* offset 4836 */,
    0x233d5 /* offset 4837 */,
    0x3b9d /* offset 4838 */,
    0x4018 /* offset 4839 */,
    0x4039 /* offset 4840 */,
    0x25249 /* offset 4841 */,
    0x25cd0 /* offset 4842 */,
    0x27ed3 /* offset 4843 */,
    0x9f43 /* offset 4844 */,
    0x9f8e /* offset 4845 */,
    0x66,
    0x66 /* offset 4846 */,
    0x66,
    0x69 /* offset 4848 */,
    0x66,
    0x6c /* offset 4850 */,
    0x66,
    0x66,
    0x69 /* offset 4852 */,
    0x66,
    0x66,
    0x6c /* offset 4855 */,
    0x73,
    0x74 /* offset 4858 */,
    0x574,
    0x576 /* offset 4860 */,
    0x574,
    0x565 /* offset 4862 */,
    0x574,
    0x56b /* offset 4864 */,
    0x57e,
    0x576 /* offset 4866 */,
    0x574,
    0x56d /* offset 4868 */,
    0x5d9,
    0x5b4 /* offset 4870 */,
    0x5f2,
    0x5b7 /* offset 4872 */,
    0x5e2 /* offset 4874 */,
    0x5d4 /* offset 4875 */,
    0x5db /* offset 4876 */,
    0x5dc /* offset 4877 */,
    0x5dd /* offset 4878 */,
    0x5e8 /* offset 4879 */,
    0x5ea /* offset 4880 */,
    0x5e9,
    0x5c1 /* offset 4881 */,
    0x5e9,
    0x5c2 /* offset 4883 */,
    0x5e9,
    0x5bc,
    0x5c1 /* offset 4885 */,
    0x5e9,
    0x5bc,
    0x5c2 /* offset 4888 */,
    0x5d0,
    0x5b7 /* offset 4891 */,
    0x5d0,
    0x5b8 /* offset 4893 */,
    0x5d0,
    0x5bc /* offset 4895 */,
    0x5d1,
    0x5bc /* offset 4897 */,
    0x5d2,
    0x5bc /* offset 4899 */,
    0x5d3,
    0x5bc /* offset 4901 */,
    0x5d4,
    0x5bc /* offset 4903 */,
    0x5d5,
    0x5bc /* offset 4905 */,
    0x5d6,
    0x5bc /* offset 4907 */,
    0x5d8,
    0x5bc /* offset 4909 */,
    0x5d9,
    0x5bc /* offset 4911 */,
    0x5da,
    0x5bc /* offset 4913 */,
    0x5db,
    0x5bc /* offset 4915 */,
    0x5dc,
    0x5bc /* offset 4917 */,
    0x5de,
    0x5bc /* offset 4919 */,
    0x5e0,
    0x5bc /* offset 4921 */,
    0x5e1,
    0x5bc /* offset 4923 */,
    0x5e3,
    0x5bc /* offset 4925 */,
    0x5e4,
    0x5bc /* offset 4927 */,
    0x5e6,
    0x5bc /* offset 4929 */,
    0x5e7,
    0x5bc /* offset 4931 */,
    0x5e8,
    0x5bc /* offset 4933 */,
    0x5e9,
    0x5bc /* offset 4935 */,
    0x5ea,
    0x5bc /* offset 4937 */,
    0x5d5,
    0x5b9 /* offset 4939 */,
    0x5d1,
    0x5bf /* offset 4941 */,
    0x5db,
    0x5bf /* offset 4943 */,
    0x5e4,
    0x5bf /* offset 4945 */,
    0x5d0,
    0x5dc /* offset 4947 */,
    0x671 /* offset 4949 */,
    0x67b /* offset 4950 */,
    0x67e /* offset 4951 */,
    0x680 /* offset 4952 */,
    0x67a /* offset 4953 */,
    0x67f /* offset 4954 */,
    0x679 /* offset 4955 */,
    0x6a4 /* offset 4956 */,
    0x6a6 /* offset 4957 */,
    0x684 /* offset 4958 */,
    0x683 /* offset 4959 */,
    0x686 /* offset 4960 */,
    0x687 /* offset 4961 */,
    0x68d /* offset 4962 */,
    0x68c /* offset 4963 */,
    0x68e /* offset 4964 */,
    0x688 /* offset 4965 */,
    0x698 /* offset 4966 */,
    0x691 /* offset 4967 */,
    0x6a9 /* offset 4968 */,
    0x6af /* offset 4969 */,
    0x6b3 /* offset 4970 */,
    0x6b1 /* offset 4971 */,
    0x6ba /* offset 4972 */,
    0x6bb /* offset 4973 */,
    0x6c1 /* offset 4974 */,
    0x6be /* offset 4975 */,
    0x6d2 /* offset 4976 */,
    0x6ad /* offset 4977 */,
    0x6c7 /* offset 4978 */,
    0x6c6 /* offset 4979 */,
    0x6c8 /* offset 4980 */,
    0x6cb /* offset 4981 */,
    0x6c5 /* offset 4982 */,
    0x6c9 /* offset 4983 */,
    0x6d0 /* offset 4984 */,
    0x649 /* offset 4985 */,
    0x64a,
    0x654,
    0x627 /* offset 4986 */,
    0x64a,
    0x654,
    0x6d5 /* offset 4989 */,
    0x64a,
    0x654,
    0x648 /* offset 4992 */,
    0x64a,
    0x654,
    0x6c7 /* offset 4995 */,
    0x64a,
    0x654,
    0x6c6 /* offset 4998 */,
    0x64a,
    0x654,
    0x6c8 /* offset 5001 */,
    0x64a,
    0x654,
    0x6d0 /* offset 5004 */,
    0x64a,
    0x654,
    0x649 /* offset 5007 */,
    0x6cc /* offset 5010 */,
    0x64a,
    0x654,
    0x62c /* offset 5011 */,
    0x64a,
    0x654,
    0x62d /* offset 5014 */,
    0x64a,
    0x654,
    0x645 /* offset 5017 */,
    0x64a,
    0x654,
    0x64a /* offset 5020 */,
    0x628,
    0x62c /* offset 5023 */,
    0x628,
    0x62d /* offset 5025 */,
    0x628,
    0x62e /* offset 5027 */,
    0x628,
    0x645 /* offset 5029 */,
    0x628,
    0x649 /* offset 5031 */,
    0x628,
    0x64a /* offset 5033 */,
    0x62a,
    0x62c /* offset 5035 */,
    0x62a,
    0x62d /* offset 5037 */,
    0x62a,
    0x62e /* offset 5039 */,
    0x62a,
    0x645 /* offset 5041 */,
    0x62a,
    0x649 /* offset 5043 */,
    0x62a,
    0x64a /* offset 5045 */,
    0x62b,
    0x62c /* offset 5047 */,
    0x62b,
    0x645 /* offset 5049 */,
    0x62b,
    0x649 /* offset 5051 */,
    0x62b,
    0x64a /* offset 5053 */,
    0x62c,
    0x62d /* offset 5055 */,
    0x62c,
    0x645 /* offset 5057 */,
    0x62d,
    0x62c /* offset 5059 */,
    0x62d,
    0x645 /* offset 5061 */,
    0x62e,
    0x62c /* offset 5063 */,
    0x62e,
    0x62d /* offset 5065 */,
    0x62e,
    0x645 /* offset 5067 */,
    0x633,
    0x62c /* offset 5069 */,
    0x633,
    0x62d /* offset 5071 */,
    0x633,
    0x62e /* offset 5073 */,
    0x633,
    0x645 /* offset 5075 */,
    0x635,
    0x62d /* offset 5077 */,
    0x635,
    0x645 /* offset 5079 */,
    0x636,
    0x62c /* offset 5081 */,
    0x636,
    0x62d /* offset 5083 */,
    0x636,
    0x62e /* offset 5085 */,
    0x636,
    0x645 /* offset 5087 */,
    0x637,
    0x62d /* offset 5089 */,
    0x637,
    0x645 /* offset 5091 */,
    0x638,
    0x645 /* offset 5093 */,
    0x639,
    0x62c /* offset 5095 */,
    0x639,
    0x645 /* offset 5097 */,
    0x63a,
    0x62c /* offset 5099 */,
    0x63a,
    0x645 /* offset 5101 */,
    0x641,
    0x62c /* offset 5103 */,
    0x641,
    0x62d /* offset 5105 */,
    0x641,
    0x62e /* offset 5107 */,
    0x641,
    0x645 /* offset 5109 */,
    0x641,
    0x649 /* offset 5111 */,
    0x641,
    0x64a /* offset 5113 */,
    0x642,
    0x62d /* offset 5115 */,
    0x642,
    0x645 /* offset 5117 */,
    0x642,
    0x649 /* offset 5119 */,
    0x642,
    0x64a /* offset 5121 */,
    0x643,
    0x627 /* offset 5123 */,
    0x643,
    0x62c /* offset 5125 */,
    0x643,
    0x62d /* offset 5127 */,
    0x643,
    0x62e /* offset 5129 */,
    0x643,
    0x644 /* offset 5131 */,
    0x643,
    0x645 /* offset 5133 */,
    0x643,
    0x649 /* offset 5135 */,
    0x643,
    0x64a /* offset 5137 */,
    0x644,
    0x62c /* offset 5139 */,
    0x644,
    0x62d /* offset 5141 */,
    0x644,
    0x62e /* offset 5143 */,
    0x644,
    0x645 /* offset 5145 */,
    0x644,
    0x649 /* offset 5147 */,
    0x644,
    0x64a /* offset 5149 */,
    0x645,
    0x62c /* offset 5151 */,
    0x645,
    0x62d /* offset 5153 */,
    0x645,
    0x62e /* offset 5155 */,
    0x645,
    0x645 /* offset 5157 */,
    0x645,
    0x649 /* offset 5159 */,
    0x645,
    0x64a /* offset 5161 */,
    0x646,
    0x62c /* offset 5163 */,
    0x646,
    0x62d /* offset 5165 */,
    0x646,
    0x62e /* offset 5167 */,
    0x646,
    0x645 /* offset 5169 */,
    0x646,
    0x649 /* offset 5171 */,
    0x646,
    0x64a /* offset 5173 */,
    0x647,
    0x62c /* offset 5175 */,
    0x647,
    0x645 /* offset 5177 */,
    0x647,
    0x649 /* offset 5179 */,
    0x647,
    0x64a /* offset 5181 */,
    0x64a,
    0x62c /* offset 5183 */,
    0x64a,
    0x62d /* offset 5185 */,
    0x64a,
    0x62e /* offset 5187 */,
    0x64a,
    0x645 /* offset 5189 */,
    0x64a,
    0x649 /* offset 5191 */,
    0x64a,
    0x64a /* offset 5193 */,
    0x630,
    0x670 /* offset 5195 */,
    0x631,
    0x670 /* offset 5197 */,
    0x649,
    0x670 /* offset 5199 */,
    0x20,
    0x64c,
    0x651 /* offset 5201 */,
    0x20,
    0x64d,
    0x651 /* offset 5204 */,
    0x20,
    0x64e,
    0x651 /* offset 5207 */,
    0x20,
    0x64f,
    0x651 /* offset 5210 */,
    0x20,
    0x650,
    0x651 /* offset 5213 */,
    0x20,
    0x651,
    0x670 /* offset 5216 */,
    0x64a,
    0x654,
    0x631 /* offset 5219 */,
    0x64a,
    0x654,
    0x632 /* offset 5222 */,
    0x64a,
    0x654,
    0x646 /* offset 5225 */,
    0x628,
    0x631 /* offset 5228 */,
    0x628,
    0x632 /* offset 5230 */,
    0x628,
    0x646 /* offset 5232 */,
    0x62a,
    0x631 /* offset 5234 */,
    0x62a,
    0x632 /* offset 5236 */,
    0x62a,
    0x646 /* offset 5238 */,
    0x62b,
    0x631 /* offset 5240 */,
    0x62b,
    0x632 /* offset 5242 */,
    0x62b,
    0x646 /* offset 5244 */,
    0x645,
    0x627 /* offset 5246 */,
    0x646,
    0x631 /* offset 5248 */,
    0x646,
    0x632 /* offset 5250 */,
    0x646,
    0x646 /* offset 5252 */,
    0x64a,
    0x631 /* offset 5254 */,
    0x64a,
    0x632 /* offset 5256 */,
    0x64a,
    0x646 /* offset 5258 */,
    0x64a,
    0x654,
    0x62e /* offset 5260 */,
    0x64a,
    0x654,
    0x647 /* offset 5263 */,
    0x628,
    0x647 /* offset 5266 */,
    0x62a,
    0x647 /* offset 5268 */,
    0x635,
    0x62e /* offset 5270 */,
    0x644,
    0x647 /* offset 5272 */,
    0x646,
    0x647 /* offset 5274 */,
    0x647,
    0x670 /* offset 5276 */,
    0x64a,
    0x647 /* offset 5278 */,
    0x62b,
    0x647 /* offset 5280 */,
    0x633,
    0x647 /* offset 5282 */,
    0x634,
    0x645 /* offset 5284 */,
    0x634,
    0x647 /* offset 5286 */,
    0x640,
    0x64e,
    0x651 /* offset 5288 */,
    0x640,
    0x64f,
    0x651 /* offset 5291 */,
    0x640,
    0x650,
    0x651 /* offset 5294 */,
    0x637,
    0x649 /* offset 5297 */,
    0x637,
    0x64a /* offset 5299 */,
    0x639,
    0x649 /* offset 5301 */,
    0x639,
    0x64a /* offset 5303 */,
    0x63a,
    0x649 /* offset 5305 */,
    0x63a,
    0x64a /* offset 5307 */,
    0x633,
    0x649 /* offset 5309 */,
    0x633,
    0x64a /* offset 5311 */,
    0x634,
    0x649 /* offset 5313 */,
    0x634,
    0x64a /* offset 5315 */,
    0x62d,
    0x649 /* offset 5317 */,
    0x62d,
    0x64a /* offset 5319 */,
    0x62c,
    0x649 /* offset 5321 */,
    0x62c,
    0x64a /* offset 5323 */,
    0x62e,
    0x649 /* offset 5325 */,
    0x62e,
    0x64a /* offset 5327 */,
    0x635,
    0x649 /* offset 5329 */,
    0x635,
    0x64a /* offset 5331 */,
    0x636,
    0x649 /* offset 5333 */,
    0x636,
    0x64a /* offset 5335 */,
    0x634,
    0x62c /* offset 5337 */,
    0x634,
    0x62d /* offset 5339 */,
    0x634,
    0x62e /* offset 5341 */,
    0x634,
    0x631 /* offset 5343 */,
    0x633,
    0x631 /* offset 5345 */,
    0x635,
    0x631 /* offset 5347 */,
    0x636,
    0x631 /* offset 5349 */,
    0x627,
    0x64b /* offset 5351 */,
    0x62a,
    0x62c,
    0x645 /* offset 5353 */,
    0x62a,
    0x62d,
    0x62c /* offset 5356 */,
    0x62a,
    0x62d,
    0x645 /* offset 5359 */,
    0x62a,
    0x62e,
    0x645 /* offset 5362 */,
    0x62a,
    0x645,
    0x62c /* offset 5365 */,
    0x62a,
    0x645,
    0x62d /* offset 5368 */,
    0x62a,
    0x645,
    0x62e /* offset 5371 */,
    0x62c,
    0x645,
    0x62d /* offset 5374 */,
    0x62d,
    0x645,
    0x64a /* offset 5377 */,
    0x62d,
    0x645,
    0x649 /* offset 5380 */,
    0x633,
    0x62d,
    0x62c /* offset 5383 */,
    0x633,
    0x62c,
    0x62d /* offset 5386 */,
    0x633,
    0x62c,
    0x649 /* offset 5389 */,
    0x633,
    0x645,
    0x62d /* offset 5392 */,
    0x633,
    0x645,
    0x62c /* offset 5395 */,
    0x633,
    0x645,
    0x645 /* offset 5398 */,
    0x635,
    0x62d,
    0x62d /* offset 5401 */,
    0x635,
    0x645,
    0x645 /* offset 5404 */,
    0x634,
    0x62d,
    0x645 /* offset 5407 */,
    0x634,
    0x62c,
    0x64a /* offset 5410 */,
    0x634,
    0x645,
    0x62e /* offset 5413 */,
    0x634,
    0x645,
    0x645 /* offset 5416 */,
    0x636,
    0x62d,
    0x649 /* offset 5419 */,
    0x636,
    0x62e,
    0x645 /* offset 5422 */,
    0x637,
    0x645,
    0x62d /* offset 5425 */,
    0x637,
    0x645,
    0x645 /* offset 5428 */,
    0x637,
    0x645,
    0x64a /* offset 5431 */,
    0x639,
    0x62c,
    0x645 /* offset 5434 */,
    0x639,
    0x645,
    0x645 /* offset 5437 */,
    0x639,
    0x645,
    0x649 /* offset 5440 */,
    0x63a,
    0x645,
    0x645 /* offset 5443 */,
    0x63a,
    0x645,
    0x64a /* offset 5446 */,
    0x63a,
    0x645,
    0x649 /* offset 5449 */,
    0x641,
    0x62e,
    0x645 /* offset 5452 */,
    0x642,
    0x645,
    0x62d /* offset 5455 */,
    0x642,
    0x645,
    0x645 /* offset 5458 */,
    0x644,
    0x62d,
    0x645 /* offset 5461 */,
    0x644,
    0x62d,
    0x64a /* offset 5464 */,
    0x644,
    0x62d,
    0x649 /* offset 5467 */,
    0x644,
    0x62c,
    0x62c /* offset 5470 */,
    0x644,
    0x62e,
    0x645 /* offset 5473 */,
    0x644,
    0x645,
    0x62d /* offset 5476 */,
    0x645,
    0x62d,
    0x62c /* offset 5479 */,
    0x645,
    0x62d,
    0x645 /* offset 5482 */,
    0x645,
    0x62d,
    0x64a /* offset 5485 */,
    0x645,
    0x62c,
    0x62d /* offset 5488 */,
    0x645,
    0x62c,
    0x645 /* offset 5491 */,
    0x645,
    0x62e,
    0x62c /* offset 5494 */,
    0x645,
    0x62e,
    0x645 /* offset 5497 */,
    0x645,
    0x62c,
    0x62e /* offset 5500 */,
    0x647,
    0x645,
    0x62c /* offset 5503 */,
    0x647,
    0x645,
    0x645 /* offset 5506 */,
    0x646,
    0x62d,
    0x645 /* offset 5509 */,
    0x646,
    0x62d,
    0x649 /* offset 5512 */,
    0x646,
    0x62c,
    0x645 /* offset 5515 */,
    0x646,
    0x62c,
    0x649 /* offset 5518 */,
    0x646,
    0x645,
    0x64a /* offset 5521 */,
    0x646,
    0x645,
    0x649 /* offset 5524 */,
    0x64a,
    0x645,
    0x645 /* offset 5527 */,
    0x628,
    0x62e,
    0x64a /* offset 5530 */,
    0x62a,
    0x62c,
    0x64a /* offset 5533 */,
    0x62a,
    0x62c,
    0x649 /* offset 5536 */,
    0x62a,
    0x62e,
    0x64a /* offset 5539 */,
    0x62a,
    0x62e,
    0x649 /* offset 5542 */,
    0x62a,
    0x645,
    0x64a /* offset 5545 */,
    0x62a,
    0x645,
    0x649 /* offset 5548 */,
    0x62c,
    0x645,
    0x64a /* offset 5551 */,
    0x62c,
    0x62d,
    0x649 /* offset 5554 */,
    0x62c,
    0x645,
    0x649 /* offset 5557 */,
    0x633,
    0x62e,
    0x649 /* offset 5560 */,
    0x635,
    0x62d,
    0x64a /* offset 5563 */,
    0x634,
    0x62d,
    0x64a /* offset 5566 */,
    0x636,
    0x62d,
    0x64a /* offset 5569 */,
    0x644,
    0x62c,
    0x64a /* offset 5572 */,
    0x644,
    0x645,
    0x64a /* offset 5575 */,
    0x64a,
    0x62d,
    0x64a /* offset 5578 */,
    0x64a,
    0x62c,
    0x64a /* offset 5581 */,
    0x64a,
    0x645,
    0x64a /* offset 5584 */,
    0x645,
    0x645,
    0x64a /* offset 5587 */,
    0x642,
    0x645,
    0x64a /* offset 5590 */,
    0x646,
    0x62d,
    0x64a /* offset 5593 */,
    0x639,
    0x645,
    0x64a /* offset 5596 */,
    0x643,
    0x645,
    0x64a /* offset 5599 */,
    0x646,
    0x62c,
    0x62d /* offset 5602 */,
    0x645,
    0x62e,
    0x64a /* offset 5605 */,
    0x644,
    0x62c,
    0x645 /* offset 5608 */,
    0x643,
    0x645,
    0x645 /* offset 5611 */,
    0x62c,
    0x62d,
    0x64a /* offset 5614 */,
    0x62d,
    0x62c,
    0x64a /* offset 5617 */,
    0x645,
    0x62c,
    0x64a /* offset 5620 */,
    0x641,
    0x645,
    0x64a /* offset 5623 */,
    0x628,
    0x62d,
    0x64a /* offset 5626 */,
    0x633,
    0x62e,
    0x64a /* offset 5629 */,
    0x646,
    0x62c,
    0x64a /* offset 5632 */,
    0x635,
    0x644,
    0x6d2 /* offset 5635 */,
    0x642,
    0x644,
    0x6d2 /* offset 5638 */,
    0x627,
    0x644,
    0x644,
    0x647 /* offset 5641 */,
    0x627,
    0x643,
    0x628,
    0x631 /* offset 5645 */,
    0x645,
    0x62d,
    0x645,
    0x62f /* offset 5649 */,
    0x635,
    0x644,
    0x639,
    0x645 /* offset 5653 */,
    0x631,
    0x633,
    0x648,
    0x644 /* offset 5657 */,
    0x639,
    0x644,
    0x64a,
    0x647 /* offset 5661 */,
    0x648,
    0x633,
    0x644,
    0x645 /* offset 5665 */,
    0x635,
    0x644,
    0x649 /* offset 5669 */,
    0x635,
    0x644,
    0x649,
    0x20,
    0x627,
    0x644,
    0x644,
    0x647,
    0x20,
    0x639,
    0x644,
    0x64a,
    0x647,
    0x20,
    0x648,
    0x633,
    0x644,
    0x645 /* offset 5672 */,
    0x62c,
    0x644,
    0x20,
    0x62c,
    0x644,
    0x627,
    0x644,
    0x647 /* offset 5690 */,
    0x631,
    0x6cc,
    0x627,
    0x644 /* offset 5698 */,
    0x2c /* offset 5702 */,
    0x3001 /* offset 5703 */,
    0x3002 /* offset 5704 */,
    0x3a /* offset 5705 */,
    0x21 /* offset 5706 */,
    0x3f /* offset 5707 */,
    0x3016 /* offset 5708 */,
    0x3017 /* offset 5709 */,
    0x2014 /* offset 5710 */,
    0x2013 /* offset 5711 */,
    0x5f /* offset 5712 */,
    0x7b /* offset 5713 */,
    0x7d /* offset 5714 */,
    0x3014 /* offset 5715 */,
    0x3015 /* offset 5716 */,
    0x3010 /* offset 5717 */,
    0x3011 /* offset 5718 */,
    0x300a /* offset 5719 */,
    0x300b /* offset 5720 */,
    0x300c /* offset 5721 */,
    0x300d /* offset 5722 */,
    0x300e /* offset 5723 */,
    0x300f /* offset 5724 */,
    0x5b /* offset 5725 */,
    0x5d /* offset 5726 */,
    0x23 /* offset 5727 */,
    0x26 /* offset 5728 */,
    0x2a /* offset 5729 */,
    0x2d /* offset 5730 */,
    0x3c /* offset 5731 */,
    0x3e /* offset 5732 */,
    0x5c /* offset 5733 */,
    0x24 /* offset 5734 */,
    0x25 /* offset 5735 */,
    0x40 /* offset 5736 */,
    0x20,
    0x64b /* offset 5737 */,
    0x640,
    0x64b /* offset 5739 */,
    0x20,
    0x64c /* offset 5741 */,
    0x20,
    0x64d /* offset 5743 */,
    0x20,
    0x64e /* offset 5745 */,
    0x640,
    0x64e /* offset 5747 */,
    0x20,
    0x64f /* offset 5749 */,
    0x640,
    0x64f /* offset 5751 */,
    0x20,
    0x650 /* offset 5753 */,
    0x640,
    0x650 /* offset 5755 */,
    0x20,
    0x651 /* offset 5757 */,
    0x640,
    0x651 /* offset 5759 */,
    0x20,
    0x652 /* offset 5761 */,
    0x640,
    0x652 /* offset 5763 */,
    0x621 /* offset 5765 */,
    0x627 /* offset 5766 */,
    0x628 /* offset 5767 */,
    0x629 /* offset 5768 */,
    0x62a /* offset 5769 */,
    0x62b /* offset 5770 */,
    0x62c /* offset 5771 */,
    0x62d /* offset 5772 */,
    0x62e /* offset 5773 */,
    0x62f /* offset 5774 */,
    0x630 /* offset 5775 */,
    0x631 /* offset 5776 */,
    0x632 /* offset 5777 */,
    0x633 /* offset 5778 */,
    0x634 /* offset 5779 */,
    0x635 /* offset 5780 */,
    0x636 /* offset 5781 */,
    0x637 /* offset 5782 */,
    0x638 /* offset 5783 */,
    0x639 /* offset 5784 */,
    0x63a /* offset 5785 */,
    0x641 /* offset 5786 */,
    0x642 /* offset 5787 */,
    0x643 /* offset 5788 */,
    0x644 /* offset 5789 */,
    0x645 /* offset 5790 */,
    0x646 /* offset 5791 */,
    0x647 /* offset 5792 */,
    0x648 /* offset 5793 */,
    0x64a /* offset 5794 */,
    0x644,
    0x627,
    0x653 /* offset 5795 */,
    0x644,
    0x627,
    0x654 /* offset 5798 */,
    0x644,
    0x627,
    0x655 /* offset 5801 */,
    0x644,
    0x627 /* offset 5804 */,
    0x22 /* offset 5806 */,
    0x27 /* offset 5807 */,
    0x2f /* offset 5808 */,
    0x5e /* offset 5809 */,
    0x7c /* offset 5810 */,
    0x7e /* offset 5811 */,
    0x2985 /* offset 5812 */,
    0x2986 /* offset 5813 */,
    0x30fb /* offset 5814 */,
    0x30a1 /* offset 5815 */,
    0x30a3 /* offset 5816 */,
    0x30a5 /* offset 5817 */,
    0x30a7 /* offset 5818 */,
    0x30a9 /* offset 5819 */,
    0x30e3 /* offset 5820 */,
    0x30e5 /* offset 5821 */,
    0x30e7 /* offset 5822 */,
    0x30c3 /* offset 5823 */,
    0x30fc /* offset 5824 */,
    0x30f3 /* offset 5825 */,
    0x3099 /* offset 5826 */,
    0x309a /* offset 5827 */,
    0xa2 /* offset 5828 */,
    0xa3 /* offset 5829 */,
    0xac /* offset 5830 */,
    0xa6 /* offset 5831 */,
    0xa5 /* offset 5832 */,
    0x20a9 /* offset 5833 */,
    0x2502 /* offset 5834 */,
    0x2190 /* offset 5835 */,
    0x2191 /* offset 5836 */,
    0x2192 /* offset 5837 */,
    0x2193 /* offset 5838 */,
    0x25a0 /* offset 5839 */,
    0x25cb /* offset 5840 */,
    0x11099,
    0x110ba /* offset 5841 */,
    0x1109b,
    0x110ba /* offset 5843 */,
    0x110a5,
    0x110ba /* offset 5845 */,
    0x11131,
    0x11127 /* offset 5847 */,
    0x11132,
    0x11127 /* offset 5849 */,
    0x11347,
    0x1133e /* offset 5851 */,
    0x11347,
    0x11357 /* offset 5853 */,
    0x114b9,
    0x114ba /* offset 5855 */,
    0x114b9,
    0x114b0 /* offset 5857 */,
    0x114b9,
    0x114bd /* offset 5859 */,
    0x115b8,
    0x115af /* offset 5861 */,
    0x115b9,
    0x115af /* offset 5863 */,
    0x1d157,
    0x1d165 /* offset 5865 */,
    0x1d158,
    0x1d165 /* offset 5867 */,
    0x1d158,
    0x1d165,
    0x1d16e /* offset 5869 */,
    0x1d158,
    0x1d165,
    0x1d16f /* offset 5872 */,
    0x1d158,
    0x1d165,
    0x1d170 /* offset 5875 */,
    0x1d158,
    0x1d165,
    0x1d171 /* offset 5878 */,
    0x1d158,
    0x1d165,
    0x1d172 /* offset 5881 */,
    0x1d1b9,
    0x1d165 /* offset 5884 */,
    0x1d1ba,
    0x1d165 /* offset 5886 */,
    0x1d1b9,
    0x1d165,
    0x1d16e /* offset 5888 */,
    0x1d1ba,
    0x1d165,
    0x1d16e /* offset 5891 */,
    0x1d1b9,
    0x1d165,
    0x1d16f /* offset 5894 */,
    0x1d1ba,
    0x1d165,
    0x1d16f /* offset 5897 */,
    0x131 /* offset 5900 */,
    0x237 /* offset 5901 */,
    0x391 /* offset 5902 */,
    0x392 /* offset 5903 */,
    0x394 /* offset 5904 */,
    0x395 /* offset 5905 */,
    0x396 /* offset 5906 */,
    0x397 /* offset 5907 */,
    0x399 /* offset 5908 */,
    0x39a /* offset 5909 */,
    0x39b /* offset 5910 */,
    0x39c /* offset 5911 */,
    0x39d /* offset 5912 */,
    0x39e /* offset 5913 */,
    0x39f /* offset 5914 */,
    0x3a1 /* offset 5915 */,
    0x3a4 /* offset 5916 */,
    0x3a6 /* offset 5917 */,
    0x3a7 /* offset 5918 */,
    0x3a8 /* offset 5919 */,
    0x2207 /* offset 5920 */,
    0x3b1 /* offset 5921 */,
    0x3b6 /* offset 5922 */,
    0x3b7 /* offset 5923 */,
    0x3bb /* offset 5924 */,
    0x3bd /* offset 5925 */,
    0x3be /* offset 5926 */,
    0x3bf /* offset 5927 */,
    0x3c3 /* offset 5928 */,
    0x3c4 /* offset 5929 */,
    0x3c5 /* offset 5930 */,
    0x3c8 /* offset 5931 */,
    0x3c9 /* offset 5932 */,
    0x2202 /* offset 5933 */,
    0x3dc /* offset 5934 */,
    0x3dd /* offset 5935 */,
    0x66e /* offset 5936 */,
    0x6a1 /* offset 5937 */,
    0x66f /* offset 5938 */,
    0x30,
    0x2e /* offset 5939 */,
    0x30,
    0x2c /* offset 5941 */,
    0x31,
    0x2c /* offset 5943 */,
    0x32,
    0x2c /* offset 5945 */,
    0x33,
    0x2c /* offset 5947 */,
    0x34,
    0x2c /* offset 5949 */,
    0x35,
    0x2c /* offset 5951 */,
    0x36,
    0x2c /* offset 5953 */,
    0x37,
    0x2c /* offset 5955 */,
    0x38,
    0x2c /* offset 5957 */,
    0x39,
    0x2c /* offset 5959 */,
    0x28,
    0x41,
    0x29 /* offset 5961 */,
    0x28,
    0x42,
    0x29 /* offset 5964 */,
    0x28,
    0x43,
    0x29 /* offset 5967 */,
    0x28,
    0x44,
    0x29 /* offset 5970 */,
    0x28,
    0x45,
    0x29 /* offset 5973 */,
    0x28,
    0x46,
    0x29 /* offset 5976 */,
    0x28,
    0x47,
    0x29 /* offset 5979 */,
    0x28,
    0x48,
    0x29 /* offset 5982 */,
    0x28,
    0x49,
    0x29 /* offset 5985 */,
    0x28,
    0x4a,
    0x29 /* offset 5988 */,
    0x28,
    0x4b,
    0x29 /* offset 5991 */,
    0x28,
    0x4c,
    0x29 /* offset 5994 */,
    0x28,
    0x4d,
    0x29 /* offset 5997 */,
    0x28,
    0x4e,
    0x29 /* offset 6000 */,
    0x28,
    0x4f,
    0x29 /* offset 6003 */,
    0x28,
    0x50,
    0x29 /* offset 6006 */,
    0x28,
    0x51,
    0x29 /* offset 6009 */,
    0x28,
    0x52,
    0x29 /* offset 6012 */,
    0x28,
    0x53,
    0x29 /* offset 6015 */,
    0x28,
    0x54,
    0x29 /* offset 6018 */,
    0x28,
    0x55,
    0x29 /* offset 6021 */,
    0x28,
    0x56,
    0x29 /* offset 6024 */,
    0x28,
    0x57,
    0x29 /* offset 6027 */,
    0x28,
    0x58,
    0x29 /* offset 6030 */,
    0x28,
    0x59,
    0x29 /* offset 6033 */,
    0x28,
    0x5a,
    0x29 /* offset 6036 */,
    0x3014,
    0x53,
    0x3015 /* offset 6039 */,
    0x43,
    0x44 /* offset 6042 */,
    0x57,
    0x5a /* offset 6044 */,
    0x48,
    0x56 /* offset 6046 */,
    0x53,
    0x44 /* offset 6048 */,
    0x53,
    0x53 /* offset 6050 */,
    0x50,
    0x50,
    0x56 /* offset 6052 */,
    0x57,
    0x43 /* offset 6055 */,
    0x4d,
    0x43 /* offset 6057 */,
    0x4d,
    0x44 /* offset 6059 */,
    0x44,
    0x4a /* offset 6061 */,
    0x307b,
    0x304b /* offset 6063 */,
    0x30b3,
    0x30b3 /* offset 6065 */,
    0x5b57 /* offset 6067 */,
    0x53cc /* offset 6068 */,
    0x591a /* offset 6069 */,
    0x89e3 /* offset 6070 */,
    0x4ea4 /* offset 6071 */,
    0x6620 /* offset 6072 */,
    0x7121 /* offset 6073 */,
    0x524d /* offset 6074 */,
    0x5f8c /* offset 6075 */,
    0x518d /* offset 6076 */,
    0x65b0 /* offset 6077 */,
    0x521d /* offset 6078 */,
    0x7d42 /* offset 6079 */,
    0x8ca9 /* offset 6080 */,
    0x58f0 /* offset 6081 */,
    0x5439 /* offset 6082 */,
    0x6f14 /* offset 6083 */,
    0x6295 /* offset 6084 */,
    0x6355 /* offset 6085 */,
    0x904a /* offset 6086 */,
    0x6307 /* offset 6087 */,
    0x6253 /* offset 6088 */,
    0x7981 /* offset 6089 */,
    0x7a7a /* offset 6090 */,
    0x5408 /* offset 6091 */,
    0x6e80 /* offset 6092 */,
    0x7533 /* offset 6093 */,
    0x5272 /* offset 6094 */,
    0x55b6 /* offset 6095 */,
    0x914d /* offset 6096 */,
    0x3014,
    0x672c,
    0x3015 /* offset 6097 */,
    0x3014,
    0x4e09,
    0x3015 /* offset 6100 */,
    0x3014,
    0x4e8c,
    0x3015 /* offset 6103 */,
    0x3014,
    0x5b89,
    0x3015 /* offset 6106 */,
    0x3014,
    0x70b9,
    0x3015 /* offset 6109 */,
    0x3014,
    0x6253,
    0x3015 /* offset 6112 */,
    0x3014,
    0x76d7,
    0x3015 /* offset 6115 */,
    0x3014,
    0x52dd,
    0x3015 /* offset 6118 */,
    0x3014,
    0x6557,
    0x3015 /* offset 6121 */,
    0x5f97 /* offset 6124 */,
    0x53ef /* offset 6125 */,
    0x4e3d /* offset 6126 */,
    0x4e38 /* offset 6127 */,
    0x4e41 /* offset 6128 */,
    0x20122 /* offset 6129 */,
    0x4f60 /* offset 6130 */,
    0x4fbb /* offset 6131 */,
    0x5002 /* offset 6132 */,
    0x507a /* offset 6133 */,
    0x5099 /* offset 6134 */,
    0x50cf /* offset 6135 */,
    0x349e /* offset 6136 */,
    0x2063a /* offset 6137 */,
    0x5154 /* offset 6138 */,
    0x5164 /* offset 6139 */,
    0x5177 /* offset 6140 */,
    0x2051c /* offset 6141 */,
    0x34b9 /* offset 6142 */,
    0x5167 /* offset 6143 */,
    0x2054b /* offset 6144 */,
    0x5197 /* offset 6145 */,
    0x51a4 /* offset 6146 */,
    0x4ecc /* offset 6147 */,
    0x51ac /* offset 6148 */,
    0x291df /* offset 6149 */,
    0x5203 /* offset 6150 */,
    0x34df /* offset 6151 */,
    0x523b /* offset 6152 */,
    0x5246 /* offset 6153 */,
    0x5277 /* offset 6154 */,
    0x3515 /* offset 6155 */,
    0x5305 /* offset 6156 */,
    0x5306 /* offset 6157 */,
    0x5349 /* offset 6158 */,
    0x535a /* offset 6159 */,
    0x5373 /* offset 6160 */,
    0x537d /* offset 6161 */,
    0x537f /* offset 6162 */,
    0x20a2c /* offset 6163 */,
    0x7070 /* offset 6164 */,
    0x53ca /* offset 6165 */,
    0x53df /* offset 6166 */,
    0x20b63 /* offset 6167 */,
    0x53eb /* offset 6168 */,
    0x53f1 /* offset 6169 */,
    0x5406 /* offset 6170 */,
    0x549e /* offset 6171 */,
    0x5438 /* offset 6172 */,
    0x5448 /* offset 6173 */,
    0x5468 /* offset 6174 */,
    0x54a2 /* offset 6175 */,
    0x54f6 /* offset 6176 */,
    0x5510 /* offset 6177 */,
    0x5553 /* offset 6178 */,
    0x5563 /* offset 6179 */,
    0x5584 /* offset 6180 */,
    0x55ab /* offset 6181 */,
    0x55b3 /* offset 6182 */,
    0x55c2 /* offset 6183 */,
    0x5716 /* offset 6184 */,
    0x5717 /* offset 6185 */,
    0x5651 /* offset 6186 */,
    0x5674 /* offset 6187 */,
    0x58ee /* offset 6188 */,
    0x57ce /* offset 6189 */,
    0x57f4 /* offset 6190 */,
    0x580d /* offset 6191 */,
    0x578b /* offset 6192 */,
    0x5832 /* offset 6193 */,
    0x5831 /* offset 6194 */,
    0x58ac /* offset 6195 */,
    0x214e4 /* offset 6196 */,
    0x58f2 /* offset 6197 */,
    0x58f7 /* offset 6198 */,
    0x5906 /* offset 6199 */,
    0x5922 /* offset 6200 */,
    0x5962 /* offset 6201 */,
    0x216a8 /* offset 6202 */,
    0x216ea /* offset 6203 */,
    0x59ec /* offset 6204 */,
    0x5a1b /* offset 6205 */,
    0x5a27 /* offset 6206 */,
    0x59d8 /* offset 6207 */,
    0x5a66 /* offset 6208 */,
    0x36ee /* offset 6209 */,
    0x36fc /* offset 6210 */,
    0x5b08 /* offset 6211 */,
    0x5b3e /* offset 6212 */,
    0x219c8 /* offset 6213 */,
    0x5bc3 /* offset 6214 */,
    0x5bd8 /* offset 6215 */,
    0x5bf3 /* offset 6216 */,
    0x21b18 /* offset 6217 */,
    0x5bff /* offset 6218 */,
    0x5c06 /* offset 6219 */,
    0x5f53 /* offset 6220 */,
    0x3781 /* offset 6221 */,
    0x5c60 /* offset 6222 */,
    0x5cc0 /* offset 6223 */,
    0x5c8d /* offset 6224 */,
    0x21de4 /* offset 6225 */,
    0x5d43 /* offset 6226 */,
    0x21de6 /* offset 6227 */,
    0x5d6e /* offset 6228 */,
    0x5d6b /* offset 6229 */,
    0x5d7c /* offset 6230 */,
    0x5de1 /* offset 6231 */,
    0x5de2 /* offset 6232 */,
    0x382f /* offset 6233 */,
    0x5dfd /* offset 6234 */,
    0x5e28 /* offset 6235 */,
    0x5e3d /* offset 6236 */,
    0x5e69 /* offset 6237 */,
    0x3862 /* offset 6238 */,
    0x22183 /* offset 6239 */,
    0x387c /* offset 6240 */,
    0x5eb0 /* offset 6241 */,
    0x5eb3 /* offset 6242 */,
    0x5eb6 /* offset 6243 */,
    0x2a392 /* offset 6244 */,
    0x22331 /* offset 6245 */,
    0x8201 /* offset 6246 */,
    0x5f22 /* offset 6247 */,
    0x38c7 /* offset 6248 */,
    0x232b8 /* offset 6249 */,
    0x261da /* offset 6250 */,
    0x5f62 /* offset 6251 */,
    0x5f6b /* offset 6252 */,
    0x38e3 /* offset 6253 */,
    0x5f9a /* offset 6254 */,
    0x5fcd /* offset 6255 */,
    0x5fd7 /* offset 6256 */,
    0x5ff9 /* offset 6257 */,
    0x6081 /* offset 6258 */,
    0x393a /* offset 6259 */,
    0x391c /* offset 6260 */,
    0x226d4 /* offset 6261 */,
    0x60c7 /* offset 6262 */,
    0x6148 /* offset 6263 */,
    0x614c /* offset 6264 */,
    0x617a /* offset 6265 */,
    0x61b2 /* offset 6266 */,
    0x61a4 /* offset 6267 */,
    0x61af /* offset 6268 */,
    0x61de /* offset 6269 */,
    0x6210 /* offset 6270 */,
    0x621b /* offset 6271 */,
    0x625d /* offset 6272 */,
    0x62b1 /* offset 6273 */,
    0x62d4 /* offset 6274 */,
    0x6350 /* offset 6275 */,
    0x22b0c /* offset 6276 */,
    0x633d /* offset 6277 */,
    0x62fc /* offset 6278 */,
    0x6368 /* offset 6279 */,
    0x6383 /* offset 6280 */,
    0x63e4 /* offset 6281 */,
    0x22bf1 /* offset 6282 */,
    0x6422 /* offset 6283 */,
    0x63c5 /* offset 6284 */,
    0x63a9 /* offset 6285 */,
    0x3a2e /* offset 6286 */,
    0x6469 /* offset 6287 */,
    0x647e /* offset 6288 */,
    0x649d /* offset 6289 */,
    0x6477 /* offset 6290 */,
    0x3a6c /* offset 6291 */,
    0x656c /* offset 6292 */,
    0x2300a /* offset 6293 */,
    0x65e3 /* offset 6294 */,
    0x66f8 /* offset 6295 */,
    0x6649 /* offset 6296 */,
    0x3b19 /* offset 6297 */,
    0x3b08 /* offset 6298 */,
    0x3ae4 /* offset 6299 */,
    0x5192 /* offset 6300 */,
    0x5195 /* offset 6301 */,
    0x6700 /* offset 6302 */,
    0x669c /* offset 6303 */,
    0x80ad /* offset 6304 */,
    0x43d9 /* offset 6305 */,
    0x6721 /* offset 6306 */,
    0x675e /* offset 6307 */,
    0x6753 /* offset 6308 */,
    0x233c3 /* offset 6309 */,
    0x3b49 /* offset 6310 */,
    0x67fa /* offset 6311 */,
    0x6785 /* offset 6312 */,
    0x6852 /* offset 6313 */,
    0x2346d /* offset 6314 */,
    0x688e /* offset 6315 */,
    0x681f /* offset 6316 */,
    0x6914 /* offset 6317 */,
    0x6942 /* offset 6318 */,
    0x69a3 /* offset 6319 */,
    0x69ea /* offset 6320 */,
    0x6aa8 /* offset 6321 */,
    0x236a3 /* offset 6322 */,
    0x6adb /* offset 6323 */,
    0x3c18 /* offset 6324 */,
    0x6b21 /* offset 6325 */,
    0x238a7 /* offset 6326 */,
    0x6b54 /* offset 6327 */,
    0x3c4e /* offset 6328 */,
    0x6b72 /* offset 6329 */,
    0x6b9f /* offset 6330 */,
    0x6bbb /* offset 6331 */,
    0x23a8d /* offset 6332 */,
    0x21d0b /* offset 6333 */,
    0x23afa /* offset 6334 */,
    0x6c4e /* offset 6335 */,
    0x23cbc /* offset 6336 */,
    0x6cbf /* offset 6337 */,
    0x6ccd /* offset 6338 */,
    0x6c67 /* offset 6339 */,
    0x6d16 /* offset 6340 */,
    0x6d3e /* offset 6341 */,
    0x6d69 /* offset 6342 */,
    0x6d78 /* offset 6343 */,
    0x6d85 /* offset 6344 */,
    0x23d1e /* offset 6345 */,
    0x6d34 /* offset 6346 */,
    0x6e2f /* offset 6347 */,
    0x6e6e /* offset 6348 */,
    0x3d33 /* offset 6349 */,
    0x6ec7 /* offset 6350 */,
    0x23ed1 /* offset 6351 */,
    0x6df9 /* offset 6352 */,
    0x6f6e /* offset 6353 */,
    0x23f5e /* offset 6354 */,
    0x23f8e /* offset 6355 */,
    0x6fc6 /* offset 6356 */,
    0x7039 /* offset 6357 */,
    0x701b /* offset 6358 */,
    0x3d96 /* offset 6359 */,
    0x704a /* offset 6360 */,
    0x707d /* offset 6361 */,
    0x7077 /* offset 6362 */,
    0x70ad /* offset 6363 */,
    0x20525 /* offset 6364 */,
    0x7145 /* offset 6365 */,
    0x24263 /* offset 6366 */,
    0x719c /* offset 6367 */,
    0x243ab /* offset 6368 */,
    0x7228 /* offset 6369 */,
    0x7250 /* offset 6370 */,
    0x24608 /* offset 6371 */,
    0x7280 /* offset 6372 */,
    0x7295 /* offset 6373 */,
    0x24735 /* offset 6374 */,
    0x24814 /* offset 6375 */,
    0x737a /* offset 6376 */,
    0x738b /* offset 6377 */,
    0x3eac /* offset 6378 */,
    0x73a5 /* offset 6379 */,
    0x3eb8 /* offset 6380 */,
    0x7447 /* offset 6381 */,
    0x745c /* offset 6382 */,
    0x7485 /* offset 6383 */,
    0x74ca /* offset 6384 */,
    0x3f1b /* offset 6385 */,
    0x7524 /* offset 6386 */,
    0x24c36 /* offset 6387 */,
    0x753e /* offset 6388 */,
    0x24c92 /* offset 6389 */,
    0x2219f /* offset 6390 */,
    0x7610 /* offset 6391 */,
    0x24fa1 /* offset 6392 */,
    0x24fb8 /* offset 6393 */,
    0x25044 /* offset 6394 */,
    0x3ffc /* offset 6395 */,
    0x4008 /* offset 6396 */,
    0x250f3 /* offset 6397 */,
    0x250f2 /* offset 6398 */,
    0x25119 /* offset 6399 */,
    0x25133 /* offset 6400 */,
    0x771e /* offset 6401 */,
    0x771f /* offset 6402 */,
    0x778b /* offset 6403 */,
    0x4046 /* offset 6404 */,
    0x4096 /* offset 6405 */,
    0x2541d /* offset 6406 */,
    0x784e /* offset 6407 */,
    0x40e3 /* offset 6408 */,
    0x25626 /* offset 6409 */,
    0x2569a /* offset 6410 */,
    0x256c5 /* offset 6411 */,
    0x79eb /* offset 6412 */,
    0x412f /* offset 6413 */,
    0x7a4a /* offset 6414 */,
    0x7a4f /* offset 6415 */,
    0x2597c /* offset 6416 */,
    0x25aa7 /* offset 6417 */,
    0x7aee /* offset 6418 */,
    0x4202 /* offset 6419 */,
    0x25bab /* offset 6420 */,
    0x7bc6 /* offset 6421 */,
    0x7bc9 /* offset 6422 */,
    0x4227 /* offset 6423 */,
    0x25c80 /* offset 6424 */,
    0x7cd2 /* offset 6425 */,
    0x42a0 /* offset 6426 */,
    0x7ce8 /* offset 6427 */,
    0x7ce3 /* offset 6428 */,
    0x7d00 /* offset 6429 */,
    0x25f86 /* offset 6430 */,
    0x7d63 /* offset 6431 */,
    0x4301 /* offset 6432 */,
    0x7dc7 /* offset 6433 */,
    0x7e02 /* offset 6434 */,
    0x7e45 /* offset 6435 */,
    0x4334 /* offset 6436 */,
    0x26228 /* offset 6437 */,
    0x26247 /* offset 6438 */,
    0x4359 /* offset 6439 */,
    0x262d9 /* offset 6440 */,
    0x7f7a /* offset 6441 */,
    0x2633e /* offset 6442 */,
    0x7f95 /* offset 6443 */,
    0x7ffa /* offset 6444 */,
    0x264da /* offset 6445 */,
    0x26523 /* offset 6446 */,
    0x8060 /* offset 6447 */,
    0x265a8 /* offset 6448 */,
    0x8070 /* offset 6449 */,
    0x2335f /* offset 6450 */,
    0x43d5 /* offset 6451 */,
    0x80b2 /* offset 6452 */,
    0x8103 /* offset 6453 */,
    0x440b /* offset 6454 */,
    0x813e /* offset 6455 */,
    0x5ab5 /* offset 6456 */,
    0x267a7 /* offset 6457 */,
    0x267b5 /* offset 6458 */,
    0x23393 /* offset 6459 */,
    0x2339c /* offset 6460 */,
    0x8204 /* offset 6461 */,
    0x8f9e /* offset 6462 */,
    0x446b /* offset 6463 */,
    0x8291 /* offset 6464 */,
    0x828b /* offset 6465 */,
    0x829d /* offset 6466 */,
    0x52b3 /* offset 6467 */,
    0x82b1 /* offset 6468 */,
    0x82b3 /* offset 6469 */,
    0x82bd /* offset 6470 */,
    0x82e6 /* offset 6471 */,
    0x26b3c /* offset 6472 */,
    0x831d /* offset 6473 */,
    0x8363 /* offset 6474 */,
    0x83ad /* offset 6475 */,
    0x8323 /* offset 6476 */,
    0x83bd /* offset 6477 */,
    0x83e7 /* offset 6478 */,
    0x8353 /* offset 6479 */,
    0x83ca /* offset 6480 */,
    0x83cc /* offset 6481 */,
    0x83dc /* offset 6482 */,
    0x26c36 /* offset 6483 */,
    0x26d6b /* offset 6484 */,
    0x26cd5 /* offset 6485 */,
    0x452b /* offset 6486 */,
    0x84f1 /* offset 6487 */,
    0x84f3 /* offset 6488 */,
    0x8516 /* offset 6489 */,
    0x273ca /* offset 6490 */,
    0x8564 /* offset 6491 */,
    0x26f2c /* offset 6492 */,
    0x455d /* offset 6493 */,
    0x4561 /* offset 6494 */,
    0x26fb1 /* offset 6495 */,
    0x270d2 /* offset 6496 */,
    0x456b /* offset 6497 */,
    0x8650 /* offset 6498 */,
    0x8667 /* offset 6499 */,
    0x8669 /* offset 6500 */,
    0x86a9 /* offset 6501 */,
    0x8688 /* offset 6502 */,
    0x870e /* offset 6503 */,
    0x86e2 /* offset 6504 */,
    0x8728 /* offset 6505 */,
    0x876b /* offset 6506 */,
    0x8786 /* offset 6507 */,
    0x45d7 /* offset 6508 */,
    0x87e1 /* offset 6509 */,
    0x8801 /* offset 6510 */,
    0x45f9 /* offset 6511 */,
    0x8860 /* offset 6512 */,
    0x27667 /* offset 6513 */,
    0x88d7 /* offset 6514 */,
    0x88de /* offset 6515 */,
    0x4635 /* offset 6516 */,
    0x88fa /* offset 6517 */,
    0x34bb /* offset 6518 */,
    0x278ae /* offset 6519 */,
    0x27966 /* offset 6520 */,
    0x46be /* offset 6521 */,
    0x46c7 /* offset 6522 */,
    0x8aa0 /* offset 6523 */,
    0x27ca8 /* offset 6524 */,
    0x8cab /* offset 6525 */,
    0x8cc1 /* offset 6526 */,
    0x8d1b /* offset 6527 */,
    0x8d77 /* offset 6528 */,
    0x27f2f /* offset 6529 */,
    0x20804 /* offset 6530 */,
    0x8dcb /* offset 6531 */,
    0x8dbc /* offset 6532 */,
    0x8df0 /* offset 6533 */,
    0x208de /* offset 6534 */,
    0x8ed4 /* offset 6535 */,
    0x285d2 /* offset 6536 */,
    0x285ed /* offset 6537 */,
    0x9094 /* offset 6538 */,
    0x90f1 /* offset 6539 */,
    0x9111 /* offset 6540 */,
    0x2872e /* offset 6541 */,
    0x911b /* offset 6542 */,
    0x9238 /* offset 6543 */,
    0x92d7 /* offset 6544 */,
    0x92d8 /* offset 6545 */,
    0x927c /* offset 6546 */,
    0x93f9 /* offset 6547 */,
    0x9415 /* offset 6548 */,
    0x28bfa /* offset 6549 */,
    0x958b /* offset 6550 */,
    0x4995 /* offset 6551 */,
    0x95b7 /* offset 6552 */,
    0x28d77 /* offset 6553 */,
    0x49e6 /* offset 6554 */,
    0x96c3 /* offset 6555 */,
    0x5db2 /* offset 6556 */,
    0x9723 /* offset 6557 */,
    0x29145 /* offset 6558 */,
    0x2921a /* offset 6559 */,
    0x4a6e /* offset 6560 */,
    0x4a76 /* offset 6561 */,
    0x97e0 /* offset 6562 */,
    0x2940a /* offset 6563 */,
    0x4ab2 /* offset 6564 */,
    0x29496 /* offset 6565 */,
    0x9829 /* offset 6566 */,
    0x295b6 /* offset 6567 */,
    0x98e2 /* offset 6568 */,
    0x4b33 /* offset 6569 */,
    0x9929 /* offset 6570 */,
    0x99a7 /* offset 6571 */,
    0x99c2 /* offset 6572 */,
    0x99fe /* offset 6573 */,
    0x4bce /* offset 6574 */,
    0x29b30 /* offset 6575 */,
    0x9c40 /* offset 6576 */,
    0x9cfd /* offset 6577 */,
    0x4cce /* offset 6578 */,
    0x4ced /* offset 6579 */,
    0x9d67 /* offset 6580 */,
    0x2a0ce /* offset 6581 */,
    0x4cf8 /* offset 6582 */,
    0x2a105 /* offset 6583 */,
    0x2a20e /* offset 6584 */,
    0x2a291 /* offset 6585 */,
    0x4d56 /* offset 6586 */,
    0x9efe /* offset 6587 */,
    0x9f05 /* offset 6588 */,
    0x9f0f /* offset 6589 */,
    0x9f16 /* offset 6590 */,
    0x2a600 /* offset 6591 */
};
