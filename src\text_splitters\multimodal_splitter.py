"""
multimodal_splitter.py

此模組定義了用於多模態 (文本 + 圖像) 文件的文本分割器。
"""

from typing import List
import os
from unstructured.documents.elements import Element, Text, Image
from src.text_splitters.base_splitter import BaseTextSplitter
from src.utils.document_model import Document
from src.utils.file_reader import FileReader
from src.utils.image_processor import ImageProcessor
from src.config.settings import UNSTRUCTURED_IMAGE_OUTPUT_DIR # 引入圖像輸出目錄

class MultiModalSplitter(BaseTextSplitter):
    """
    多模態文件文本分割器，能夠提取文本和圖像內容，並對圖像生成摘要。
    """
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200):
        """
        初始化 MultiModalSplitter。

        Args:
            chunk_size (int): 每個文本塊的最大字元數。 (可調整參數)
            chunk_overlap (int): 相鄰文本塊之間的重疊字元數。 (可調整參數)
        """
        super().__init__(chunk_size, chunk_overlap)
        self.image_processor = ImageProcessor()
        # 確保圖像輸出目錄存在
        os.makedirs(UNSTRUCTURED_IMAGE_OUTPUT_DIR, exist_ok=True)

    def split_document(self, file_path: str) -> List[Document]:
        """
        從指定多模態 PDF 文件路徑讀取內容並進行文本分割，提取文本和圖像。

        Args:
            file_path (str): 待分割多模態 PDF 文件的路徑。

        Returns:
            List[Document]: 包含分割後文本塊及其元數據的 Document 物件列表。
        """
        # 提取圖像時，Unstructured 會將圖像保存到 image_output_dir
        elements = FileReader.read_pdf_file(file_path, extract_images=True, image_output_dir=UNSTRUCTURED_IMAGE_OUTPUT_DIR)
        
        documents = []
        current_text_chunk = ""
        chunk_index = 0

        for element in elements:
            if isinstance(element, Text):
                # 處理普通文本，與 PDFTableSplitter 類似的簡單分割邏輯
                text_content = element.text.strip()
                if text_content:
                    if len(current_text_chunk) + len(text_content) > self.chunk_size and current_text_chunk:
                        documents.append(Document(
                            page_content=current_text_chunk,
                            metadata={"source": file_path, "type": "multimodal_text", "chunk_index": chunk_index}
                        ))
                        chunk_index += 1
                        current_text_chunk = text_content
                    else:
                        current_text_chunk += ("\n\n" if current_text_chunk else "") + text_content
            elif isinstance(element, Image):
                # 處理圖像
                if current_text_chunk: # 如果有未處理的文本，先作為一個 Document
                    documents.append(Document(
                        page_content=current_text_chunk,
                        metadata={"source": file_path, "type": "multimodal_text", "chunk_index": chunk_index}
                    ))
                    chunk_index += 1
                    current_text_chunk = "" # 重置文本塊

                image_path = element.metadata.image_path # Unstructured 提取的圖像路徑
                if image_path and os.path.exists(image_path):
                    image_summary = self.image_processor.summarize_image(image_path)
                    documents.append(Document(
                        page_content=image_summary,
                        metadata={
                            "source": file_path,
                            "type": "image_summary",
                            "image_path": image_path,
                            "chunk_index": chunk_index
                        }
                    ))
                    chunk_index += 1
                else:
                    print(f"警告: 圖像文件未找到或路徑無效: {image_path}")
        
        # 添加最後一個文本塊（如果存在）
        if current_text_chunk:
            documents.append(Document(
                page_content=current_text_chunk,
                metadata={"source": file_path, "type": "multimodal_text", "chunk_index": chunk_index}
            ))

        return documents