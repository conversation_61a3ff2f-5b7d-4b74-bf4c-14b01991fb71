//========================================================================
//
// GlobalParams.h
//
// Copyright 2001-2003 Glyph & Cog, LLC
//
//========================================================================

//========================================================================
//
// Modified under the Poppler project - http://poppler.freedesktop.org
//
// All changes made under the Poppler project to this file are licensed
// under GPL version 2 or later
//
// Copyright (C) 2005, 2007-2010, 2012, 2015, 2017-2023 <PERSON> <<EMAIL>>
// Copyright (C) 2005 <PERSON> <<EMAIL>>
// Copyright (C) 2006 Ta<PERSON><PERSON> <<EMAIL>>
// Copyright (C) 2006 Kristian Høgsberg <<EMAIL>>
// Copyright (C) 2007 Krzysztof Kowalczyk <<EMAIL>>
// Copyright (C) 2009 Jonathan Kew <<EMAIL>>
// Copyright (C) 2009 Petr Gajdos <<EMAIL>>
// Copyright (C) 2009, 2011, 2012, 2014, 2015 William Bader <<EMAIL>>
// Copyright (C) 2010 Hib Eris <<EMAIL>>
// Copyright (C) 2011 Pino Toscano <<EMAIL>>
// Copyright (C) 2012, 2017 Adrian Johnson <<EMAIL>>
// Copyright (C) 2012 Thomas Freitag <<EMAIL>>
// Copyright (C) 2013 Jason Crain <<EMAIL>>
// Copyright (C) 2018, 2020 Adam Reichold <<EMAIL>>
// Copyright (C) 2019 Oliver Sander <<EMAIL>>
// Copyright (C) 2023 Shivodit Gill <<EMAIL>>
//
// To see a description of the changes please see the Changelog file that
// came with your tarball or type make ChangeLog if you are building from git
//
//========================================================================

#ifndef GLOBALPARAMS_H
#define GLOBALPARAMS_H

#include <cassert>
#include "poppler-config.h"
#include "poppler_private_export.h"
#include <cstdio>
#include "CharTypes.h"
#include "UnicodeMap.h"
#include "Error.h"
#include <unordered_map>
#include <string>
#include <memory>
#include <mutex>
#include <vector>

class GooString;
class NameToCharCode;
class CharCodeToUnicode;
class CharCodeToUnicodeCache;
class UnicodeMapCache;
class CMap;
class CMapCache;
class GlobalParams;
class GfxFont;
class Stream;
class SysFontList;

//------------------------------------------------------------------------

// The global parameters object.
#ifdef _COMPILING_POPPLER
// cmake's WINDOWS_EXPORT_ALL_SYMBOLS doesn't extend to data unfortunately
// so we must to this by hand
extern __declspec(dllexport) std::unique_ptr<GlobalParams> globalParams;
#else
extern __declspec(dllimport) std::unique_ptr<GlobalParams> globalParams;
#endif


//------------------------------------------------------------------------

enum SysFontType
{
    sysFontPFA,
    sysFontPFB,
    sysFontTTF,
    sysFontTTC
};

//------------------------------------------------------------------------

struct FamilyStyleFontSearchResult
{
    FamilyStyleFontSearchResult() = default;

    FamilyStyleFontSearchResult(const std::string &filepathA, int faceIndexA) : filepath(filepathA), faceIndex(faceIndexA) { }

    std::string filepath;
    int faceIndex = 0;
};

//------------------------------------------------------------------------

struct UCharFontSearchResult
{
    UCharFontSearchResult() = default;

    UCharFontSearchResult(const std::string &filepathA, int faceIndexA, const std::string &familyA, const std::string &styleA) : filepath(filepathA), faceIndex(faceIndexA), family(familyA), style(styleA) { }

    const std::string filepath;
    const int faceIndex = 0;
    const std::string family;
    const std::string style;
};

//------------------------------------------------------------------------

class POPPLER_PRIVATE_EXPORT GlobalParams
{
public:
    // Initialize the global parameters
    explicit GlobalParams(const char *customPopplerDataDir = nullptr);

    ~GlobalParams();

    GlobalParams(const GlobalParams &) = delete;
    GlobalParams &operator=(const GlobalParams &) = delete;

    void setupBaseFonts(const char *dir);

    //----- accessors

    CharCode getMacRomanCharCode(const char *charName);

    // Return Unicode values for character names.  Used for general text
    // extraction.
    Unicode mapNameToUnicodeText(const char *charName);

    // Return Unicode values for character names.  Used for glyph
    // lookups or text extraction with ZapfDingbats fonts.
    Unicode mapNameToUnicodeAll(const char *charName);

    UnicodeMap *getResidentUnicodeMap(const std::string &encodingName);
    FILE *getUnicodeMapFile(const std::string &encodingName);
    FILE *findCMapFile(const GooString *collection, const GooString *cMapName);
    FILE *findToUnicodeFile(const GooString *name);
    GooString *findFontFile(const std::string &fontName);
    GooString *findBase14FontFile(const GooString *base14Name, const GfxFont *font, GooString *substituteFontName = nullptr);
    GooString *findSystemFontFile(const GfxFont *font, SysFontType *type, int *fontNum, GooString *substituteFontName = nullptr, const GooString *base14Name = nullptr);
    FamilyStyleFontSearchResult findSystemFontFileForFamilyAndStyle(const std::string &fontFamily, const std::string &fontStyle, const std::vector<std::string> &filesToIgnore = {});
    UCharFontSearchResult findSystemFontFileForUChar(Unicode uChar, const GfxFont &fontToEmulate);
    std::string getTextEncodingName() const;
    bool getPrintCommands();
    bool getProfileCommands();
    bool getErrQuiet();

    CharCodeToUnicode *getCIDToUnicode(const GooString *collection);
    const UnicodeMap *getUnicodeMap(const std::string &encodingName);
    std::shared_ptr<CMap> getCMap(const GooString *collection, const GooString *cMapName);
    const UnicodeMap *getTextEncoding();

    const UnicodeMap *getUtf8Map();

    std::vector<std::string> getEncodingNames();

    //----- functions to set parameters
    void addFontFile(const std::string &fontName, const std::string &path);
    void setTextEncoding(const char *encodingName);
    void setPrintCommands(bool printCommandsA);
    void setProfileCommands(bool profileCommandsA);
    void setErrQuiet(bool errQuietA);
#ifdef ANDROID
    static void setFontDir(const std::string &fontDir);
#endif
    static bool parseYesNo2(const char *token, bool *flag);

private:
    void parseNameToUnicode(const GooString *name);

    void scanEncodingDirs();
    void addCIDToUnicode(const GooString *collection, const GooString *fileName);
    void addUnicodeMap(const GooString *encodingName, const GooString *fileName);
    void addCMapDir(const GooString *collection, const GooString *dir);

    //----- static tables

    NameToCharCode * // mapping from char name to
            macRomanReverseMap; //   MacRomanEncoding index

    //----- user-modifiable settings

    NameToCharCode * // mapping from char name to Unicode for ZapfDingbats
            nameToUnicodeZapfDingbats;
    NameToCharCode * // mapping from char name to Unicode for text
            nameToUnicodeText; // extraction
    // files for mappings from char collections
    // to Unicode, indexed by collection name
    std::unordered_map<std::string, std::string> cidToUnicodes;
    // mappings from Unicode to char codes,
    // indexed by encoding name
    std::unordered_map<std::string, UnicodeMap> residentUnicodeMaps;
    // files for mappings from Unicode to char
    // codes, indexed by encoding name
    std::unordered_map<std::string, std::string> unicodeMaps;
    // list of CMap dirs, indexed by collection
    std::unordered_multimap<std::string, std::string> cMapDirs;
    std::vector<GooString *> toUnicodeDirs; // list of ToUnicode CMap dirs
    bool baseFontsInitialized;
#ifdef _WIN32
    // windows font substitutes (for CID fonts)
    std::unordered_map<std::string, std::string> substFiles;
#endif
    // font files: font name mapped to path
    std::unordered_map<std::string, std::string> fontFiles;
    SysFontList *sysFonts; // system fonts
    GooString *textEncoding; // encoding (unicodeMap) to use for text
                             //   output
    bool printCommands; // print the drawing commands
    bool profileCommands; // profile the drawing commands
    bool errQuiet; // suppress error messages?

    CharCodeToUnicodeCache *cidToUnicodeCache;
    CharCodeToUnicodeCache *unicodeToUnicodeCache;
    UnicodeMapCache *unicodeMapCache;
    CMapCache *cMapCache;

    const UnicodeMap *utf8Map;

    mutable std::recursive_mutex mutex;
    mutable std::recursive_mutex unicodeMapCacheMutex;
    mutable std::recursive_mutex cMapCacheMutex;

    const char *popplerDataDir;
};

class POPPLER_PRIVATE_EXPORT GlobalParamsIniter
{
public:
    explicit GlobalParamsIniter(ErrorCallback errorCallback);
    ~GlobalParamsIniter();

    GlobalParamsIniter(const GlobalParamsIniter &) = delete;
    GlobalParamsIniter &operator=(const GlobalParamsIniter &) = delete;

    static bool setCustomDataDir(const std::string &dir);

private:
    static std::mutex mutex;
    static int count;

    static std::string customDataDir;
};

#endif
