"""
圖片處理管理器
整合圖片提取、描述生成和向量化功能
"""
import os
from typing import List, Dict, Optional
from .image_extractor import ImageExtractor
from .image_captioner import ImageCaptioner, SimpleImageCaptioner
from src.embeddings.embedding_generator import EmbeddingGenerator
from src.database.db_manager import DBManager
from src.utils.document_model import Document

class ImageProcessor:
    """圖片處理管理器"""
    
    def __init__(self, 
                 output_dir: str = "./data/images",
                 use_ai_captioning: bool = True,
                 db_manager: Optional[DBManager] = None,
                 embedding_generator: Optional[EmbeddingGenerator] = None):
        """
        初始化圖片處理管理器
        
        Args:
            output_dir: 圖片保存目錄
            use_ai_captioning: 是否使用 AI 生成描述
            db_manager: 資料庫管理器
            embedding_generator: 嵌入生成器
        """
        self.output_dir = output_dir
        self.use_ai_captioning = use_ai_captioning
        
        # 初始化組件
        self.extractor = ImageExtractor(output_dir)
        
        if use_ai_captioning:
            try:
                # 嘗試使用 Gemini 描述生成器
                api_key = os.getenv('GEMINI_API_KEY')
                if api_key:
                    self.captioner = ImageCaptioner(api_key=api_key)
                    print("✓ 使用 Gemini 圖片描述生成器")
                else:
                    print("⚠️ 未設置 GEMINI_API_KEY，使用簡化版本")
                    self.captioner = SimpleImageCaptioner()
                    self.use_ai_captioning = False
            except Exception as e:
                print(f"⚠️ Gemini 描述生成器載入失敗，使用簡化版本: {e}")
                self.captioner = SimpleImageCaptioner()
                self.use_ai_captioning = False
        else:
            self.captioner = SimpleImageCaptioner()
            print("✓ 使用簡化圖片描述生成器")
        
        self.db_manager = db_manager
        self.embedding_generator = embedding_generator
    
    def process_pdf_images(self, pdf_path: str, parent_id: str) -> Dict[str, any]:
        """
        處理 PDF 中的所有圖片
        
        Args:
            pdf_path: PDF 文件路徑
            parent_id: 父文檔 ID
            
        Returns:
            Dict: 處理結果統計
        """
        print(f"\n=== 開始處理 PDF 圖片: {pdf_path} ===")
        
        results = {
            'extracted_count': 0,
            'processed_count': 0,
            'saved_count': 0,
            'error_count': 0,
            'image_paths': []
        }
        
        try:
            # 1. 提取圖片
            print("步驟 1: 提取圖片...")
            extracted_images = self.extractor.extract_and_deduplicate(pdf_path)
            results['extracted_count'] = len(extracted_images)
            
            if not extracted_images:
                print("✓ 此 PDF 中沒有找到圖片")
                return results
            
            # 2. 生成描述
            print("步驟 2: 生成圖片描述...")
            captioned_images = self.captioner.generate_captions_batch(
                extracted_images, 
                detailed=self.use_ai_captioning
            )
            results['processed_count'] = len(captioned_images)
            
            # 3. 生成向量嵌入並存入資料庫
            if self.embedding_generator and self.db_manager:
                print("步驟 3: 生成向量嵌入並存入資料庫...")
                saved_count = self._save_images_to_database(captioned_images, parent_id)
                results['saved_count'] = saved_count
                
                # 4. 更新父文檔的圖片信息
                image_paths = [img['path'] for img in captioned_images]
                self.db_manager.update_parent_document_images(parent_id, image_paths)
                results['image_paths'] = image_paths
                
                print(f"✓ 已將 {saved_count} 張圖片信息存入資料庫")
            
            print(f"=== 圖片處理完成 ===")
            print(f"提取: {results['extracted_count']} 張")
            print(f"處理: {results['processed_count']} 張") 
            print(f"存儲: {results['saved_count']} 張")
            
        except Exception as e:
            print(f"✗ 圖片處理失敗: {e}")
            results['error_count'] = 1
        
        return results
    
    def _save_images_to_database(self, image_list: List[Dict], parent_id: str) -> int:
        """
        將圖片信息存入資料庫
        
        Args:
            image_list: 圖片信息列表
            parent_id: 父文檔 ID
            
        Returns:
            int: 成功保存的數量
        """
        saved_count = 0
        
        for i, img_info in enumerate(image_list):
            try:
                # 創建圖片文檔對象
                img_document = Document()
                img_document.page_content = img_info.get('caption', '')
                img_document.content = img_info.get('caption', '')
                img_document.metadata = {
                    'source': img_info.get('pdf_source', ''),
                    'type': 'image',
                    'page_number': img_info.get('page_number', 0),
                    'image_filename': img_info.get('filename', ''),
                    'image_width': img_info.get('width', 0),
                    'image_height': img_info.get('height', 0),
                    'image_size_bytes': img_info.get('size_bytes', 0),
                    'extracted_at': img_info.get('extracted_at', ''),
                    'descriptions': img_info.get('descriptions', {})
                }
                
                # 生成向量嵌入
                embedding = self.embedding_generator.generate_single_embedding(
                    img_info.get('caption', '')
                )
                
                # 存入資料庫
                child_id = self.db_manager.insert_child_document(
                    parent_id=parent_id,
                    document=img_document,
                    embedding=embedding,
                    content_type='image',
                    image_path=img_info.get('path', ''),
                    image_description=img_info.get('caption', '')
                )
                
                saved_count += 1
                print(f"  ✓ 圖片 {i+1} 已存入資料庫，ID: {child_id}")
                
            except Exception as e:
                print(f"  ✗ 圖片 {i+1} 存入失敗: {e}")
                continue
        
        return saved_count
    
    def get_image_info(self, image_path: str) -> Dict:
        """
        獲取圖片基本信息
        
        Args:
            image_path: 圖片路徑
            
        Returns:
            Dict: 圖片信息
        """
        try:
            from PIL import Image
            
            if not os.path.exists(image_path):
                return {'error': '圖片文件不存在'}
            
            image = Image.open(image_path)
            
            return {
                'path': image_path,
                'filename': os.path.basename(image_path),
                'width': image.width,
                'height': image.height,
                'mode': image.mode,
                'format': image.format,
                'size_bytes': os.path.getsize(image_path)
            }
            
        except Exception as e:
            return {'error': f'無法讀取圖片: {str(e)}'}
    
    def cleanup(self):
        """清理資源"""
        if hasattr(self.captioner, 'cleanup_model'):
            self.captioner.cleanup_model()
        print("✓ 圖片處理器資源已清理")
    
    def test_image_processing(self, test_image_path: str = None) -> bool:
        """
        測試圖片處理功能
        
        Args:
            test_image_path: 測試圖片路徑
            
        Returns:
            bool: 測試是否成功
        """
        try:
            print("=== 測試圖片處理功能 ===")
            
            # 如果沒有提供測試圖片，創建一個簡單的測試圖片
            if not test_image_path:
                from PIL import Image, ImageDraw
                test_image = Image.new('RGB', (200, 100), color='lightblue')
                draw = ImageDraw.Draw(test_image)
                draw.text((50, 40), "Test Image", fill='black')
                test_image_path = os.path.join(self.output_dir, 'test_image.png')
                test_image.save(test_image_path)
                print(f"✓ 創建測試圖片: {test_image_path}")
            
            # 測試描述生成
            caption = self.captioner.generate_caption(test_image_path)
            print(f"✓ 圖片描述: {caption}")
            
            # 測試向量生成
            if self.embedding_generator:
                embedding = self.embedding_generator.generate_single_embedding(caption)
                print(f"✓ 向量維度: {len(embedding)}")
            
            print("✓ 圖片處理功能測試通過")
            return True
            
        except Exception as e:
            print(f"✗ 圖片處理功能測試失敗: {e}")
            return False
