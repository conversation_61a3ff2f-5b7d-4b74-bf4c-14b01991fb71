"""
統一文件處理程式
支援 PDF 和 TXT 文件的自動識別和處理
使用簡化的資料夾結構：檔案/ 和 照片/
"""
import os
import glob
from pathlib import Path
from src.config.poppler_config import setup_all_tools
from src.text_splitters.pdf_table_splitter import PDFTableSplitter
from src.text_splitters.txt_splitter import TXTSplitter
from src.utils.document_model import Document
from src.database.db_manager import DBManager
from src.embeddings.embedding_generator import EmbeddingGenerator
from src.image_processing.image_processor import ImageProcessor
from langchain_text_splitters import RecursiveCharacterTextSplitter

# 設置所有工具路徑
print("=== 檢查和設置工具路徑 ===")
setup_all_tools()
print("=" * 40)

def get_files_in_directory(directory: str, extensions: list) -> list:
    """獲取指定目錄中的特定擴展名文件"""
    files = []
    for ext in extensions:
        pattern = os.path.join(directory, f"*.{ext}")
        files.extend(glob.glob(pattern))
    return files

def process_file(file_path: str, db_manager, embedding_generator, image_processor=None):
    """處理單個文件（PDF 或 TXT）"""
    file_ext = Path(file_path).suffix.lower()
    file_name = Path(file_path).name
    
    print(f"\n{'='*60}")
    print(f"正在處理文件: {file_name}")
    print(f"文件類型: {file_ext}")
    print(f"{'='*60}")
    
    # 根據文件類型選擇處理器
    if file_ext == '.pdf':
        return process_pdf_file(file_path, db_manager, embedding_generator, image_processor)
    elif file_ext == '.txt':
        return process_txt_file(file_path, db_manager, embedding_generator)
    else:
        print(f"⚠️ 不支援的文件類型: {file_ext}")
        return False

def process_pdf_file(file_path: str, db_manager, embedding_generator, image_processor):
    """處理 PDF 文件"""
    try:
        # PDF 分割器
        splitter = PDFTableSplitter(split_by_title=True)
        parent_documents = splitter.split_document(file_path)
        
        print(f"✓ PDF 分割完成，共 {len(parent_documents)} 個 Parent Documents")
        
        # 處理文字內容
        stored_parent_ids = process_documents(
            parent_documents, file_path, db_manager, embedding_generator, 'pdf'
        )
        
        # 處理圖片
        if image_processor:
            process_pdf_images(file_path, stored_parent_ids, image_processor, db_manager, embedding_generator)
        
        return True
        
    except Exception as e:
        print(f"❌ PDF 處理失敗: {e}")
        return False

def process_txt_file(file_path: str, db_manager, embedding_generator):
    """處理 TXT 文件"""
    try:
        # 預覽標題
        splitter_preview = TXTSplitter()
        potential_titles = splitter_preview.get_title_preview(file_path)
        
        use_title_split = False
        if potential_titles:
            print(f"檢測到 {len(potential_titles)} 個可能的標題")
            for title in potential_titles[:5]:  # 顯示前5個
                print(f"  {title}")
            
            if len(potential_titles) >= 3:  # 如果有3個以上標題，自動使用標題分割
                use_title_split = True
                print("✓ 自動啟用標題分割")
            else:
                print("✓ 使用段落分割")
        
        # TXT 分割器
        splitter = TXTSplitter(split_by_title=use_title_split)
        parent_documents = splitter.split_document(file_path)
        
        split_type = "按標題" if use_title_split else "按段落"
        print(f"✓ TXT 分割完成 ({split_type})，共 {len(parent_documents)} 個 Parent Documents")
        
        # 處理文字內容
        process_documents(
            parent_documents, file_path, db_manager, embedding_generator, 'txt'
        )
        
        return True
        
    except Exception as e:
        print(f"❌ TXT 處理失敗: {e}")
        return False

def process_documents(parent_documents, file_path, db_manager, embedding_generator, file_type):
    """處理文檔並存入資料庫"""
    child_splitter = RecursiveCharacterTextSplitter(
        chunk_size=500,
        chunk_overlap=100
    )
    
    stored_parent_ids = []
    
    for i, parent_doc in enumerate(parent_documents):
        title = parent_doc.metadata.get('title', f'文檔 {i+1}')
        print(f"\n--- Parent Document {i+1}: {title} ---")
        print(f"內容 (前100字): {parent_doc.page_content[:100]}...")
        
        # 存入資料庫
        parent_id = db_manager.insert_parent_document(parent_doc)
        stored_parent_ids.append(parent_id)
        
        # 生成 Child Documents
        child_texts = child_splitter.split_text(parent_doc.page_content)
        print(f"  ✓ 拆分為 {len(child_texts)} 個 Child Documents")
        
        for j, child_text in enumerate(child_texts):
            child_doc = Document(
                page_content=child_text,
                metadata={
                    'source': file_path,
                    'type': f'{file_type}_child_text',
                    'title': title,
                    'chunk_index': parent_doc.metadata.get('chunk_index', i),
                    'parent_id': parent_id,
                    'child_chunk_index': j
                }
            )
            
            # 生成嵌入向量並存入資料庫
            embedding = embedding_generator.generate_embeddings([child_doc.page_content])[0]
            db_manager.insert_child_document(parent_id, child_doc, embedding)
    
    return stored_parent_ids

def process_pdf_images(file_path, stored_parent_ids, image_processor, db_manager, embedding_generator):
    """處理 PDF 圖片"""
    try:
        print(f"\n=== 🖼️ 處理 PDF 圖片 ===")
        from src.image_processing.image_extractor import ImageExtractor
        extractor = ImageExtractor("./data/照片")
        all_images = extractor.extract_and_deduplicate(file_path)
        
        if all_images:
            print(f"✓ 從 PDF 中提取了 {len(all_images)} 張圖片")
            
            # 處理每張圖片
            for img_info in all_images:
                print(f"  ✓ 圖片 {img_info['filename']} -> 描述: {img_info.get('description', '無描述')[:50]}...")
                
                # 創建圖片文檔並存入資料庫
                if stored_parent_ids:
                    img_document = Document()
                    img_document.page_content = img_info.get('description', '')
                    img_document.metadata = {
                        'source': file_path,
                        'type': 'image',
                        'image_path': img_info['path'],
                        'image_description': img_info.get('description', ''),
                        'page_number': img_info.get('page', 0),
                        'image_size': f"{img_info.get('width', 0)}x{img_info.get('height', 0)}"
                    }
                    
                    # 生成嵌入向量
                    embedding = embedding_generator.generate_embeddings([img_document.page_content])[0]
                    
                    # 存入資料庫
                    db_manager.insert_child_document(
                        parent_id=stored_parent_ids[0],
                        document=img_document,
                        embedding=embedding,
                        content_type='image',
                        image_path=img_info['path'],
                        image_description=img_info.get('description', '')
                    )
            
            print(f"✓ 圖片處理完成: 成功處理 {len(all_images)} 張圖片")
        else:
            print("ℹ️ 此 PDF 中沒有圖片")
            
    except Exception as e:
        print(f"⚠️ 圖片處理失敗: {e}")

def main():
    """主函數"""
    print("🚀 統一文件處理系統")
    print("支援文件類型: PDF, TXT")
    print("資料夾結構: 檔案/ (輸入) → 照片/ (圖片輸出)")
    
    # 確保資料夾存在
    os.makedirs("./data/檔案", exist_ok=True)
    os.makedirs("./data/照片", exist_ok=True)
    
    # 初始化組件
    db_manager = DBManager()
    embedding_generator = EmbeddingGenerator()
    image_processor = ImageProcessor(
        output_dir="./data/照片",
        use_ai_captioning=True,
        db_manager=db_manager,
        embedding_generator=embedding_generator
    )
    
    # 創建資料庫表
    db_manager.create_tables()
    
    # 掃描檔案目錄
    files_dir = "./data/檔案"
    supported_extensions = ['pdf', 'txt']
    
    print(f"\n📁 掃描目錄: {files_dir}")
    all_files = get_files_in_directory(files_dir, supported_extensions)
    
    if not all_files:
        print("❌ 沒有找到支援的文件")
        print(f"請將 PDF 或 TXT 文件放入 {files_dir} 目錄中")
        return
    
    print(f"✓ 找到 {len(all_files)} 個文件:")
    for file_path in all_files:
        print(f"  - {Path(file_path).name}")
    
    # 處理所有文件
    success_count = 0
    total_count = len(all_files)
    
    for file_path in all_files:
        if process_file(file_path, db_manager, embedding_generator, image_processor):
            success_count += 1
    
    # 顯示處理結果
    print(f"\n{'='*60}")
    print(f"📊 處理完成統計")
    print(f"{'='*60}")
    print(f"✓ 成功處理: {success_count}/{total_count} 個文件")
    print(f"✓ 向量維度: 768 (BGE 中文模型)")
    print(f"✓ 資料庫: PostgreSQL + pgvector")
    print(f"✓ 圖片存儲: ./data/照片/")
    
    # 清理資源
    try:
        image_processor.cleanup()
        print("✓ 資源清理完成")
    except Exception as e:
        print(f"⚠️ 資源清理時出現問題: {e}")

if __name__ == "__main__":
    main()
