"""
FastAPI 文件處理接口
支援文件上傳、自動判斷類型、向量處理、防重複上傳
"""
import os
import hashlib
import shutil
from pathlib import Path
from typing import List, Optional
from datetime import datetime

from fastapi import FastAPI, UploadFile, File, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# 導入處理模組
from src.config.poppler_config import setup_all_tools
from src.text_splitters.pdf_table_splitter import PDFTableSplitter
from src.text_splitters.txt_splitter import TXTSplitter
from src.utils.document_model import Document
from src.database.db_manager import DBManager
from src.embeddings.embedding_generator import EmbeddingGenerator
from src.image_processing.image_processor import ImageProcessor
from langchain_text_splitters import RecursiveCharacterTextSplitter

# 設置工具路徑
setup_all_tools()

# 創建 FastAPI 應用
app = FastAPI(
    title="智能文件處理 API",
    description="支援 PDF、TXT 文件上傳、向量化處理和防重複管理",
    version="1.0.0"
)

# 添加 CORS 中間件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允許所有來源
    allow_credentials=True,
    allow_methods=["*"],  # 允許所有方法
    allow_headers=["*"],  # 允許所有標頭
)

# 全局變數
db_manager = None
embedding_generator = None
image_processor = None

# 支援的文件類型
SUPPORTED_EXTENSIONS = {
    '.pdf': 'application/pdf',
    '.txt': 'text/plain',
    '.doc': 'application/msword',
    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
}

# 響應模型
class FileProcessResponse(BaseModel):
    success: bool
    message: str
    file_id: Optional[str] = None
    file_hash: Optional[str] = None
    processing_stats: Optional[dict] = None

class FileStatusResponse(BaseModel):
    exists: bool
    file_hash: str
    processed_at: Optional[str] = None
    stats: Optional[dict] = None

@app.on_event("startup")
async def startup_event():
    """應用啟動時初始化組件"""
    global db_manager, embedding_generator, image_processor
    
    print("🚀 正在初始化文件處理系統...")
    
    # 確保資料夾存在
    os.makedirs("./data/output", exist_ok=True)
    os.makedirs("./data/照片", exist_ok=True)
    
    # 初始化組件
    db_manager = DBManager()
    embedding_generator = EmbeddingGenerator()
    image_processor = ImageProcessor(
        output_dir="./data/照片",
        use_ai_captioning=True,
        db_manager=db_manager,
        embedding_generator=embedding_generator
    )
    
    # 創建資料庫表
    db_manager.create_tables()
    
    print("✅ 文件處理系統初始化完成")

@app.on_event("shutdown")
async def shutdown_event():
    """應用關閉時清理資源"""
    global image_processor
    if image_processor:
        try:
            image_processor.cleanup()
            print("✅ 資源清理完成")
        except Exception as e:
            print(f"⚠️ 資源清理時出現問題: {e}")

def calculate_file_hash(file_content: bytes) -> str:
    """計算文件的 MD5 雜湊值"""
    return hashlib.md5(file_content).hexdigest()

def is_file_processed(file_hash: str) -> bool:
    """檢查文件是否已經處理過"""
    output_dir = Path("./data/output")
    for file_path in output_dir.glob("*"):
        if file_path.is_file() and file_hash in file_path.name:
            return True
    return False

def get_safe_filename(original_filename: str, file_hash: str) -> str:
    """生成安全的文件名"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    name = Path(original_filename).stem
    ext = Path(original_filename).suffix
    return f"{name}_{file_hash[:8]}_{timestamp}{ext}"

async def process_file_content(file_path: str, original_filename: str) -> dict:
    """處理文件內容並返回統計信息"""
    file_ext = Path(file_path).suffix.lower()
    
    try:
        if file_ext == '.pdf':
            return await process_pdf_file(file_path, original_filename)
        elif file_ext == '.txt':
            return await process_txt_file(file_path, original_filename)
        else:
            raise ValueError(f"不支援的文件類型: {file_ext}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件處理失敗: {str(e)}")

async def process_pdf_file(file_path: str, original_filename: str) -> dict:
    """處理 PDF 文件"""
    # PDF 分割器
    splitter = PDFTableSplitter(split_by_title=True)
    parent_documents = splitter.split_document(file_path)
    
    # 處理文字內容
    stored_parent_ids = await process_documents(
        parent_documents, file_path, original_filename, 'pdf'
    )
    
    # 處理圖片
    image_count = 0
    if image_processor:
        image_count = await process_pdf_images(file_path, stored_parent_ids)
    
    return {
        'file_type': 'PDF',
        'parent_documents': len(parent_documents),
        'child_documents': sum(len(RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=100).split_text(doc.page_content)) for doc in parent_documents),
        'images_processed': image_count,
        'vector_dimension': 768
    }

async def process_txt_file(file_path: str, original_filename: str) -> dict:
    """處理 TXT 文件"""
    # 檢測標題
    splitter_preview = TXTSplitter()
    potential_titles = splitter_preview.get_title_preview(file_path)
    
    use_title_split = len(potential_titles) >= 3
    
    # TXT 分割器
    splitter = TXTSplitter(split_by_title=use_title_split)
    parent_documents = splitter.split_document(file_path)
    
    # 處理文字內容
    await process_documents(
        parent_documents, file_path, original_filename, 'txt'
    )
    
    return {
        'file_type': 'TXT',
        'split_method': '按標題' if use_title_split else '按段落',
        'titles_detected': len(potential_titles),
        'parent_documents': len(parent_documents),
        'child_documents': sum(len(RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=100).split_text(doc.page_content)) for doc in parent_documents),
        'vector_dimension': 768
    }

async def process_documents(parent_documents: List[Document], file_path: str, original_filename: str, file_type: str) -> List[int]:
    """處理文檔並存入資料庫"""
    child_splitter = RecursiveCharacterTextSplitter(
        chunk_size=500,
        chunk_overlap=100
    )
    
    stored_parent_ids = []
    
    for i, parent_doc in enumerate(parent_documents):
        # 存入資料庫
        parent_id = db_manager.insert_parent_document(parent_doc)
        stored_parent_ids.append(parent_id)
        
        # 生成 Child Documents
        child_texts = child_splitter.split_text(parent_doc.page_content)
        
        for j, child_text in enumerate(child_texts):
            child_doc = Document(
                page_content=child_text,
                metadata={
                    'source': original_filename,
                    'type': f'{file_type}_child_text',
                    'title': parent_doc.metadata.get('title', f'文檔 {i+1}'),
                    'chunk_index': parent_doc.metadata.get('chunk_index', i),
                    'parent_id': parent_id,
                    'child_chunk_index': j
                }
            )
            
            # 生成嵌入向量並存入資料庫
            embedding = embedding_generator.generate_embeddings([child_doc.page_content])[0]
            db_manager.insert_child_document(parent_id, child_doc, embedding)
    
    return stored_parent_ids

async def process_pdf_images(file_path: str, stored_parent_ids: List[int]) -> int:
    """處理 PDF 圖片 - 使用 ImageProcessor 進行完整處理"""
    try:
        # 只處理一次 PDF 圖片，使用第一個父文檔 ID
        if not stored_parent_ids:
            print("⚠️ 沒有父文檔 ID，跳過圖片處理")
            return 0

        # 使用第一個父文檔 ID 進行圖片處理
        first_parent_id = str(stored_parent_ids[0])

        print(f"🖼️ 開始處理 PDF 圖片，關聯到父文檔 ID: {first_parent_id}")

        # 使用 ImageProcessor 進行完整的圖片處理（包含 LLM 描述生成）
        result = image_processor.process_pdf_images(
            pdf_path=file_path,
            parent_id=first_parent_id
        )

        processed_count = result.get('processed_count', 0)
        print(f"✓ 圖片處理完成，共處理 {processed_count} 張圖片")

        return processed_count

    except Exception as e:
        print(f"⚠️ 圖片處理失敗: {e}")
        return 0

@app.post("/upload", response_model=FileProcessResponse)
async def upload_file(
    file: UploadFile = File(...)
):
    """
    上傳文件接口
    支援 PDF、TXT 文件，自動判斷類型並處理
    """
    # 檢查文件類型
    file_ext = Path(file.filename).suffix.lower()
    if file_ext not in SUPPORTED_EXTENSIONS:
        raise HTTPException(
            status_code=400, 
            detail=f"不支援的文件類型: {file_ext}。支援的類型: {list(SUPPORTED_EXTENSIONS.keys())}"
        )
    
    # 讀取文件內容
    try:
        file_content = await file.read()
        file_hash = calculate_file_hash(file_content)
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"文件讀取失敗: {str(e)}")
    
    # 檢查是否已處理過
    if is_file_processed(file_hash):
        return FileProcessResponse(
            success=False,
            message="文件已存在，跳過重複處理",
            file_hash=file_hash
        )
    
    # 生成安全的文件名並保存到臨時位置
    safe_filename = get_safe_filename(file.filename, file_hash)
    temp_file_path = f"./temp_{safe_filename}"
    
    try:
        with open(temp_file_path, "wb") as f:
            f.write(file_content)
        
        # 處理文件
        processing_stats = await process_file_content(temp_file_path, file.filename)
        
        # 移動到 output 目錄
        output_file_path = f"./data/output/{safe_filename}"
        shutil.move(temp_file_path, output_file_path)
        
        return FileProcessResponse(
            success=True,
            message="文件處理完成",
            file_id=safe_filename,
            file_hash=file_hash,
            processing_stats=processing_stats
        )
        
    except Exception as e:
        # 清理臨時文件
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)
        raise HTTPException(status_code=500, detail=f"文件處理失敗: {str(e)}")

@app.get("/status/{file_hash}", response_model=FileStatusResponse)
async def check_file_status(file_hash: str):
    """檢查文件處理狀態"""
    exists = is_file_processed(file_hash)
    
    if exists:
        # 查找文件信息
        output_dir = Path("./data/OUTPUT")
        for file_path in output_dir.glob("*"):
            if file_path.is_file() and file_hash in file_path.name:
                stat = file_path.stat()
                return FileStatusResponse(
                    exists=True,
                    file_hash=file_hash,
                    processed_at=datetime.fromtimestamp(stat.st_mtime).isoformat(),
                    stats={"file_size": stat.st_size}
                )
    
    return FileStatusResponse(
        exists=False,
        file_hash=file_hash
    )

@app.get("/files")
async def list_processed_files():
    """列出所有已處理的文件，包含資料庫統計信息"""
    output_dir = Path("./data/output")
    files = []

    for file_path in output_dir.glob("*"):
        if file_path.is_file():
            stat = file_path.stat()

            # 從文件名中提取原始文件名和構建臨時文件路徑
            filename = file_path.name

            # 構建對應的臨時文件路徑
            temp_file_pattern = f"./temp_{filename}"

            # 提取原始文件名
            parts = filename.split('_')
            if len(parts) >= 3:
                original_name = '_'.join(parts[:-2])
                if '.' in parts[-1]:
                    ext = '.' + parts[-1].split('.')[-1]
                    original_name += ext
            else:
                original_name = filename

            # 獲取資料庫統計信息（先嘗試臨時文件路徑，再嘗試原始文件名）
            db_stats = db_manager.get_documents_by_source(temp_file_pattern)
            if not db_stats["exists"]:
                db_stats = db_manager.get_documents_by_source(original_name)

            files.append({
                "filename": filename,
                "original_name": original_name,
                "size": stat.st_size,
                "processed_at": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "database_stats": db_stats
            })

    return {"files": files, "total": len(files)}

@app.delete("/files/{filename}")
async def delete_file(filename: str):
    """刪除指定文件及其在資料庫中的所有相關記錄"""
    try:
        # 查找 output 目錄中的文件
        output_dir = Path("./data/output")
        file_path = output_dir / filename

        if not file_path.exists():
            raise HTTPException(status_code=404, detail="文件不存在")

        # 從 output 文件名構建對應的臨時文件路徑
        # output 文件名格式: catpdf_581c7ef6_20250705_222427.pdf
        # 資料庫來源格式: ./temp_catpdf_581c7ef6_20250705_222427.pdf

        # 直接在文件名前加上 "./temp_" 前綴
        temp_file_pattern = f"./temp_{filename}"

        # 也嘗試原始文件名（從文件名中提取）
        parts = filename.split('_')
        if len(parts) >= 3:
            original_name = '_'.join(parts[:-2])  # 移除最後兩部分（雜湊值和時間戳）
            if '.' in parts[-1]:
                ext = '.' + parts[-1].split('.')[-1]
                original_name += ext
        else:
            original_name = filename

        # 先嘗試使用臨時文件路徑查詢
        db_stats_before = db_manager.get_documents_by_source(temp_file_pattern)

        # 如果沒找到，嘗試使用原始文件名
        if not db_stats_before["exists"]:
            db_stats_before = db_manager.get_documents_by_source(original_name)

        if not db_stats_before["exists"]:
            # 如果資料庫中沒有記錄，只刪除文件
            file_path.unlink()
            return {
                "success": True,
                "message": "文件已刪除（資料庫中無相關記錄）",
                "deleted_file": filename,
                "database_deletions": {"deleted_parents": 0, "deleted_children": 0, "deleted_images": 0},
                "search_patterns": [temp_file_pattern, original_name]
            }

        # 確定要使用的刪除模式
        delete_pattern = temp_file_pattern if db_manager.get_documents_by_source(temp_file_pattern)["exists"] else original_name

        # 刪除資料庫中的相關記錄
        deletion_stats = db_manager.delete_documents_by_source(delete_pattern)

        # 刪除相關的圖片文件
        deleted_images = []
        photos_dir = Path("./data/照片")
        if photos_dir.exists():
            # 查找與此文件相關的圖片
            for img_file in photos_dir.glob("*"):
                if img_file.is_file() and original_name.split('.')[0] in img_file.name:
                    try:
                        img_file.unlink()
                        deleted_images.append(img_file.name)
                    except Exception as e:
                        print(f"⚠️ 刪除圖片文件失敗: {img_file.name}, 錯誤: {e}")

        # 刪除 output 目錄中的文件
        file_path.unlink()

        return {
            "success": True,
            "message": "文件及相關資料已完全刪除",
            "deleted_file": filename,
            "original_name": original_name,
            "delete_pattern_used": delete_pattern,
            "database_deletions": deletion_stats,
            "deleted_images": deleted_images,
            "stats_before_deletion": db_stats_before
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"刪除失敗: {str(e)}")

@app.get("/health")
async def health_check():
    """健康檢查接口"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "components": {
            "database": "connected" if db_manager else "disconnected",
            "embedding_generator": "ready" if embedding_generator else "not_ready",
            "image_processor": "ready" if image_processor else "not_ready"
        }
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
