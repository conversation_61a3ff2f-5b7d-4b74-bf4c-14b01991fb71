version: '3.8'

services:
  # PostgreSQL 資料庫
  postgres:
    image: pgvector/pgvector:pg16
    container_name: ocr-postgres
    environment:
      POSTGRES_DB: ${DB_NAME:-postgres}
      POSTGRES_USER: ${DB_USER:-postgres}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-rag}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - ocr-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-postgres} -d ${DB_NAME:-postgres}"]
      interval: 30s
      timeout: 10s
      retries: 5

  # OCR API 服務
  ocr-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ocr-api-server
    environment:
      - ENVIRONMENT=production
      - PORT=8080
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=${DB_NAME:-postgres}
      - DB_USER=${DB_USER:-postgres}
      - DB_PASSWORD=${DB_PASSWORD:-rag}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - CORS_ORIGINS=${CORS_ORIGINS:-*}
    ports:
      - "8080:8080"
    volumes:
      - ./data:/app/data
      - ./src/embeddings:/app/src/embeddings
    networks:
      - ocr-network
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Nginx 反向代理 (可選)
  nginx:
    image: nginx:alpine
    container_name: ocr-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    networks:
      - ocr-network
    depends_on:
      - ocr-api
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local

networks:
  ocr-network:
    driver: bridge
