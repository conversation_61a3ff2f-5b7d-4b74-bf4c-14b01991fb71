# Google Cloud Run 部署配置
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: ocr-api-server
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/cpu-throttling: "false"
        run.googleapis.com/memory: "4Gi"
        run.googleapis.com/cpu: "2000m"
        run.googleapis.com/max-scale: "10"
        run.googleapis.com/min-scale: "0"
        run.googleapis.com/startup-cpu-boost: "true"
    spec:
      containerConcurrency: 80
      timeoutSeconds: 900  # 15分鐘超時
      containers:
      - image: gcr.io/PROJECT_ID/ocr-api-server:latest
        ports:
        - name: http1
          containerPort: 8080
        env:
        # 應用配置
        - name: ENVIRONMENT
          value: "production"
        - name: PORT
          value: "8080"
        - name: PYTHONPATH
          value: "/app"
        
        # CORS 配置
        - name: CORS_ORIGINS
          value: "https://your-frontend.com,https://*.run.app"
        
        # 資料庫配置 (使用 Cloud SQL)
        - name: DB_HOST
          value: "/cloudsql/PROJECT_ID:REGION:INSTANCE_NAME"
        - name: DB_PORT
          value: "5432"
        - name: DB_NAME
          value: "ocr_db"
        - name: DB_USER
          value: "ocr_user"
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: db-password
              key: password
        
        # API Keys
        - name: GEMINI_API_KEY
          valueFrom:
            secretKeyRef:
              name: gemini-api-key
              key: api-key
        
        resources:
          limits:
            cpu: 2000m
            memory: 4Gi
          requests:
            cpu: 1000m
            memory: 2Gi
        
        # 健康檢查
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        # 掛載 Cloud SQL
        volumeMounts:
        - name: cloudsql
          mountPath: /cloudsql
          readOnly: true
      
      volumes:
      - name: cloudsql
        csi:
          driver: gcp-compute-persistent-disk-csi-driver
          readOnly: true
          volumeAttributes:
            secretName: cloudsql-instance-credentials
