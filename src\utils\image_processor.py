"""
image_processor.py

此模組提供了用於處理圖像的工具函式，特別是生成圖像的文本摘要。
"""

import base64
import io
from PIL import Image
import google.generativeai as genai
from src.config.settings import GEMINI_API_KEY # 引入 Gemini API Key

class ImageProcessor:
    """
    圖像處理工具類，提供圖像相關操作，例如生成圖像摘要。
    """

    def __init__(self):
        """
        初始化 ImageProcessor，配置 Google Gemini API。
        """
        genai.configure(api_key=GEMINI_API_KEY)
        self.model = genai.GenerativeModel('gemini-pro-vision') # 使用支援視覺的模型

    @staticmethod
    def _image_to_base64(image_path: str) -> str:
        """
        將圖像文件轉換為 Base64 編碼字串。

        Args:
            image_path (str): 圖像文件的路徑。

        Returns:
            str: Base64 編碼的圖像字串。
        """
        with Image.open(image_path) as image:
            buffered = io.BytesIO()
            image.save(buffered, format=image.format)
            img_str = base64.b64encode(buffered.getvalue())
            return img_str.decode('utf-8')

    def summarize_image(self, image_path: str, prompt: str = "請詳細描述這張圖片的內容。") -> str:
        """
        使用 Google Gemini API 生成圖像的文本摘要。

        Args:
            image_path (str): 圖像文件的路徑。
            prompt (str): 用於生成摘要的提示語。 (可調整參數)

        Returns:
            str: 圖像的文本摘要。
        """
        try:
            img_base64 = self._image_to_base64(image_path)
            image_parts = [
                {
                    "mime_type": f"image/{Image.open(image_path).format.lower()}",
                    "data": img_base64
                }
            ]
            response = self.model.generate_content([prompt, image_parts[0]])
            return response.text
        except Exception as e:
            print(f"生成圖像摘要時發生錯誤: {e}")
            return f"無法生成圖像摘要: {e}"