#!/bin/bash

# GCP 雲端部署腳本
# 支援 Cloud Run 和 Cloud SQL

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置變數
PROJECT_ID=${1:-"your-project-id"}
REGION=${2:-"asia-east1"}
SERVICE_NAME="ocr-api-server"
DB_INSTANCE_NAME="ocr-postgres"
DB_NAME="ocr_db"
DB_USER="ocr_user"

# 函數定義
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 檢查必要工具
check_tools() {
    log_info "檢查必要工具..."
    
    if ! command -v gcloud &> /dev/null; then
        log_error "gcloud CLI 未安裝"
        exit 1
    fi
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安裝"
        exit 1
    fi
    
    log_success "工具檢查通過"
}

# 設置 GCP 專案
setup_gcp() {
    log_info "設置 GCP 專案: $PROJECT_ID"
    gcloud config set project $PROJECT_ID
    
    log_info "啟用必要的 API..."
    gcloud services enable \
        cloudbuild.googleapis.com \
        run.googleapis.com \
        sql-component.googleapis.com \
        sqladmin.googleapis.com \
        secretmanager.googleapis.com
    
    log_success "GCP 設置完成"
}

# 創建 Cloud SQL 實例
create_database() {
    log_info "檢查 Cloud SQL 實例..."
    
    if gcloud sql instances describe $DB_INSTANCE_NAME --quiet 2>/dev/null; then
        log_warning "Cloud SQL 實例已存在: $DB_INSTANCE_NAME"
    else
        log_info "創建 Cloud SQL 實例..."
        gcloud sql instances create $DB_INSTANCE_NAME \
            --database-version=POSTGRES_15 \
            --tier=db-f1-micro \
            --region=$REGION \
            --storage-type=SSD \
            --storage-size=20GB \
            --backup-start-time=02:00 \
            --enable-bin-log \
            --maintenance-window-day=SUN \
            --maintenance-window-hour=03
        
        log_success "Cloud SQL 實例創建完成"
    fi
    
    # 創建資料庫
    log_info "創建資料庫: $DB_NAME"
    gcloud sql databases create $DB_NAME --instance=$DB_INSTANCE_NAME || true
    
    # 創建用戶
    log_info "創建資料庫用戶: $DB_USER"
    DB_PASSWORD=$(openssl rand -base64 32)
    gcloud sql users create $DB_USER \
        --instance=$DB_INSTANCE_NAME \
        --password=$DB_PASSWORD || true
    
    # 存儲密碼到 Secret Manager
    echo -n "$DB_PASSWORD" | gcloud secrets create db-password --data-file=-
    
    log_success "資料庫設置完成"
}

# 設置 Secrets
setup_secrets() {
    log_info "設置 Secret Manager..."
    
    # Gemini API Key
    if [ -z "$GEMINI_API_KEY" ]; then
        log_warning "請設置 GEMINI_API_KEY 環境變數"
        read -p "請輸入 Gemini API Key: " GEMINI_API_KEY
    fi
    
    echo -n "$GEMINI_API_KEY" | gcloud secrets create gemini-api-key --data-file=- || \
    echo -n "$GEMINI_API_KEY" | gcloud secrets versions add gemini-api-key --data-file=-
    
    log_success "Secrets 設置完成"
}

# 構建和推送映像
build_and_push() {
    log_info "構建 Docker 映像..."
    
    # 使用 Cloud Build
    gcloud builds submit --tag gcr.io/$PROJECT_ID/$SERVICE_NAME:latest
    
    log_success "映像構建完成"
}

# 部署到 Cloud Run
deploy_service() {
    log_info "部署到 Cloud Run..."
    
    # 獲取 Cloud SQL 連接名稱
    CONNECTION_NAME=$(gcloud sql instances describe $DB_INSTANCE_NAME --format="value(connectionName)")
    
    gcloud run deploy $SERVICE_NAME \
        --image gcr.io/$PROJECT_ID/$SERVICE_NAME:latest \
        --platform managed \
        --region $REGION \
        --allow-unauthenticated \
        --memory 4Gi \
        --cpu 2 \
        --max-instances 10 \
        --min-instances 0 \
        --concurrency 80 \
        --timeout 900 \
        --set-env-vars ENVIRONMENT=production \
        --set-env-vars PORT=8080 \
        --set-env-vars DB_HOST=/cloudsql/$CONNECTION_NAME \
        --set-env-vars DB_PORT=5432 \
        --set-env-vars DB_NAME=$DB_NAME \
        --set-env-vars DB_USER=$DB_USER \
        --set-secrets DB_PASSWORD=db-password:latest \
        --set-secrets GEMINI_API_KEY=gemini-api-key:latest \
        --add-cloudsql-instances $CONNECTION_NAME
    
    log_success "Cloud Run 部署完成"
}

# 測試部署
test_deployment() {
    log_info "測試部署..."
    
    SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --region=$REGION --format='value(status.url)')
    
    log_info "服務 URL: $SERVICE_URL"
    
    # 健康檢查
    if curl -f "$SERVICE_URL/health" > /dev/null 2>&1; then
        log_success "健康檢查通過"
    else
        log_error "健康檢查失敗"
        return 1
    fi
    
    log_success "部署測試完成"
    echo "🌐 服務 URL: $SERVICE_URL"
}

# 主函數
main() {
    if [ $# -lt 2 ]; then
        echo "使用方法: $0 PROJECT_ID REGION"
        echo "例如: $0 my-project asia-east1"
        exit 1
    fi
    
    log_info "開始 GCP 部署流程..."
    
    check_tools
    setup_gcp
    create_database
    setup_secrets
    build_and_push
    deploy_service
    test_deployment
    
    log_success "🎉 部署完成！"
}

# 執行主函數
main "$@"
