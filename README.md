# 🚀 OCR 智能文件處理系統

一個基於 FastAPI 的智能文件處理系統，支援 PDF 和 TXT 文件的自動處理、向量化存儲和智能搜索。

## 📁 專案結構

```
OCR/
├── 📄 核心程式
│   ├── main.py                    # 主程式入口 (PDF 處理)
│   ├── main_txt.py               # TXT 文件處理程式
│   ├── main_unified.py           # 統一處理程式
│   ├── api_server.py             # FastAPI Web 服務器
│   └── test_upload.html          # 網頁上傳界面
│
├── 📁 資料目錄 (data/)
│   ├── input/                    # 輸入文件
│   ├── output/                   # 處理完成的文件
│   ├── 檔案/                     # 原始文件存放
│   └── 照片/                     # 提取的圖片存放
│
├── 📁 源代碼 (src/)
│   ├── config/                   # 配置文件
│   │   ├── settings.py          # 資料庫設定
│   │   └── poppler_config.py    # Poppler 工具配置
│   ├── text_splitters/          # 文本分割器
│   │   ├── base_splitter.py     # 基礎分割器
│   │   ├── pdf_table_splitter.py # PDF 表格分割器
│   │   └── txt_splitter.py      # TXT 分割器
│   ├── utils/                   # 工具類
│   │   └── document_model.py    # 文檔模型
│   ├── database/                # 資料庫管理
│   │   └── db_manager.py        # PostgreSQL 管理器
│   ├── embeddings/              # 向量嵌入
│   │   ├── embedding_generator.py # BGE 中文模型
│   │   └── bge-base-zh-v1.5/    # 預訓練模型
│   └── image_processing/        # 圖片處理
│       ├── image_extractor.py   # 圖片提取器
│       ├── image_processor.py   # 圖片處理器
│       └── gemini_captioner.py  # Gemini AI 描述生成
│
├── 📁 依賴管理
│   ├── pyproject.toml           # Poetry 配置
│   └── poetry.lock              # 鎖定版本
```

## ✨ 主要功能

### 🔄 文件處理
- **PDF 處理**: 自動提取文字、表格和圖片
- **TXT 處理**: 智能標題檢測和段落分割
- **防重複上傳**: MD5 雜湊值檢測重複文件
- **統一輸出管理**: 處理完的文件存放在 `data/output/`

### 🤖 AI 功能
- **中文向量化**: 使用 BGE-base-zh-v1.5 模型
- **圖片描述**: Gemini AI 生成中文圖片描述
- **智能分割**: 按標題、段落自動分割文檔

### 💾 資料庫管理
- **PostgreSQL + pgvector**: 高效向量存儲和搜索
- **層級結構**: 父文檔 → 子文檔 → 圖片記錄
- **完整刪除**: 一鍵刪除文件及所有相關記錄

### 🌐 Web 界面
- **FastAPI 後端**: RESTful API 接口
- **拖拽上傳**: 友好的網頁上傳界面
- **實時統計**: 顯示處理進度和資料庫統計
- **安全刪除**: 確認對話框防止誤刪

## 🚀 快速開始

### 1. 安裝依賴
```bash
poetry install
```

### 2. 配置環境
創建 `.env` 文件並設置：
```env
DB_HOST=localhost
DB_PORT=5432
DB_USER=your_username
DB_PASSWORD=your_password
DB_NAME=ocr_database
GEMINI_API_KEY=your_gemini_api_key
```

### 3. 啟動服務
```bash
# 啟動 FastAPI 服務器
poetry run python api_server.py

# 或直接處理文件
poetry run python main.py
```

### 4. 訪問界面
打開 `test_upload.html` 或訪問 `http://localhost:8000/docs`

## 📊 API 接口

| 接口 | 方法 | 功能 |
|------|------|------|
| `/upload` | POST | 上傳並處理文件 |
| `/files` | GET | 列出已處理文件 |
| `/files/{filename}` | DELETE | 刪除文件及相關記錄 |
| `/health` | GET | 健康檢查 |
| `/docs` | GET | API 文檔 |

## 🛠️ 技術棧

- **後端**: FastAPI, Python 3.10+
- **資料庫**: PostgreSQL + pgvector
- **AI 模型**: BGE-base-zh-v1.5, Gemini AI
- **文件處理**: Poppler, Tesseract OCR
- **依賴管理**: Poetry

## 📝 使用說明

1. **上傳文件**: 支援 PDF、TXT 格式
2. **自動處理**: 系統自動判斷文件類型並處理
3. **查看結果**: 在網頁界面查看處理統計
4. **管理文件**: 可以查看和刪除已處理的文件
5. **搜索功能**: 基於向量相似度的智能搜索

## 🔧 維護

- 所有測試文件已清理，保持專案結構乾淨
- 核心功能集中在主要程式文件中
- 配置文件統一管理在 `src/config/` 目錄
