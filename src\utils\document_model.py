# Document model for representing documents in the system
from dataclasses import dataclass
from typing import List, Optional, Dict, Any
from datetime import datetime

@dataclass
class Document:
    """Represents a document in the OCR system"""
    id: Optional[int] = None
    filename: str = ""
    content: str = ""
    page_content: str = ""  # Add page_content for compatibility with PDFTableSplitter
    file_path: str = ""
    file_type: str = ""
    created_at: Optional[datetime] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        if self.created_at is None:
            self.created_at = datetime.now()
        # Sync content and page_content
        if self.page_content and not self.content:
            self.content = self.page_content
        elif self.content and not self.page_content:
            self.page_content = self.content
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert document to dictionary"""
        return {
            'id': self.id,
            'filename': self.filename,
            'content': self.content,
            'file_path': self.file_path,
            'file_type': self.file_type,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Document':
        """Create document from dictionary"""
        doc = cls()
        doc.id = data.get('id')
        doc.filename = data.get('filename', '')
        doc.content = data.get('content', '')
        doc.file_path = data.get('file_path', '')
        doc.file_type = data.get('file_type', '')
        doc.metadata = data.get('metadata', {})
        
        created_at_str = data.get('created_at')
        if created_at_str:
            doc.created_at = datetime.fromisoformat(created_at_str)
        
        return doc
    
    def get_text_length(self) -> int:
        """Get the length of the document content"""
        return len(self.content)
    
    def add_metadata(self, key: str, value: Any):
        """Add metadata to the document"""
        self.metadata[key] = value
    
    def get_metadata(self, key: str, default: Any = None) -> Any:
        """Get metadata value by key"""
        return self.metadata.get(key, default)
