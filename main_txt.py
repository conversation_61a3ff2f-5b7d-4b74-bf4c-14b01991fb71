"""
TXT 文件處理主程式
支援與 PDF 相同的處理邏輯：Parent Documents + Child Documents + 向量化 + 資料庫存儲
"""
import os
from src.text_splitters.txt_splitter import TXTSplitter
from src.utils.document_model import Document
from src.database.db_manager import DBManager
from src.embeddings.embedding_generator import EmbeddingGenerator
from langchain_text_splitters import RecursiveCharacterTextSplitter

def main():
    """
    主函數，用於演示 TXT 文件處理，並將結果存入資料庫
    """
    txt_file_path = "./data/檔案/sample.txt"  # 使用者提供的 TXT 路徑

    if not os.path.exists(txt_file_path):
        print(f"錯誤: 文件不存在於 {txt_file_path}")
        print("請將 TXT 文件放入 ./data/檔案/ 目錄中")
        return
    
    # 初始化資料庫管理器和嵌入生成器
    db_manager = DBManager()
    embedding_generator = EmbeddingGenerator()
    
    # 創建資料庫表
    db_manager.create_tables()
    
    print(f"正在處理 TXT 文件: {txt_file_path}")
    
    # 預覽可能的標題
    print("\n--- 預覽可能的標題 ---")
    splitter_preview = TXTSplitter()
    potential_titles = splitter_preview.get_title_preview(txt_file_path)
    
    if potential_titles:
        print("檢測到可能的標題:")
        for title in potential_titles[:10]:  # 只顯示前10個
            print(f"  {title}")
        
        use_title_split = input("\n是否使用標題分割？(y/N): ").lower() == 'y'
    else:
        print("未檢測到明顯的標題結構，將使用段落分割")
        use_title_split = False
    
    # 演示基於標題的分割 (作為 Parent Documents)
    print(f"\n--- 演示 TXT 分割 ({'按標題' if use_title_split else '按段落'}) (作為 Parent Documents) ---")
    splitter_by_title = TXTSplitter(split_by_title=use_title_split)
    parent_documents = splitter_by_title.split_document(txt_file_path)
    print(f"\n分割完成，共 {len(parent_documents)} 個 Parent Document 物件。")
    
    # 初始化逐元素拆分器 (用於生成 Child Documents)
    child_splitter = RecursiveCharacterTextSplitter(
        chunk_size=500,  # 可以根據需求調整
        chunk_overlap=100
    )
    
    # 收集所有存儲的父文檔ID
    stored_parent_ids = []
    
    for i, parent_doc in enumerate(parent_documents):
        title = parent_doc.metadata.get('title', f'段落 {i+1}')
        line_info = ""
        if 'line_start' in parent_doc.metadata:
            line_info = f" (第{parent_doc.metadata['line_start']+1}-{parent_doc.metadata['line_end']+1}行)"
        
        print(f"\n--- Parent Document {i+1}: {title}{line_info} ---")
        print(f"內容 (前200字): {parent_doc.page_content[:200]}...")
        print(f"元數據: {parent_doc.metadata}")
        
        # 將 Parent Document 存入資料庫
        parent_id = db_manager.insert_parent_document(parent_doc)
        stored_parent_ids.append(parent_id)
        
        # 對每個 Parent Document 進行逐元素拆分，生成 Child Documents
        print(f"  正在對 Parent Document (ID: {parent_id}) 進行逐元素拆分...")
        child_texts = child_splitter.split_text(parent_doc.page_content)
        child_documents = []
        
        for j, child_text in enumerate(child_texts):
            # 創建 Child Document
            child_doc = Document(
                page_content=child_text,
                metadata={
                    'source': txt_file_path,
                    'type': 'txt_child_text',
                    'title': title,
                    'chunk_index': parent_doc.metadata.get('chunk_index', i),
                    'parent_id': parent_id,
                    'child_chunk_index': j
                }
            )
            child_documents.append(child_doc)
        
        print(f"  拆分完成，共 {len(child_documents)} 個 Child Document 物件。")
        
        # 為每個 Child Document 生成嵌入向量並存入資料庫
        for k, child_doc in enumerate(child_documents):
            print(f"    --- Child Document {k+1} ---")
            print(f"    內容 (前100字): {child_doc.page_content[:100]}...")
            print(f"    元數據: {child_doc.metadata}")
            
            # 生成嵌入向量
            embedding = embedding_generator.generate_embeddings([child_doc.page_content])[0]
            print(f"    嵌入維度: {len(embedding)}")
            
            # 存入資料庫
            db_manager.insert_child_document(parent_id, child_doc, embedding)
    
    # 顯示處理統計
    print(f"\n=== 處理完成統計 ===")
    print(f"✓ 處理文件: {txt_file_path}")
    print(f"✓ Parent Documents: {len(parent_documents)} 個")
    
    total_child_docs = 0
    for parent_doc in parent_documents:
        child_texts = child_splitter.split_text(parent_doc.page_content)
        total_child_docs += len(child_texts)
    
    print(f"✓ Child Documents: {total_child_docs} 個")
    print(f"✓ 向量維度: 768 (BGE 中文模型)")
    print(f"✓ 資料庫存儲: PostgreSQL + pgvector")
    
    # 演示原始的段落分割 (不存入資料庫)
    print("\n--- 原始的 TXT 分割 (段落模式，不存入資料庫) ---")
    splitter_default = TXTSplitter(split_by_title=False)
    documents_default = splitter_default.split_document(txt_file_path)
    print(f"\n分割完成 (段落模式)，共 {len(documents_default)} 個 Document 物件。")
    
    for i, doc in enumerate(documents_default[:5]):  # 只顯示前5個
        print(f"\n--- Document {i+1} ---")
        print(f"內容 (前200字): {doc.page_content[:200]}...")
        print(f"元數據: {doc.metadata}")
    
    if len(documents_default) > 5:
        print(f"\n... 還有 {len(documents_default) - 5} 個文檔")

def create_sample_txt():
    """創建一個示例 TXT 文件用於測試"""
    sample_content = """貓咪飼養指南

第一章 貓咪基本認識

一、貓咪的生理特徵

貓咪是純肉食性動物，具有敏銳的感官和獨特的生理結構。牠們的視覺在夜間特別敏銳，能夠在微弱的光線下清楚地看到物體。

貓咪的聽覺也非常發達，能夠聽到人類無法察覺的高頻聲音。這種能力幫助牠們在野外捕獵小型獵物。

二、貓咪的行為特徵

貓咪具有強烈的領域性，會通過氣味標記來宣示自己的領域。牠們也是非常愛乾淨的動物，每天會花費大量時間進行理毛。

第二章 貓咪的飼養環境

一、室內環境設置

為貓咪提供一個安全、舒適的室內環境是非常重要的。環境中應該包含以下基本設施：

1. 貓砂盆：應放置在安靜、通風良好的地方
2. 食物和水碗：應與貓砂盆保持適當距離
3. 休息區域：提供溫暖、安靜的休息空間

二、玩具和娛樂設施

貓咪需要適當的運動和娛樂來保持身心健康。可以提供各種玩具，如：

- 逗貓棒
- 小球
- 貓抓板
- 貓跳台

第三章 貓咪的飲食管理

一、營養需求

貓咪作為純肉食性動物，需要高蛋白質的飲食。牠們無法自行合成某些必需的營養素，如牛磺酸，因此必須從食物中獲取。

二、餵食方式

可以採用定時餵食或自由餵食的方式，但需要根據貓咪的年齡、健康狀況和生活習慣來決定最適合的餵食方法。

結語

飼養貓咪是一項長期的責任，需要飼主投入時間、精力和愛心。只有提供適當的照顧，貓咪才能健康快樂地生活。"""
    
    # 確保目錄存在
    os.makedirs("./data/檔案", exist_ok=True)

    # 寫入示例文件
    with open("./data/檔案/sample.txt", "w", encoding="utf-8") as f:
        f.write(sample_content)

    print("✓ 已創建示例 TXT 文件: ./data/檔案/sample.txt")

if __name__ == "__main__":
    # 如果示例文件不存在，創建一個
    if not os.path.exists("./data/檔案/sample.txt"):
        print("示例 TXT 文件不存在，正在創建...")
        create_sample_txt()
    
    main()
