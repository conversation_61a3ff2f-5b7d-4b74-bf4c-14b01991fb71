# Base splitter class for text splitting functionality
from abc import ABC, abstractmethod
from typing import List, Any

class BaseTextSplitter(ABC):
    """Abstract base class for text splitters"""

    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap

    @abstractmethod
    def split_document(self, file_path: str) -> List[Any]:
        """Split document into chunks"""
        pass

    def get_chunk_size(self) -> int:
        """Get the chunk size for splitting"""
        return self.chunk_size

    def get_chunk_overlap(self) -> int:
        """Get the chunk overlap for splitting"""
        return self.chunk_overlap
