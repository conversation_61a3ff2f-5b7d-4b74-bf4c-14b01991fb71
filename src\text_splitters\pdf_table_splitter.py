"""
pdf_table_splitter.py

此模組定義了用於 PDF (含表格) 文件的文本分割器。
"""

from typing import List, Set
from unstructured.documents.elements import Element, Text, Table, Title, NarrativeText
from src.text_splitters.base_splitter import BaseTextSplitter
from src.utils.document_model import Document
from src.utils.file_reader import FileReader

class PDFTableSplitter(BaseTextSplitter):
    """
    PDF 文件文本分割器，能夠提取文本和表格內容，並可選地根據標題進行分割。
    """
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200, split_by_title: bool = False):
        """
        初始化 PDFTableSplitter。

        Args:
            chunk_size (int): 每個文本塊的最大字元數。 (可調整參數)
            chunk_overlap (int): 相鄰文本塊之間的重疊字元數。 (可調整參數)
            split_by_title (bool): 是否根據標題進行分割。如果為 True，則每個大主題（標題）及其後續內容將被視為一個 Document。
        """
        super().__init__(chunk_size, chunk_overlap)
        self.split_by_title = split_by_title
        # 對於 PDF，LangChain 的 TextSplitter 可能不適用於 Unstructured 提取的元素
        # 因此這裡不直接使用 LangChain 的 splitter，而是手動處理元素

    def split_document(self, file_path: str) -> List[Document]:
        """
        從指定 PDF 文件路徑讀取內容並進行文本分割，提取文本和表格。
        如果 split_by_title 為 True，則會嘗試根據標題進行分割。

        Args:
            file_path (str): 待分割 PDF 文件的路徑。

        Returns:
            List[Document]: 包含分割後文本塊及其元數據的 Document 物件列表。
        """
        # 確保調用 FileReader.read_pdf_file 時傳遞所有預期的參數
        elements = FileReader.read_pdf_file(
            file_path=file_path,
            strategy="hi_res",
            infer_table_structure=True,
            model_name="yolox"
        )
        
        documents = []
        current_chunk_content = []
        current_chunk_pages = set() # 用於收集當前塊涉及的頁碼
        current_chunk_metadata = {"source": file_path, "type": "pdf_text"}
        chunk_index_counter = 0

        for i, element in enumerate(elements):
            page_number = element.metadata.page_number if hasattr(element.metadata, 'page_number') else None
            if page_number:
                current_chunk_pages.add(page_number)

            if isinstance(element, Title) and self.split_by_title:
                # 如果遇到標題且啟用按標題分割，則將當前累積的內容作為一個 Document
                if current_chunk_content:
                    pages_str = ",".join(map(str, sorted(list(current_chunk_pages))))
                    documents.append(Document(
                        page_content="\n".join(current_chunk_content).strip(),
                        metadata={**current_chunk_metadata, "chunk_index": chunk_index_counter, "pages": pages_str}
                    ))
                    chunk_index_counter += 1
                    current_chunk_content = [] # 重置內容
                    current_chunk_pages = set() # 重置頁碼集合
                
                # 將標題本身作為一個新的 Document 的開始，並將其內容加入
                current_chunk_content.append(element.text.strip())
                current_chunk_metadata["title"] = element.text.strip() # 將標題加入元數據
                current_chunk_metadata["type"] = "pdf_title_section" # 標記為標題區段
                if page_number:
                    current_chunk_pages.add(page_number) # 將標題頁碼加入

            elif isinstance(element, (Text, Table)):
                content = ""
                if isinstance(element, Text):
                    content = element.text.strip()
                elif isinstance(element, Table):
                    # 優先使用 Markdown 格式的表格文本，如果沒有則使用 HTML，最後是原始字符串
                    content = element.metadata.text_as_markdown if hasattr(element.metadata, 'text_as_markdown') and element.metadata.text_as_markdown else \
                              (element.metadata.text_as_html if hasattr(element.metadata, 'text_as_html') and element.metadata.text_as_html else str(element))
                    
                    # 將表格的元數據添加到當前塊的元數據中
                    table_metadata = {
                        "table_id": element.id,
                        "table_coordinates": str(element.metadata.coordinates) if hasattr(element.metadata, 'coordinates') else None,
                        "table_header_row": element.metadata.header_row if hasattr(element.metadata, 'header_row') else None,
                        "text_as_html": element.metadata.text_as_html if hasattr(element.metadata, 'text_as_html') else None,
                        "text_as_markdown": element.metadata.text_as_markdown if hasattr(element.metadata, 'text_as_markdown') else None,
                        "table_type": "table" # 標記為表格類型
                    }
                    current_chunk_metadata.update({k: v for k, v in table_metadata.items() if v is not None})
                
                if content:
                    current_chunk_content.append(content)
                    # 如果沒有啟用按標題分割，或者當前元素不是標題，則直接添加為獨立 Document
                    if not self.split_by_title:
                        pages_str = str(page_number) if page_number else "N/A"
                        documents.append(Document(
                            page_content=content,
                            metadata={"source": file_path, "type": "pdf_text", "chunk_index": chunk_index_counter, "pages": pages_str}
                        ))
                        chunk_index_counter += 1
        
        # 添加最後一個累積的 Document (如果有的話)
        if current_chunk_content and self.split_by_title:
            pages_str = ",".join(map(str, sorted(list(current_chunk_pages))))
            documents.append(Document(
                page_content="\n".join(current_chunk_content).strip(),
                metadata={**current_chunk_metadata, "chunk_index": chunk_index_counter, "pages": pages_str}
            ))

        # 如果沒有啟用按標題分割，則使用原始的逐元素分割邏輯 (此處已在循環中處理，此塊應為空)
        # 為了避免重複添加，此處邏輯應移除或確保不會重複執行
        # 原始的逐元素分割邏輯已經在上面的 `if not self.split_by_title:` 塊中處理了
        # 所以這裡不需要額外的處理
        return documents