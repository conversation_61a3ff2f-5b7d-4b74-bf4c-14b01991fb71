# Embedding generator for creating vector embeddings
from typing import List, Any
import numpy as np

class EmbeddingGenerator:
    """Generates embeddings for text and documents"""

    def __init__(self, model_name: str = "./src/embeddings/bge-base-zh-v1.5", target_dimensions: int = 768):
        self.model_name = model_name
        self.target_dimensions = target_dimensions
        self.model = None
        self.actual_dimensions = None
        self._load_model()
    
    def _load_model(self):
        """Load the embedding model"""
        try:
            from sentence_transformers import SentenceTransformer
            self.model = SentenceTransformer(self.model_name)

            # 檢查模型的實際維度
            test_embedding = self.model.encode("test")
            self.actual_dimensions = len(test_embedding)

            print(f"✓ 嵌入生成器初始化完成")
            print(f"  模型: {self.model_name}")
            print(f"  實際維度: {self.actual_dimensions}")
            print(f"  目標維度: {self.target_dimensions}")

            if self.actual_dimensions != self.target_dimensions:
                print(f"⚠️ 維度不匹配，將使用維度轉換")

        except ImportError:
            print("sentence-transformers not installed. Please install it to use embeddings.")
            self.model = None
    
    def _adjust_dimensions(self, embedding: List[float]) -> List[float]:
        """調整向量維度到目標維度"""
        if len(embedding) == self.target_dimensions:
            return embedding
        elif len(embedding) < self.target_dimensions:
            # 如果維度不足，用零填充
            padding = [0.0] * (self.target_dimensions - len(embedding))
            return embedding + padding
        else:
            # 如果維度過多，截斷到目標維度
            return embedding[:self.target_dimensions]

    def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for a list of texts"""
        if self.model is None:
            # Return dummy embeddings if model is not available
            return [[0.0] * self.target_dimensions for _ in texts]

        embeddings = self.model.encode(texts)

        # 調整維度
        adjusted_embeddings = []
        for embedding in embeddings:
            adjusted = self._adjust_dimensions(embedding.tolist())
            adjusted_embeddings.append(adjusted)

        return adjusted_embeddings
    
    def generate_single_embedding(self, text: str) -> List[float]:
        """Generate embedding for a single text"""
        return self.generate_embeddings([text])[0]
    
    def similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """Calculate cosine similarity between two embeddings"""
        vec1 = np.array(embedding1)
        vec2 = np.array(embedding2)
        
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return dot_product / (norm1 * norm2)
