//========================================================================
//
// PSTokenizer.h
//
// Copyright 2002-2003 Glyph & Cog, LLC
//
//========================================================================

//========================================================================
//
// Modified under the Poppler project - http://poppler.freedesktop.org
//
// All changes made under the Poppler project to this file are licensed
// under GPL version 2 or later
//
// Copyright (C) 2006 <PERSON> <<EMAIL>>
//
// To see a description of the changes please see the Changelog file that
// came with your tarball or type make ChangeLog if you are building from git
//
//========================================================================

#ifndef PSTOKENIZER_H
#define PSTOKENIZER_H

//------------------------------------------------------------------------

class PSTokenizer
{
public:
    PSTokenizer(int (*getCharFuncA)(void *), void *dataA);
    ~PSTokenizer();

    // Get the next PostScript token.  Returns false at end-of-stream.
    bool getToken(char *buf, int size, int *length);

private:
    int lookChar();
    void consumeChar();
    int getChar();

    int (*getCharFunc)(void *);
    void *data;
    int charBuf;
};

#endif
