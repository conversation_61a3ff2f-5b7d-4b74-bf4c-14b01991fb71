-- 初始化資料庫腳本
-- 啟用 pgvector 擴展
CREATE EXTENSION IF NOT EXISTS vector;

-- 設置編碼
SET client_encoding = 'UTF8';

-- 創建 parent_documents 表格
CREATE TABLE IF NOT EXISTS parent_documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    file_path TEXT NOT NULL,
    title TEXT,
    page_content TEXT NOT NULL,
    document_metadata JSONB,
    image_count INTEGER DEFAULT 0,
    image_paths JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 創建 child_documents 表格
CREATE TABLE IF NOT EXISTS child_documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    parent_id UUID REFERENCES parent_documents(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    document_metadata JSONB,
    embedding VECTOR(768),
    content_type TEXT DEFAULT 'text',
    image_path TEXT,
    image_description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 創建向量搜索索引
CREATE INDEX IF NOT EXISTS child_documents_embedding_idx
ON child_documents USING ivfflat (embedding vector_cosine_ops);

-- 創建其他索引
CREATE INDEX IF NOT EXISTS parent_documents_file_path_idx ON parent_documents(file_path);
CREATE INDEX IF NOT EXISTS child_documents_parent_id_idx ON child_documents(parent_id);
CREATE INDEX IF NOT EXISTS child_documents_content_type_idx ON child_documents(content_type);

-- 插入測試資料 (可選)
-- INSERT INTO parent_documents (file_path, title, page_content, document_metadata)
-- VALUES ('test.pdf', '測試文檔', '這是一個測試文檔', '{"source": "test.pdf", "type": "pdf"}');

COMMIT;
