[project]
name = "ocr"
version = "0.1.0"
description = ""
authors = [
    {name = "<PERSON>",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    # Web 框架
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "python-multipart>=0.0.6",

    # 文件處理
    "unstructured[pdf]>=0.18.2",
    "langchain-text-splitters>=0.0.1",

    # 資料庫
    "psycopg2-binary>=2.9.0",
    "sqlalchemy>=1.4.0",

    # AI/ML
    "sentence-transformers>=2.0.0",
    "torch>=2.0.0",
    "transformers>=4.30.0",

    # 數據處理
    "numpy>=1.20.0",
    "pandas>=1.3.0",
    "pillow>=9.0.0",

    # 其他
    "python-dotenv>=1.0.0",
    "requests>=2.28.0"
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
