[project]
name = "ocr"
version = "0.1.0"
description = ""
authors = [
    {name = "<PERSON>",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "unstructured[pdf]>=0.18.2",
    "psycopg2-binary>=2.9.0",
    "sqlalchemy>=1.4.0",
    "langchain-text-splitters>=0.0.1",
    "sentence-transformers>=2.0.0",
    "numpy>=1.20.0",
    "pandas>=1.3.0"
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
