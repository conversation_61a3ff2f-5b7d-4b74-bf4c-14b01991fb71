"""
圖片提取模組
從 PDF 文件中提取圖片並保存到指定目錄
"""
import os
import fitz  # PyMuPDF
from typing import List, Dict, Tuple
from PIL import Image
import hashlib
from datetime import datetime

class ImageExtractor:
    """PDF 圖片提取器"""
    
    def __init__(self, output_dir: str = "./data/images"):
        """
        初始化圖片提取器
        
        Args:
            output_dir: 圖片保存目錄
        """
        self.output_dir = output_dir
        self.ensure_output_dir()
    
    def ensure_output_dir(self):
        """確保輸出目錄存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir, exist_ok=True)
            print(f"✓ 創建圖片目錄: {self.output_dir}")
    
    def extract_images_from_pdf(self, pdf_path: str) -> List[Dict]:
        """
        從 PDF 中提取所有圖片
        
        Args:
            pdf_path: PDF 文件路徑
            
        Returns:
            List[Dict]: 圖片信息列表，包含路徑、頁面、尺寸等
        """
        extracted_images = []
        
        try:
            # 打開 PDF 文件
            pdf_document = fitz.open(pdf_path)
            pdf_name = os.path.splitext(os.path.basename(pdf_path))[0]
            
            print(f"正在從 {pdf_path} 提取圖片...")
            
            for page_num in range(len(pdf_document)):
                page = pdf_document[page_num]
                
                # 獲取頁面中的圖片列表
                image_list = page.get_images()
                
                for img_index, img in enumerate(image_list):
                    try:
                        # 提取圖片數據
                        xref = img[0]
                        pix = fitz.Pixmap(pdf_document, xref)
                        
                        # 跳過太小的圖片（可能是裝飾性圖片）
                        if pix.width < 50 or pix.height < 50:
                            pix = None
                            continue
                        
                        # 生成唯一的文件名
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        image_filename = f"{pdf_name}_page{page_num+1}_img{img_index+1}_{timestamp}.png"
                        image_path = os.path.join(self.output_dir, image_filename)
                        
                        # 保存圖片
                        if pix.n - pix.alpha < 4:  # GRAY or RGB
                            pix.save(image_path)
                        else:  # CMYK: 轉換為 RGB
                            pix1 = fitz.Pixmap(fitz.csRGB, pix)
                            pix1.save(image_path)
                            pix1 = None
                        
                        # 記錄圖片信息
                        image_info = {
                            'path': image_path,
                            'filename': image_filename,
                            'page_number': page_num + 1,
                            'width': pix.width,
                            'height': pix.height,
                            'size_bytes': os.path.getsize(image_path),
                            'pdf_source': pdf_path,
                            'extracted_at': datetime.now().isoformat()
                        }
                        
                        extracted_images.append(image_info)
                        print(f"  ✓ 提取圖片: 第{page_num+1}頁, {pix.width}x{pix.height}px -> {image_filename}")
                        
                        pix = None
                        
                    except Exception as e:
                        print(f"  ✗ 提取第{page_num+1}頁圖片{img_index+1}失敗: {e}")
                        continue
            
            pdf_document.close()
            print(f"✓ 圖片提取完成，共提取 {len(extracted_images)} 張圖片")
            
        except Exception as e:
            print(f"✗ PDF 圖片提取失敗: {e}")
            return []
        
        return extracted_images
    
    def get_image_hash(self, image_path: str) -> str:
        """
        計算圖片的 MD5 哈希值，用於去重
        
        Args:
            image_path: 圖片路徑
            
        Returns:
            str: MD5 哈希值
        """
        try:
            with open(image_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except Exception as e:
            print(f"計算圖片哈希失敗: {e}")
            return ""
    
    def remove_duplicate_images(self, image_list: List[Dict]) -> List[Dict]:
        """
        移除重複的圖片
        
        Args:
            image_list: 圖片信息列表
            
        Returns:
            List[Dict]: 去重後的圖片列表
        """
        unique_images = []
        seen_hashes = set()
        
        for img_info in image_list:
            img_hash = self.get_image_hash(img_info['path'])
            if img_hash and img_hash not in seen_hashes:
                seen_hashes.add(img_hash)
                img_info['hash'] = img_hash
                unique_images.append(img_info)
            elif img_hash in seen_hashes:
                # 刪除重複的圖片文件
                try:
                    os.remove(img_info['path'])
                    print(f"  ✓ 刪除重複圖片: {img_info['filename']}")
                except Exception as e:
                    print(f"  ✗ 刪除重複圖片失敗: {e}")
        
        print(f"✓ 去重完成，保留 {len(unique_images)} 張唯一圖片")
        return unique_images
    
    def extract_and_deduplicate(self, pdf_path: str) -> List[Dict]:
        """
        提取圖片並去重
        
        Args:
            pdf_path: PDF 文件路徑
            
        Returns:
            List[Dict]: 去重後的圖片信息列表
        """
        images = self.extract_images_from_pdf(pdf_path)
        if images:
            images = self.remove_duplicate_images(images)
        return images
