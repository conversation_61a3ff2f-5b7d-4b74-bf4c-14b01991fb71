"""
file_reader.py

此模組提供了用於讀取不同文件類型的工具函式。
"""

from typing import List, Dict, Any
import pypdf
try:
    from unstructured.partition.pdf import partition_pdf
    from unstructured.documents.elements import Element
    from src.config.poppler_config import setup_all_tools
    UNSTRUCTURED_AVAILABLE = True
    # 在模組載入時設置所有工具路徑
    setup_all_tools()
except ImportError:
    UNSTRUCTURED_AVAILABLE = False
    print("警告: unstructured 庫不可用，將使用簡化的 PDF 處理")

class FileReader:
    """
    文件讀取工具類，提供讀取不同文件格式的方法。
    """

    @staticmethod
    def read_text_file(file_path: str) -> str:
        """
        讀取純文本文件內容。

        Args:
            file_path (str): 純文本文件的路徑。

        Returns:
            str: 文件的全部內容。
        """
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()

    @staticmethod
    def read_pdf_file(
        file_path: str,
        extract_images: bool = False,
        image_output_dir: str = None,
        strategy: str = "hi_res", # 添加 strategy 參數
        infer_table_structure: bool = True, # 添加 infer_table_structure 參數
        model_name: str = "yolox", # 添加 model_name 參數
        chunking_strategy: str = None, # 添加 chunking_strategy 參數
        max_characters: int = None, # 添加 max_characters 參數
        new_after_n_chars: int = None, # 添加 new_after_n_chars 參數
        combine_text_under_n_chars: int = None # 添加 combine_text_under_n_chars 參數
    ) -> List[Element]:
        """
        讀取 PDF 文件內容並提取元素，可選地提取圖像。

        Args:
            file_path (str): PDF 文件的路徑。
            extract_images (bool): 是否從 PDF 中提取圖像。
            image_output_dir (str, optional): 提取圖像的輸出目錄。如果 extract_images 為 True 且未提供，則圖像將不會被保存到磁碟。
            strategy (str): Unstructured 的處理策略，例如 "hi_res"。
            infer_table_structure (bool): 是否推斷表格結構。
            model_name (str): 用於表格和佈局識別的模型名稱，例如 "yolox"。
            chunking_strategy (str, optional): Unstructured 的分塊策略。
            max_characters (int, optional): 分塊時的最大字元數。
            new_after_n_chars (int, optional): 在多少字元後嘗試創建新塊。
            combine_text_under_n_chars (int, optional): 將小於此字元數的文本塊合併。

        Returns:
            List[Element]: 從 PDF 中提取的元素列表 (Unstructured Element 物件)。
        """
        # 構建傳遞給 partition_pdf 的參數字典
        partition_kwargs = {
            "filename": file_path,
            "extract_images_in_pdf": extract_images,
            "image_output_dir_path": image_output_dir,
            "strategy": strategy,
            "infer_table_structure": infer_table_structure,
            "model_name": model_name,
        }

        # 根據需要添加分塊策略參數
        if chunking_strategy:
            partition_kwargs["chunking_strategy"] = chunking_strategy
        if max_characters:
            partition_kwargs["max_characters"] = max_characters
        if new_after_n_chars:
            partition_kwargs["new_after_n_chars"] = new_after_n_chars
        if combine_text_under_n_chars:
            partition_kwargs["combine_text_under_n_chars"] = combine_text_under_n_chars

        elements = partition_pdf(**partition_kwargs)
        return elements